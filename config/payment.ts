import env from '#start/env'
import { Environment } from '@paypal/paypal-server-sdk'

export const paymentConfig = {
  applyZurnoPayment: env.get('APPLY_ZURNO_PAYMENT', false),
  paypal: {
    clientId: env.get('PAYPAL_CLIENT_ID'),
    clientSecret: env.get('PAYPAL_CLIENT_SECRET'),
    environment:
      env.get('NODE_ENV') === 'production' ? Environment.Production : Environment.Sandbox,
    returnUrl: env.get('PAYPAL_RETURN_URL'),
    cancelUrl: env.get('PAYPAL_CANCEL_URL'),
    webhookId: env.get('PAYPAL_WEBHOOK_ID'),
    brandName: env.get('PAYPAL_BRAND_NAME'),
  },
  authorizeNet: {
    loginId: env.get('AUTHORIZE_NET_LOGIN_ID'),
    transactionKey: env.get('AUTHORIZE_NET_TRANSACTION_KEY'),
    environment: env.get('AUTHORIZE_NET_ENVIRONMENT'),
    sandboxUrl: 'https://apitest.authorize.net/xml/v1/request.api',
    productionUrl: 'https://api.authorize.net/xml/v1/request.api',
  },
}
