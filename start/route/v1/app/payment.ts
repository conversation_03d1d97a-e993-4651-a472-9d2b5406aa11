import router from '@adonisjs/core/services/router'

const PayPalController = () => import('#controllers/app/payment/paypal_controller')
const AuthorizeNetController = () => import('#controllers/app/payment/authorize_net_controller')
const PaymentController = () => import('#controllers/app/payment/payment_controller')

export default function paymentRoutes() {
  router
    .group(() => {
      // Paypal
      router
        .group(() => {
          router.post('create-order', [PayPalController, 'createOrder'])
          router.post('capture-order', [PayPalController, 'captureOrder'])
          router.post('webhook', [PayPalController, 'webhook'])
        })
        .prefix('paypal')

      // Authorize.net
      router
        .group(() => {
          router.post('create-transaction', [AuthorizeNetController, 'processPayment'])
        })
        .prefix('authorize-net')

      router.post('/', [PaymentController, 'payment'])
    })
    .prefix('payments')
}
