import { paymentConfig } from '#config/payment'
import ZnDraftOrderDetail from '#models/zn_draft_order_detail'
import authorizenet, { APIContracts as ApiContracts } from 'authorizenet'

export class AuthorizeNetService {
  private merchantAuthenticationType: ApiContracts.MerchantAuthenticationType

  constructor() {
    this.merchantAuthenticationType = new ApiContracts.MerchantAuthenticationType()
    this.merchantAuthenticationType.setName(paymentConfig.authorizeNet.loginId || '')
    this.merchantAuthenticationType.setTransactionKey(
      paymentConfig.authorizeNet.transactionKey || ''
    )
  }

  async processPayment(paymentData: {
    amount: number
    tax: number
    shipping: number
    discount: number
    cardNumber: string
    expirationDate: string
    cardCode: string
    firstName: string
    lastName: string
    address: string
    city: string
    state: string
    zip: string
    country: string
    email: string
    code: number
    orderId: string
    details: ZnDraftOrderDetail[]
  }) {
    try {
      const creditCard = new ApiContracts.CreditCardType()
      creditCard.setCardNumber(paymentData.cardNumber)
      creditCard.setExpirationDate(paymentData.expirationDate)
      creditCard.setCardCode(paymentData.cardCode)

      const paymentType = new ApiContracts.PaymentType()
      paymentType.setCreditCard(creditCard)

      const customerAddress = new ApiContracts.CustomerAddressType()
      customerAddress.setFirstName(paymentData.firstName)
      customerAddress.setLastName(paymentData.lastName)
      customerAddress.setAddress(paymentData.address)
      customerAddress.setCity(paymentData.city)
      customerAddress.setState(paymentData.state)
      customerAddress.setZip(paymentData.zip)
      customerAddress.setCountry(paymentData.country)
      customerAddress.setEmail(paymentData.email)

      const order = new ApiContracts.OrderType()
      order.setInvoiceNumber(`ZURNO-${paymentData.code}`)
      order.setDescription(paymentData.orderId)

      // Set discount in order if discount exists
      if (paymentData.discount > 0) {
        order.setDiscountAmount(paymentData.discount)
      }

      const lineItemList = []
      for (const [index, detail] of paymentData.details.entries()) {
        const lineItem = new ApiContracts.LineItemType()
        const id = `${paymentData.code}-I${index + 1}`
        lineItem.setItemId(id)
        lineItem.setName(detail.variantTitle)
        lineItem.setQuantity(detail.quantity)
        lineItem.setUnitPrice(detail.price)
        lineItem.setTaxAmount(detail.tax)
        lineItem.setDiscountAmount(detail.discount)
        lineItem.setProductSKU(detail.sku)
        lineItem.setTotalAmount(detail.amount)
        lineItemList.push(lineItem)
      }

      // Create tax amount
      const tax = new ApiContracts.ExtendedAmountType()
      tax.setAmount(paymentData.tax)
      tax.setName('Tax')

      // Create shipping amount
      const shipping = new ApiContracts.ExtendedAmountType()
      shipping.setAmount(paymentData.shipping)
      shipping.setName('Shipping')

      const transactionRequestType = new ApiContracts.TransactionRequestType()
      transactionRequestType.setTransactionType(
        ApiContracts.TransactionTypeEnum.AUTHCAPTURETRANSACTION
      )
      transactionRequestType.setPayment(paymentType)
      transactionRequestType.setAmount(paymentData.amount)
      transactionRequestType.setTax(tax)
      transactionRequestType.setShipping(shipping)
      transactionRequestType.setBillTo(customerAddress)
      transactionRequestType.setOrder(order)

      const lineItems = new ApiContracts.ArrayOfLineItem()
      lineItems.setLineItem(lineItemList)
      transactionRequestType.setLineItems(lineItems)

      const createRequest = new ApiContracts.CreateTransactionRequest()
      createRequest.setMerchantAuthentication(this.merchantAuthenticationType)
      createRequest.setTransactionRequest(transactionRequestType)

      const ctrl = new authorizenet.APIControllers.CreateTransactionController(
        createRequest.getJSON()
      )

      return new Promise((resolve, reject) => {
        ctrl.execute(() => {
          const apiResponse = ctrl.getResponse()
          const response = new ApiContracts.CreateTransactionResponse(apiResponse)

          if (response.getMessages().getResultCode() === ApiContracts.MessageTypeEnum.OK) {
            const transactionResponse = response.getTransactionResponse()
            resolve({
              success: true,
              transactionId: transactionResponse.getTransId(),
              accountNumber: transactionResponse.getAccountNumber(),
              accountType: transactionResponse.getAccountType(),
              authCode: transactionResponse.getAuthCode(),
              responseCode: transactionResponse.getResponseCode(),
              messageCode: transactionResponse.getMessages()[0]?.getCode(),
              description: transactionResponse.getMessages()[0]?.getDescription(),
              json: response.getJSON(),
            })
          } else {
            const errorMessages = response.getMessages().getMessage()
            reject({
              success: false,
              error: errorMessages[0]?.getText() || 'Transaction failed',
              code: errorMessages[0]?.getCode(),
            })
          }
        })
      })
    } catch (error) {
      throw new Error(`Authorize.net payment failed: ${error.message}`)
    }
  }

  async refundTransaction(payload: {
    transactionId: string
    amount: number
    creditCardNumber: string
    expirationDate: string
  }) {
    try {
      const creditCard = new ApiContracts.CreditCardType()
      creditCard.setCardNumber(payload.creditCardNumber)
      creditCard.setExpirationDate(payload.expirationDate)

      const paymentType = new ApiContracts.PaymentType()
      paymentType.setCreditCard(creditCard)

      const transactionRequestType = new ApiContracts.TransactionRequestType()
      transactionRequestType.setTransactionType(ApiContracts.TransactionTypeEnum.REFUNDTRANSACTION)
      transactionRequestType.setPayment(paymentType)
      transactionRequestType.setAmount(payload.amount)
      transactionRequestType.setRefTransId(payload.transactionId)

      const createRequest = new ApiContracts.CreateTransactionRequest()
      createRequest.setMerchantAuthentication(this.merchantAuthenticationType)
      createRequest.setTransactionRequest(transactionRequestType)

      const ctrl = new authorizenet.APIControllers.CreateTransactionController(
        createRequest.getJSON()
      )

      return new Promise((resolve, reject) => {
        ctrl.execute(() => {
          const apiResponse = ctrl.getResponse()
          const response = new ApiContracts.CreateTransactionResponse(apiResponse)

          if (response.getMessages().getResultCode() === ApiContracts.MessageTypeEnum.OK) {
            const transactionResponse = response.getTransactionResponse()
            resolve({
              success: true,
              transactionId: transactionResponse.getTransId(),
              accountNumber: transactionResponse.getAccountNumber(),
              accountType: transactionResponse.getAccountType(),
              responseCode: transactionResponse.getResponseCode(),
              messageCode: transactionResponse.getMessages()[0]?.getCode(),
              description: transactionResponse.getMessages()[0]?.getDescription(),
              json: response.getJSON(),
            })
          } else {
            const errorMessages = response.getMessages().getMessage()
            reject({
              success: false,
              error: errorMessages[0]?.getText() || 'Refund failed',
              code: errorMessages[0]?.getCode(),
            })
          }
        })
      })
    } catch (error) {
      throw new Error(`Authorize.net refund failed: ${error.message}`)
    }
  }
}
