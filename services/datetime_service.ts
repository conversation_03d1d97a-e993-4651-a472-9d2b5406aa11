import { DateTime } from 'luxon'

/**
 * DateTime service for consistent timezone handling across the application
 * Default timezone is set to US Eastern Time (America/New_York)
 */
export class DateTimeService {
  private static readonly DEFAULT_TIMEZONE = 'America/New_York'

  /**
   * Get current DateTime in US timezone
   */
  static nowUS(): DateTime {
    return DateTime.now().setZone(this.DEFAULT_TIMEZONE)
  }

  /**
   * Convert JS Date to DateTime with US timezone
   */
  static fromJSDateUS(date: Date): DateTime {
    return DateTime.fromJSDate(date).setZone(this.DEFAULT_TIMEZONE)
  }

  /**
   * Convert ISO string to DateTime with US timezone
   */
  static fromISOUS(iso: string): DateTime {
    return DateTime.fromISO(iso).setZone(this.DEFAULT_TIMEZONE)
  }

  /**
   * Convert DateTime to US timezone
   */
  static toUS(dateTime: DateTime): DateTime {
    return dateTime.setZone(this.DEFAULT_TIMEZONE)
  }

  /**
   * Get current DateTime in local timezone (for backward compatibility)
   */
  static nowLocal(): DateTime {
    return DateTime.now()
  }

  /**
   * Convert JS Date to DateTime without timezone conversion (for backward compatibility)
   */
  static fromJSDateLocal(date: Date): DateTime {
    return DateTime.fromJSDate(date)
  }

  /**
   * Extract only the date part from a Date object without timezone conversion
   * This preserves the original date values (year, month, day) regardless of timezone
   */
  static fromDateOnly(date: Date): DateTime {
    const year = date.getFullYear()
    const month = date.getMonth() + 1 // getMonth() returns 0-11, we need 1-12
    const day = date.getDate()

    // Create DateTime with only date components, no time or timezone
    return DateTime.fromObject({ year, month, day }, { zone: this.DEFAULT_TIMEZONE })
  }

  /**
   * Get today's date in US timezone (date only, no time)
   */
  static todayUS(): DateTime {
    return DateTime.now().setZone(this.DEFAULT_TIMEZONE).startOf('day')
  }
}
