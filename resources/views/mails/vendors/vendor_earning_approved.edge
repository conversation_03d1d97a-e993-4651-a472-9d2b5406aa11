@layout.home({serverDomain})
@slot('main')
<h1 style="font-size: 24px; font-weight: 600; color: #1d1c20; margin: 0 0 16px 0;">
  Earnings Approved - Order {{orderName}}
</h1>

<p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
  Dear {{ companyName }},
</p>

<p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
  Your earnings from a recent order have been approved and are ready for payout!
</p>

<p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
  <b>Earnings Details:</b>
</p>

<div style="font-size: 16px; background-color:#fff7e7;font-weight:normal; padding:4px 24px 4px 24px;">
  <ul>
    <li>Order Number: {{orderName}}</li>
    <li>Order Date: {{orderDate}}</li>
    <li>Order Total: {{orderTotal}}</li>
    <li>Your Net Earnings: {{earningAmount}}</li>
  </ul>
</div>

<p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
  <b>Items Ordered:</b>
</p>

<div style="font-size: 16px; background-color:#fff7e7;font-weight:normal; padding:4px 24px 4px 24px;">
  @each(item in orderDetails)
  <p>
    Item Name: {{ item.title }}
    <br />
  <ul>
    <li>SKU: {{ item.sku }}</li>
    <li>Item Price: ${{ item.price.toFixed(2) }}</li>
    <li>Quantity: {{ item.quantity }}</li>
    <li>Subtotal: ${{ (item.price * item.quantity).toFixed(2) }}</li>
  </ul>
  </p>
  @end
</div>

<p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
  <b>Payout Information:</b>
</p>

<div style="font-size: 16px; background-color:#fff7e7;font-weight:normal; padding:4px 24px 4px 24px;">
  <ul>
    <li>These earnings have been added to your pending payout balance.</li>
    <li>Current pending balance: {{ balance }}</li>
    @if(defaultPaymentMethod)
    <li>Default payout method: {{ defaultPaymentMethod }}</li>
    @end
  </ul>
</div>

<p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
  You can view detailed earnings reports and payout history in your <a href="{{dashboardUrl}}">Vendor Dashboard</a>.
</p>

<p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
  If you have any questions about your earnings, please contact our support team at <a
    href="mailto:{{ supportEmail }}">{{
    supportEmail }}</a> or {{ supportPhone }}.
</p>

<p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
  Thank you for your continued partnership with Zurno!
</p>

<p style="font-size: 16px; color: #1d1c20; line-height: 1.5;">
  Best regards,<br />The Zurno Team
</p>
@endslot
@end