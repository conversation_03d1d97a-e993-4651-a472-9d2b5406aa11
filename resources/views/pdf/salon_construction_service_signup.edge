<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Salon Construction Service Sign-Up</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #ffffff;
            color: #333;
            line-height: 1.4;
            font-size: 12px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .logo {
            width: 60px;
            height: 60px;
            margin: 0 auto 15px;
            background-image: url('data:image/png;base64,{{ data.logoBase64 }}');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        .title {
            font-size: 18px;
            font-weight: bold;
            margin: 0 0 8px 0;
        }
        .subtitle {
            font-size: 12px;
            margin: 0 0 15px 0;
        }
        .section {
            margin-bottom: 15px;
        }
        .section-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 8px;
            text-decoration: underline;
        }
        .field-line {
            margin-bottom: 10px;
            display: flex;
            align-items: baseline;
        }
        .field-label {
            width: 120px;
            flex-shrink: 0;
            font-weight: bold;
        }
        .field-value {
            flex: 1;
            border-bottom: 1px solid #333;
            min-height: 16px;
            padding-bottom: 2px;
            margin-left: 10px;
        }
        .checkbox-line {
            margin-bottom: 6px;
        }
        .checkbox {
            display: inline-block;
            width: 12px;
            height: 12px;
            border: 1px solid #333;
            margin-right: 6px;
            vertical-align: middle;
            position: relative;
        }
        .checkbox.checked::after {
            content: '✓';
            position: absolute;
            left: 1px;
            top: -2px;
            font-size: 10px;
            font-weight: bold;
        }
        .notes-area {
            border: 1px solid #333;
            min-height: 60px;
            padding: 8px;
            margin-top: 8px;
        }
        .signature-area {
            margin-top: 20px;
        }
        .signature-line {
            display: inline-block;
            border-bottom: 1px solid #333;
            width: 120px;
            height: 16px;
            margin-left: 8px;
        }
        .consent-text {
            font-size: 11px;
            line-height: 1.3;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo"></div>
        <h1 class="title">Salon Construction Service Sign-Up</h1>
        <p class="subtitle">Modernize your space. Impress your clients. Boost your business.</p>
    </div>

    <div class="section">
        <h2 class="section-title">Salon Owner Information</h2>
        <div class="field-line">
            <span class="field-label">Full Name:</span>
            <span class="field-value">{{ data.fullName || '' }}</span>
        </div>
        <div class="field-line">
            <span class="field-label">Business Name:</span>
            <span class="field-value">{{ data.businessName || '' }}</span>
        </div>
        <div class="field-line">
            <span class="field-label">Salon Address:</span>
            <span class="field-value">{{ data.salonAddress || '' }}</span>
        </div>
        <div class="field-line">
            <span class="field-label">Phone Number:</span>
            <span class="field-value">{{ data.phoneNumber || '' }}</span>
        </div>
        <div class="field-line">
            <span class="field-label">Email Address:</span>
            <span class="field-value">{{ data.emailAddress || '' }}</span>
        </div>
    </div>

    <div class="section">
        <h2 class="section-title">Service Interest (Check all that apply)</h2>
        <div class="checkbox-line">
            <span class="checkbox {{ data.serviceInterest && data.serviceInterest.includes('full_renovation') ? 'checked' : '' }}"></span>
            Full Renovation
        </div>
        <div class="checkbox-line">
            <span class="checkbox {{ data.serviceInterest && data.serviceInterest.includes('partial_remodel') ? 'checked' : '' }}"></span>
            Partial Remodel (e.g. chairs, counters, flooring)
        </div>
        <div class="checkbox-line">
            <span class="checkbox {{ data.serviceInterest && data.serviceInterest.includes('new_construction') ? 'checked' : '' }}"></span>
            New Construction
        </div>
        <div class="checkbox-line">
            <span class="checkbox {{ data.serviceInterest && data.serviceInterest.includes('electrical_plumbing') ? 'checked' : '' }}"></span>
            Electrical / Plumbing Updates
        </div>
        <div class="checkbox-line">
            <span class="checkbox {{ data.serviceInterest && data.serviceInterest.includes('interior_design') ? 'checked' : '' }}"></span>
            Interior Design Services
        </div>
    </div>

    <div class="section">
        <h2 class="section-title">Timeline & Budget</h2>
        <div class="field-line">
            <span class="field-label">Preferred Start Date:</span>
            <span class="field-value">{{ data.preferredStartDate || '' }}</span>
        </div>
        <div style="margin-top: 10px;">
            <span style="font-weight: bold;">Budget Range:</span>
            <div style="margin-left: 15px; margin-top: 6px;">
                <div class="checkbox-line">
                    <span class="checkbox {{ data.budgetRange === '$30-50K' ? 'checked' : '' }}"></span> $30-50K
                </div>
                <div class="checkbox-line">
                    <span class="checkbox {{ data.budgetRange === '$50-100K' ? 'checked' : '' }}"></span> $50-100K
                </div>
                <div class="checkbox-line">
                    <span class="checkbox {{ data.budgetRange === '$100-250K' ? 'checked' : '' }}"></span> $100-250K
                </div>
                <div class="checkbox-line">
                    <span class="checkbox {{ data.budgetRange === '$250-500K' ? 'checked' : '' }}"></span> $250-500K
                </div>
                <div class="checkbox-line">
                    <span class="checkbox {{ data.budgetRange === '$500K+' ? 'checked' : '' }}"></span> $500K+
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2 class="section-title">Additional Notes / Vision</h2>
        <div class="notes-area">
            {{ data.additionalNotes || '' }}
        </div>
    </div>

    <div class="signature-area">
        <h2 class="section-title">Consent & Signature</h2>
        <p class="consent-text">I confirm that I am the salon owner or authorized representative. I understand that completing this form does not obligate me to service but initiates a consultation.</p>
        <div style="margin-top: 15px;">
            <span>Signature:</span>
            <span class="signature-line">{{ data.signature || '' }}</span>
            <span style="margin-left: 30px;">Date:</span>
            <span class="signature-line">{{ data.signatureDate || '' }}</span>
        </div>
    </div>
</body>
</html>
