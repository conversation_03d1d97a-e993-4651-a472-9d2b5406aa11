import ZnOrder from '#models/zn_order';
import { ShopifyService } from '#services/shopify/shopify_service';
import { args, BaseCommand } from '@adonisjs/core/ace';
import type { CommandOptions } from '@adonisjs/core/types/ace';

export default class SyncFulfilSuppliers extends BaseCommand {
  static commandName = 'check:order'
  static description = 'Check if order exists on Shopify'

  static options: CommandOptions = {
    startApp: true,
  }

  @args.string({
    required: true,
    description: "Id of Order"
  })
  declare orderId: string

  async run() {
    const shopifyService = new ShopifyService()

    try {
      const order = await ZnOrder.find(this.orderId)
      if (!order) { return this.logger.error("Databse Order Not Found") }

      console.log('order.shopifyId', order.shopifyId);

      const fulfillments = await shopifyService.fetchFulfillmentOrdersByOrderId(order.shopifyId)

      console.log('fulfillments.length', fulfillments.length);

      for (const fulfillment of fulfillments) {
        console.log(fulfillment.id);
        console.log('fulfillment', fulfillment);
        console.log('fulfillment.lineItems.nodes', fulfillment.lineItems.nodes);
      }


    } catch (error) {
      console.log(error);
    }
  }
}
