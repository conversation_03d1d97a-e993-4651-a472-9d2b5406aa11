// ace-commands/ExtractMsdsLinks.ts
import { BaseCommand } from '@adonisjs/core/ace'
import queue from '@rlanz/bull-queue/services/main'
import path from 'path'
import ExtractMsdsLinksJob from "#jobs/download_msds_job";
import type {CommandOptions} from "@adonisjs/core/types/ace";

export default class ExtractMsdsLinks extends BaseCommand {
  public static commandName = 'download:msds_links'
  public static description = 'Enqueue a job to extract all MSDS PDF links'

  static options: CommandOptions = {
    startApp: true,
  }

  public async run() {
    const pageUrl    = 'https://zurno.com/pages/msds'
    const outputPath = path.join(process.cwd(), 'app','services', 'chatbot', 'documents', 'msds-links.txt')


    this.logger.info(`→ Dispatching MSDS extraction job…`)
    await queue.dispatch(
       ExtractMsdsLinksJob,
      { pageUrl, outputPath },
      { queueName: 'syncData' }
    )

    this.logger.success(`✓ Job enqueued. Results will be in ${outputPath}`)
  }
}
