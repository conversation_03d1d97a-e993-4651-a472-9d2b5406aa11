import { BaseCommand } from '@adonisjs/core/ace'
import queue from '@rlanz/bull-queue/services/main'
import type {CommandOptions} from "@adonisjs/core/types/ace";
import axios from 'axios'
import DownloadArticleJob from "#jobs/download_article_job";
import path from 'path'


export default class DownloadAllArticlesCommand extends BaseCommand {
  public static commandName = 'download:all-articles'
  public static description = 'Enqueue the DownloadAllArticlesJob to fetch and save all blog articles.'

  static options: CommandOptions = {
    startApp: true,
  }

  async run() {
    const blogHandle = 'discover'
    const outputDir = path.join(process.cwd(), 'app','services', 'chatbot', 'documents')
    let page         = 1
    let total        = 0

    // regex to find <a ... href="/blogs/discover/handle" ... class="card">
    const linkRegex = /<a\b[^>]*href="\/blogs\/discover\/([^"]+)"[^>]*class="[^"]*\bcard\b[^"]*"[^>]*>/g

    while (true) {
      const url = `https://zurno.com/blogs/${blogHandle}?page=${page}`
      this.logger.info(`→ Crawling ${url}`)
      const { data: html } = await axios.get<string>(url)

      const handles = new Set<string>()
      let match: RegExpExecArray | null

      // extract all handles
      while ((match = linkRegex.exec(html))) {
        handles.add(match[1])
      }

      if (handles.size === 0) {
        this.logger.info('⟳ no more articles found—stopping crawl.')
        break
      }

      for (const handle of handles) {
        this.logger.info(`• enqueuing download for "${handle}"`)
        await queue.dispatch(
          DownloadArticleJob,
           {
            handle,
            blogHandle,
            outputDir,
          },
          {queueName: 'syncData'}
        )
        total++
      }

      page++
    }
    this.logger.success(`✓ Enqueued download jobs for ${total} articles over ${page - 1} pages`)
  }
}
