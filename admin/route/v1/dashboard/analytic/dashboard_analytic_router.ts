/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import { middleware } from '#start/kernel'
import router from '@adonisjs/core/services/router'

const AdminDashboardAnalyticController = () =>
  import('#adminControllers/dashboard/analytic/dashboard_analytic_controller')

export default function adminDashboardAnalyticRoutes() {
  router
    .group(() => {
      router.get('/top-selling-product', [
        AdminDashboardAnalyticController,
        'getTopSellingProducts',
      ])
      router.get('/most-viewed-product', [
        AdminDashboardAnalyticController,
        'getMostViewedProducts',
      ])
      router.get('/top-favorited-product', [
        AdminDashboardAnalyticController,
        'getTopFavoritedProducts',
      ])
      router.get('/highest-rated-product', [
        AdminDashboardAnalyticController,
        'getHighestRatedProducts',
      ])
      router.get('/most-viewed-post', [AdminDashboardAnalyticController, 'getMostViewedPosts'])
      router.get('/highest-rated-store', [
        AdminDashboardAnalyticController,
        'getHighestRatedStores',
      ])
      router.get('/top-commented-post', [AdminDashboardAnalyticController, 'getTopCommentedPosts'])
      router.get('/most-liked-post', [AdminDashboardAnalyticController, 'getTopLikedPosts'])
    })
    .prefix('dashboard/analytic')
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
