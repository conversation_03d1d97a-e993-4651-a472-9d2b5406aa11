/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import { middleware } from '#start/kernel'
import router from '@adonisjs/core/services/router'

const AdminDashboardEcomerceController = () =>
  import('#adminControllers/dashboard/ecomerce/dashboard_ecomerce_controller')

export default function adminDashboardEcomerceRoutes() {
  router
    .group(() => {
      router.get('/totals', [AdminDashboardEcomerceController, 'getDashboardEcomerceTotals'])

      router.get('/monthly-sale-count', [AdminDashboardEcomerceController, 'getMonthlySaleCounts'])

      router.get('/recent-order', [AdminDashboardEcomerceController, 'getRecentOrders'])

      router.get('/order-count-by-status', [
        AdminDashboardEcomerceController,
        'getOrderCountByFulfillmentStatus',
      ])

      router.get('/top-customers', [AdminDashboardEcomerceController, 'getTopCustomers'])

      router.get('/order-count-by-country', [
        AdminDashboardEcomerceController,
        'getOrderCountByCountry',
      ])

      router.get('/top-selling-product', [
        AdminDashboardEcomerceController,
        'getTopSellingProducts',
      ])

      router.get('/top-purchased', [AdminDashboardEcomerceController, 'getTopPurchasedProducts'])
    })
    .prefix('dashboard/ecomerce')
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
