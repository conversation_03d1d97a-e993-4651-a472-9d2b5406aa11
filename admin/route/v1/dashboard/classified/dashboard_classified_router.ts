/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import { middleware } from '#start/kernel'
import router from '@adonisjs/core/services/router'

const AdminDashboardClassifiedController = () =>
  import('#adminControllers/dashboard/classified/dashboard_classified_controller')

export default function adminDashboardClassifiedRoutes() {
  router
    .group(() => {
      router.get('/total-post-and-store', [
        AdminDashboardClassifiedController,
        'getClassifiedTotalPostsAndStores',
      ])

      router.get('/monthly-post-count', [
        AdminDashboardClassifiedController,
        'getMonthlyPostCounts',
      ])

      router.get('/top-user-by-post', [AdminDashboardClassifiedController, 'getTopUsersByPosts'])

      router.get('/recent-post', [AdminDashboardClassifiedController, 'getRecentPosts'])

      router.get('/post-count-by-category', [
        AdminDashboardClassifiedController,
        'getPostCountByCategory',
      ])

      router.get('/store-count-by-nail-system', [
        AdminDashboardClassifiedController,
        'getStoreCountByNailSystem',
      ])
    })
    .prefix('dashboard/classified')
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
