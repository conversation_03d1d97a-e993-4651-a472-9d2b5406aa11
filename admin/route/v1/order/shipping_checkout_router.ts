/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'

const AdminShippingCheckoutController = () => import('#adminControllers/order/shipping_checkout_controller')

export default function adminShippingCheckoutRoutes() {
  router
    .group(() => {
      router.get('/', [AdminShippingCheckoutController, 'showFulfilOrder'])

      router.post('/report', [AdminShippingCheckoutController, 'report'])

      router.post('/:id/scan', [AdminShippingCheckoutController, 'scanItem'])
      router.post('/:id/bundle', [AdminShippingCheckoutController, 'passBundle'])
      router.put('/:id', [AdminShippingCheckoutController, 'updateFulfilOrder'])

    })
    .prefix('shipping-checkout')
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
