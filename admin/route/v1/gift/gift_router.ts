/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'

const AdminGiftController = () => import('#adminControllers/gift/gift_controller')
const AdminGiftRegisterController = () => import('#adminControllers/gift/gift_register_controller')

export default function adminGiftRoutes() {
  router.get('/gift/register/export', [AdminGiftRegisterController, 'export'])

  router
    .group(() => {
      router.get('/gift/register', [AdminGiftRegisterController, 'getList'])
      router.delete('/gift/register/:id', [AdminGiftRegisterController, 'softDelete'])
      router.get('/gift/register/:id', [AdminGiftRegisterController, 'getOne'])

      router.resource('gift', AdminGiftController)
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
