/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

const AdminAuthController = () => import('#adminControllers/auth/auth_controller')
import router from '@adonisjs/core/services/router'

export default function adminAuthRoutes() {
  router
    .group(() => {
      router.post('/', [AdminAuthController, 'loginAdmin'])
      router.post('/passbyCheckin', [Admin<PERSON>uthController, 'passbyCheckin'])
    })
    .prefix('login')
}
