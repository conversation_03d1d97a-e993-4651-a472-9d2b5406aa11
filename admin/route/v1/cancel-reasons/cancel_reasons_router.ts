/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'
const AdminCancelReasonsController = () =>
  import('#adminControllers/cancel-reasons/cancel_reasons_controller')

export default function adminCancelReasonsRoutes() {
  router
    .group(() => {
      router.resource('/cancel-reasons', AdminCancelReasonsController)
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
