/*
|--------------------------------------------------------------------------
| Admin Event Routes
|--------------------------------------------------------------------------
|
| This file defines the admin routes for managing events
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'

const AdminEventController = () => import('#adminControllers/event/admin_event_controller')
const AdminEventRegistrationController = () => import('#adminControllers/event/admin_event_registration_controller')
const AdminEventRaffleController = () => import('#adminControllers/event/admin_event_raffle_controller')

export default function adminEventRoutes() {
  router
    .group(() => {
      router.get('/', [AdminEventController, 'index'])
      router.get('/select', [AdminEventController, 'select'])
      router.get('/suggest-slug-and-url', [AdminEventController, 'suggestSlugAndUrl'])
      router.get('/:id', [AdminEventController, 'show'])
      router.post('/', [AdminEventController, 'store'])
      router.put('/:id', [AdminEventController, 'update'])
      router.delete('/:id', [AdminEventController, 'destroy'])

      router.post('/:id/register', [AdminEventController, 'register'])
      router.post('/:id/register-and-check-in', [AdminEventController, 'registerAndCheckIn'])
      router.post('/:id/check-in', [AdminEventController, 'checkIn'])
      router.post('/:id/regenerate-qr', [AdminEventController, 'regenerateQRCode'])
      router.post('/find-registration', [AdminEventController, 'findRegistration'])
      router.delete('/registration/:id', [AdminEventController, 'deleteRegistration'])
      router.put('/:id/status', [AdminEventController, 'updateStatus'])
      router.get('/:id/registrations', [AdminEventRegistrationController, 'index'])
      router.get('/:id/export-registrations', [AdminEventRegistrationController, 'exportRegistrations'])

      router.get('/:id/raffles/', [AdminEventRaffleController, 'list'])
      router.get('/:id/raffles/prizes', [AdminEventRaffleController, 'prizes'])
      router.post('/:id/raffles/assign-lucky-numbers', [AdminEventRaffleController, 'assignLuckyNumbers'])
      router.post('/:id/raffles/', [AdminEventRaffleController, 'store'])
      router.patch('/:id/raffles/', [AdminEventRaffleController, 'withdrawReward'])
    })
    .prefix('/events')
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))

  router
    .group(() => {
      router.post('/register-mobile', [AdminEventController, 'registerMobile'])
    })
    .prefix('mobile/events')
}
