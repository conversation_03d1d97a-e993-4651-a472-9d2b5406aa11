/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'

const AdminPostCategoryController = () =>
  import('#adminControllers/post-category/post_category_controller')

export default function adminPostCategoryRoutes() {
  router
    .group(() => {
      router.get('/select', [AdminPostCategoryController, 'select'])
    })
    .prefix('post-category')

  router
    .group(() => {
      router.resource('post-category', AdminPostCategoryController)
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
