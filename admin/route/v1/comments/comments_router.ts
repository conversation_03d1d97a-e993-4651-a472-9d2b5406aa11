/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'
const AdminCommentsController = () => import('#adminControllers/comments/comments_controller')

export default function adminCommentsRoutes() {
  const prefix = 'comments'
  router
    .group(() => {

      router
        .group(() => {
          router.get('/:id/children', [AdminCommentsController, 'children']).where('id', router.matchers.uuid())
          router.put('/:id/react', [AdminCommentsController, 'react']).where('id', router.matchers.uuid())
        }).prefix(prefix)

      router.resource(prefix, AdminCommentsController).apiOnly()
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
