/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'

const AdminCategoryPagesCategoriesController = () =>
  import('#adminControllers/category-pages/categories_controller')
const AdminCategoryPagesTabsController = () =>
  import('#adminControllers/category-pages/tabs_controller')

export default function adminCategoryPagesRoutes() {
  router
    .group(() => {
      router.post('/', [AdminCategoryPagesCategoriesController, 'create'])
      router.get('/', [AdminCategoryPagesCategoriesController, 'list'])
      router.get('/:id', [AdminCategoryPagesCategoriesController, 'show'])
      router.put('/:id', [AdminCategoryPagesCategoriesController, 'update'])
      router.delete('/:id', [AdminCategoryPagesCategoriesController, 'softDelete'])
    })
    .prefix('category-pages/categories')
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))

  router
    .group(() => {
      router.post('/', [AdminCategoryPagesTabsController, 'create'])
      router.get('/', [AdminCategoryPagesTabsController, 'list'])
      router.get('/:id', [AdminCategoryPagesTabsController, 'show'])
      router.put('/:id', [AdminCategoryPagesTabsController, 'update'])
      router.delete('/:id', [AdminCategoryPagesTabsController, 'softDelete'])
    })
    .prefix('category-pages/tabs')
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
