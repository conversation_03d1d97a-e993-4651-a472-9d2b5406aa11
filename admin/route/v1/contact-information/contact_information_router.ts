/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'
const AdminContactInformationController = () =>
  import('#adminControllers/contact-information/contact_information_controller')

export default function adminContactInformationRoutes() {
  router
    .group(() => {
      router.resource('/contact-information', AdminContactInformationController)
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
