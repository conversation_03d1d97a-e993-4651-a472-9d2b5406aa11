/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'
const AdminProductVendorController = () =>
  import('#adminControllers/product-organization/product_vendor_controller')

export default function adminProductVendorRoutes() {
  router
    .group(() => {
      router.get('product-vendor/select', [AdminProductVendorController, 'select'])

      router.resource('product-vendor', AdminProductVendorController)
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
