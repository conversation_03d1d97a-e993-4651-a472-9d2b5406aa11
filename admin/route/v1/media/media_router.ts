/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'
const AdminMediaController = () => import('#adminControllers/media/media_controller')

export default function adminMediaRoutes() {
  router
    .group(() => {
      router.post('/', [AdminMediaController, 'upload'])
    })
    .prefix('media')
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
