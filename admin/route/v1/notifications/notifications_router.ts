/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'

const AdminNotificationsController = () => import('#adminControllers/notifications/notifications_controller')

export default function adminNotificationsRoutes() {
  const prefix = 'notifications'
  router
    .group(() => {
      router.group(() => {
        router.get('/', [AdminNotificationsController, 'index'])
        router.post('/:id/read', [AdminNotificationsController, 'read'])
        router.delete('/:id', [AdminNotificationsController, 'destroy'])
      }).prefix(prefix)

      // router.resource(prefix, AdminNotificationsController).except(['index'])
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
