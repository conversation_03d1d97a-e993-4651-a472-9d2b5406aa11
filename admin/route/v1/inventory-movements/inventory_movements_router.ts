/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'

const AdminInventoryMovementsController = () =>
  import('#adminControllers/inventory-movements/inventory_movements_controller')

export default function adminInventoryMovementsRoutes() {
  router
    .group(() => {
      router.get('/', [AdminInventoryMovementsController, 'index'])
    })
    .prefix('inventory-movements')
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
