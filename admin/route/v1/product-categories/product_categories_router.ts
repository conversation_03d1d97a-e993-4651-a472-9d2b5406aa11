/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import { middleware } from '#start/kernel'
import router from '@adonisjs/core/services/router'
const AdminProductCategoryController = () =>
  import('#adminControllers/product-category/product_category_controller')

export default function adminProductCategoriesRoutes() {
  router
    .group(() => {
      router.get('/', [AdminProductCategoryController, 'index'])
      router.get('/select', [AdminProductCategoryController, 'select'])
      router.post('/', [AdminProductCategoryController, 'store'])
      router.get('/:id', [AdminProductCategoryController, 'show'])
      router.put('/:id', [AdminProductCategoryController, 'update'])
      router.delete('/:id', [AdminProductCategoryController, 'destroy'])
      router.post('/:id/collections', [AdminProductCategoryController, 'attachCollections'])
    })
    .prefix('product-categories')
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
