/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'

const AdminPostController = () => import('#adminControllers/post/post_controller')

export default function adminPostRoutes() {
  router
    .group(() => {
      router.delete('post/locale-translation', [AdminPostController, 'deleteLocaleTranslation'])
      // router.post('post/filter', [AdminPostController, 'filter'])
      router.put('post/thumbnail', [AdminPost<PERSON>ontroller, 'generateThumbnail'])
      router.resource('post', AdminPostController)
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
