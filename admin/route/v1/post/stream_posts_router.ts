/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'

const AdminStreamPostController = () => import('#adminControllers/post/stream_post_controller')

export default function adminStreamPostRoutes() {
  const prefix = 'stream-post'
  router
    .group(() => {
      router.group(() => {
        router.get('/:id/statistics', [AdminStreamPostController, 'statistics'])
        // router.get('/:id/viewer-count', [AdminStreamPostController, 'getViewerCount'])

        // raffle
        router.get('/:id/viewers-full', [AdminStreamPostController, 'getViewersFullList'])
        router.get('/:id/viewers-paginated', [AdminStreamPostController, 'getViewersPaginatedList'])
        router.get('/:id/user-interactions', [AdminStreamPostController, 'syncStreamInteractions'])
        router.post('/:id/raffle-winner', [AdminStreamPostController, 'saveStreamRaffleWinner'])
        router.post('/:id/remove-participant', [AdminStreamPostController, 'removeViewerFromRaffleList'])

        router.post('/:id/metadata', [AdminStreamPostController, 'putMetadata'])
        router.post('/:id/start-broadcast', [AdminStreamPostController, 'startBroadcast'])
        router.post('/:id/end-broadcast', [AdminStreamPostController, 'endBroadcast'])
        router.post('/:id/stream-key', [AdminStreamPostController, 'generateStreamKey'])
        router.post('/:id/notifications', [AdminStreamPostController, 'sendNotifications'])
      }).prefix(prefix)

      router.resource(prefix, AdminStreamPostController).except(['create', 'edit'])
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
