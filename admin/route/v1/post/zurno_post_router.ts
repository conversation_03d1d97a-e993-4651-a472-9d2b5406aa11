/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'

const AdminZurnoPostController = () => import('#adminControllers/post/zurno_post_controller')

export default function adminZurnoPostRoutes() {
  router
    .group(() => {
      router.delete('zurno-post/locale-translation', [
        AdminZurnoPostController,
        'deleteLocaleTranslation',
      ])
      router.put('zurno-post/thumbnail', [Admin<PERSON>urnoPostController, 'generateThumbnail'])
      router.resource('zurno-post', AdminZurnoPostController)
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
