/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'
const AdminCollectionController = () => import('#adminControllers/collection/collection_controller')

export default function adminCollectionRoutes() {
  router
    .group(() => {
      router.get('/collection/select', [AdminCollectionController, 'select'])
      router.get('/collection/:id/productIds', [
        AdminCollectionController,
        'getCollectionAllProductIds',
      ])
      router.post('/collection/select-product', [AdminCollectionController, 'selectProduct'])
      router.post('/collection/list-product', [AdminCollectionController, 'listProduct'])
      router.post('/collection/check-product', [AdminCollectionController, 'checkProduct'])

      router.resource('/collection', AdminCollectionController)
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
