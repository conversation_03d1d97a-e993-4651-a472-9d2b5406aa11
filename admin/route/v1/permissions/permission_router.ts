/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'

const AdminPermissionController = () => import('#adminControllers/permission/permission_controller')

export default function adminPermissionRoutes() {
  router
    .group(() => {
      router.get('/permission/select', [AdminPermissionController, 'select'])
      router.resource('/permission', AdminPermissionController)
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
