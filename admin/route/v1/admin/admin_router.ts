/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import { middleware } from '#start/kernel'
import router from '@adonisjs/core/services/router'
const AdminManagementController = () => import('#adminControllers/management/management_controller')

export default function adminManagementRoutes() {
  router
    .group(() => {
      router.get('/management/select', [AdminManagementController, 'select'])
      router.resource('/management', AdminManagementController)
      router.put('/management/:id/password', [AdminManagementController, 'updatePassword'])
      router.put('/management/:id/superadmin', [AdminManagementController, 'updateSuperAdmin'])
      router.put('/management/:id/activity', [AdminManagementController, 'updateActivity'])
      router.get('/management/:id/permissions', [AdminManagementController, 'showPermissions'])
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
