/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

const AdminCampaignController = () => import('#adminControllers/campaign/campaign_controller')
import { middleware } from '#start/kernel'
import router from '@adonisjs/core/services/router'

export default function adminCampaignRoutes() {
  router
    .group(() => {
      router.post('/campaign/:id/publish', [AdminCampaignController, 'publish'])
      router.get('/campaign/list-user', [AdminCampaignController, 'listUser'])
      router.post('/campaign/filter', [Admin<PERSON><PERSON>aign<PERSON>ontroller, 'filter'])
      router.get('/campaign/select-mail-templates', [
        AdminCampaignController,
        'selectMailTemplates',
      ])

      router.resource('/campaign', AdminCampaignController).apiOnly()
      // router.put('/campaign/:id/active', [AdminCampaignController, 'active'])
      // router.put('/campaign/:id/deactive', [AdminCampaignController, 'deactive'])
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
