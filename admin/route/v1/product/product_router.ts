/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'
const AdminProductController = () => import('#adminControllers/product/product_controller')

export default function adminProductRoutes() {
  router
    .group(() => {
      router.get('/product/select', [AdminProductController, 'select'])
      router.put('/product/:id/sync', [AdminProductController, 'syncShopify'])
      router.put('/product/approval', [AdminProductController, 'productApproval'])

      router.resource('product', AdminProductController).except(['create', 'edit'])
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
