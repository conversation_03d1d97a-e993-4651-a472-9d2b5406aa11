/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'

const AdminDrawerMenusController = () =>
  import('#adminControllers/drawer-menus/drawer_menus_controller')

export default function adminDrawerMenusRoutes() {
  router
    .group(() => {
      router.resource('/drawer-menus', AdminDrawerMenusController)
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
