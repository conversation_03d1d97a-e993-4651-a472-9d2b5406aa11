/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import { middleware } from '#start/kernel'
import router from '@adonisjs/core/services/router'
const AdminMeController = () => import('#adminControllers/me/me_controller')

export default function adminMeRoutes() {
  router
    .group(() => {
      router.get('/', [AdminMeController, 'show'])
      router.post('/', [AdminMeController, 'update'])
      router.post('/password', [AdminMeController, 'updatePassword'])
    })
    .prefix('me')
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
