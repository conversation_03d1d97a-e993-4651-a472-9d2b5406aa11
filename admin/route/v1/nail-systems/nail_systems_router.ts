/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'
const AdminNailSystemsController = () =>
  import('#adminControllers/nail-system/nail_system_controller')

export default function adminNailSystemsRoutes() {
  router
    .group(() => {
      router.get('/nail-systems/select', [AdminNailSystemsController, 'select'])

      router.resource('/nail-systems', AdminNailSystemsController)
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
