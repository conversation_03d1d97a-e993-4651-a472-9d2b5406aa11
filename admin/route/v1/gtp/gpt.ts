import { middleware } from '#start/kernel'
import router from '@adonisjs/core/services/router'
const AdminGPTController = () => import('#adminControllers/gpt/gpt_controller')

export default function adminGPTRoutes() {
  router
    .group(() => {
      router.post('/translation', [AdminGPTController, 'translation'])
    })
    .prefix('gpt')
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
