/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'

const AdminLocationController = () => import('#adminControllers/locations/locations_controller')

export default function adminLocationRoutes() {
  router
    .group(() => {
      router.get('countries', [AdminLocationController, 'selectCountries'])
      router.get('states', [AdminLocationController, 'selectStates'])
      router.get('cities', [AdminLocationController, 'selectCities'])
    })
    .prefix('locations')
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
