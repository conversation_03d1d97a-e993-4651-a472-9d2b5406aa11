/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'
const AdminHomeLayoutBannersController = () =>
  import('#adminControllers/home-layout/banners_controller')
const AdminHomeLayoutProductsCategoriesController = () =>
  import('#adminControllers/home-layout/products_categories_controller')
const AdminHomeLayoutTopCategoriesController = () =>
  import('#adminControllers/home-layout/top_categories_controller')

export default function adminHomeLayoutRoutes() {
  router
    .group(() => {
      router.resource('/banners', AdminHomeLayoutBannersController)
    })
    .prefix('home-layout')
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))

  router
    .group(() => {
      router.post('/', [AdminHomeLayoutProductsCategoriesController, 'create'])
      router.get('/', [AdminHomeLayoutProductsCategoriesController, 'list'])
      router.get('/:id', [AdminHomeLayoutProductsCategoriesController, 'show'])
      router.put('/:id', [AdminHomeLayoutProductsCategoriesController, 'update'])
      router.delete('/:id', [AdminHomeLayoutProductsCategoriesController, 'softDelete'])
    })
    .prefix('home-layout/products-categories')
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))

  router
    .group(() => {
      router.post('/', [AdminHomeLayoutTopCategoriesController, 'create'])
      router.get('/', [AdminHomeLayoutTopCategoriesController, 'list'])
      router.get('/:id', [AdminHomeLayoutTopCategoriesController, 'show'])
      router.put('/:id', [AdminHomeLayoutTopCategoriesController, 'update'])
      router.delete('/:id', [AdminHomeLayoutTopCategoriesController, 'softDelete'])
    })
    .prefix('home-layout/top-categories')
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
