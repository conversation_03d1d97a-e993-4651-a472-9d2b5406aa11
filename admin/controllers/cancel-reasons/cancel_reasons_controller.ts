import ZnCancelReasons from '#models/zn_cancel_reasons'
import { CancelReasonsService } from '#services/firebase/cancel_reason_service'
import { appCreateCancelReasonsValidator } from '#validators/app/cancel-reasons/cancel-reasons'
import { HttpContext } from '@adonisjs/core/http'
import { ACTION, RESOURCE } from '../../../app/constants/authorization.js'

export default class AdminCancelReasonsController {
  private cancelReasonsService = new CancelReasonsService()

  /**
   * @index
   * @tag Admin Cancel-Reasons
   * @summary Read all cancel reasons
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @responseBody 200 - <ZnCancelReasons[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null).paginated() - Read all cancel reasons descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display a list of resource
   */
  async index({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.CANCEL_REASON)

    try {
      const { page = 1, limit = 10, search } = request.qs()

      const query = ZnCancelReasons.query().whereNull('deletedAt')

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder.where('value', 'like', `%${search}%`)
        })
      }

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @create
   * @tag Admin Cancel-Reasons
   * @summary Return data to create
   */
  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {}

  /**
   * @store
   * @tag Admin Cancel-Reasons
   * @summary Create a cancel reason
   * @requestBody <ZnCancelReasons>
   * @responseBody 201 - <ZnCancelReasons>.append("id":"","createdAt":"","updatedAt":"") - Create a cancel reason descriptively
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The value field must be defined","rule":"required","field":"value"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Handle form submission for the create action
   */
  async store({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.CANCEL_REASON)

    const data = request.all()

    const payload = await appCreateCancelReasonsValidator.validate(data)

    try {
      const created = await ZnCancelReasons.create({
        value: payload.value,
      })

      this.cancelReasonsService.write(created)

      return response.created(created)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @show
   * @tag Admin Cancel-Reasons
   * @summary Read a cancel reason
   * @paramPath id - ID of Cancel-Reasons - @type(string) @required
   * @responseBody 200 - <ZnCancelReasons>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read a cancel reason descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Cancel reasons not found"} - Not Found
   */
  /**
   * Show individual record
   */
  async show({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.CANCEL_REASON)

    try {
      const cancelReasons = await this.cancelReasonsIfExist(params, response)
      if (!cancelReasons) {
        return
      }

      return response.ok(cancelReasons)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @edit
   * @tag Admin Cancel-Reasons
   * @summary Return data to update
   */
  /**
   * Edit individual record
   */
  async edit({}: HttpContext) {}

  /**
   * @update
   * @tag Admin Cancel-Reasons
   * @summary Update a cancel reasons
   * @description Update a cancel reasons descriptively
   * @paramPath id - ID of Cancel-Reasons - @type(string) @required
   * @requestBody <ZnCancelReasons>
   * @responseBody 200 - <ZnCancelReasons>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The value field must be defined","rule":"required","field":"value"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Cancel reason not found"} - Not Found
   */
  /**
   * Handle form submission for the edit action
   */
  async update({ bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.CANCEL_REASON)

    const cancelReasons = await this.cancelReasonsIfExist(params, response)
    if (!cancelReasons) {
      return
    }

    const data = request.all()

    const payload = await appCreateCancelReasonsValidator.validate(data)

    try {
      cancelReasons.value = payload.value

      const updated = await cancelReasons.save()

      this.cancelReasonsService.write(updated)

      return response.ok(updated)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @destroy
   * @tag Admin Cancel-Reasons
   * @summary Soft-delete a cancel reason
   * @description Soft-delete a cancel reason descriptively
   * @paramPath id - ID of Cancel-Reasons - @type(string) @required
   * @responseBody 200 - {"message":"Cancel reason soft-deleted successfully"}
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Cancel reason not found"} - Not Found
   */
  /**
   * Delete record
   */
  async destroy({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.CANCEL_REASON)

    try {
      const cancelReasons = await this.cancelReasonsIfExist(params, response)
      if (!cancelReasons) {
        return
      }

      await cancelReasons.softDelete()

      this.cancelReasonsService.delete(cancelReasons)

      return response.ok({ message: 'Cancel reasons soft-deleted successfully' })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  private async cancelReasonsIfExist(
    params: HttpContext['params'],
    response: HttpContext['response']
  ) {
    const cancelReasonsId = params.id

    const cancelReasons = await ZnCancelReasons.query().where('id', cancelReasonsId).first()

    if (!cancelReasons) {
      response.notFound({ message: 'Cancels reason not found' })
    }

    return cancelReasons
  }
}
