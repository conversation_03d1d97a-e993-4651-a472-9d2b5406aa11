import ZnPermission from '#models/zn_permission'
import ZnPermissionGroup from '#models/zn_permission_group'
import ZnPermissionResource from '#models/zn_permission_resource'
import type { HttpContext } from '@adonisjs/core/http'

export default class AdminPermissionController {
  /**
   * @select
   * @tag Admin Permission
   */
  /**
   * Edit individual record
   */
  public async select({ request, response }: HttpContext) {
    const { page = 1, search, filter } = request.qs()

    try {
      const query = ZnPermissionResource.query()
        .preload('permissions', (permissionQuery) => {
          permissionQuery.orderBy('orderBy', 'asc')
        })
        .whereNull('groupId')

      if (search) {
        query.whereRaw('LOWER(`key`) LIKE LOWER(?)', [`%${search}%`])
      }

      if (filter) {
        query.whereNotIn('id', filter)
      }

      const result = await query.paginate(page, 100)

      return response.ok(result)
    } catch (error) {
      return response.internalServerError(error)
    }
  }

  /**
   * @index
   * @tag Admin Permission
   */
  /**
   * Edit individual record
   */
  async index({ bouncer, response }: HttpContext) {
    await bouncer.authorize('allow', null, null)

    try {
      const groups = await ZnPermissionGroup.query()
        .preload('resources', (resourceQuery) => {
          resourceQuery.orderBy('orderBy', 'asc')
          resourceQuery.preload('permissions', (permissionQuery) => {
            permissionQuery.orderBy('orderBy', 'asc')
          })
        })
        .withCount('resources')
        .orderBy('orderBy', 'asc')

      const ungroupedResources = await ZnPermissionResource.query()
        .preload('permissions')
        .whereNull('groupId')
        .orderBy('orderBy', 'asc')

      return response.ok([
        ...groups,
        {
          name: 'Others',
          resources: ungroupedResources,
        },
      ])
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @show
   * @tag Admin Permission
   */
  /**
   * Edit individual record
   */
  async show({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', null, null)

    const groupId = params.id

    try {
      const groups = await ZnPermissionGroup.query()
        .where('id', groupId)
        .preload('resources', (resourceQuery) => {
          resourceQuery.orderBy('orderBy', 'asc')
          resourceQuery.preload('permissions', (permissionQuery) => {
            permissionQuery.orderBy('orderBy', 'asc')
          })
        })
        .orderBy('orderBy', 'asc')
        .first()

      // const ungroupedResources = await ZnPermissionResource.query()
      //   .preload('permissions')
      //   .whereNull('groupId')
      //   .orderBy('orderBy', 'asc')

      return response.ok(groups)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @store
   * @tag Admin Permission
   */
  /**
   * Edit individual record
   */
  async store({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', null, null)

    const data = request.all()

    if (!data.name) {
      return response.badRequest({ message: 'Name required' })
    }

    try {
      const group = await ZnPermissionGroup.create({
        name: data.name,
        orderBy: data.orderBy,
      })

      data.resources?.map(async (resource: ZnPermissionResource) => {
        const resour = await ZnPermissionResource.find(resource.id)
        if (resour) {
          resour.name = resource.name
          resour.orderBy = resource.orderBy
          resour.groupId = group.id
          await resour.save()

          resource.permissions.map(async (permission) => {
            const permis = await ZnPermission.find(permission.id)
            if (permis) {
              permis.name = permission.name
              await permis.save()
            }
          })
        }
      })

      return response.ok(group)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @update
   * @tag Admin Permission
   * @summary Update permission
   * @description Update permission descriptively
   * @paramPath id - ID of Permission - @type(string) @required
   * @requestBody <ZnPermission>
   * @responseBody 200 - <ZnPermission>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The name field must be defined","rule":"required","field":"name"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Permission not found"} - Not Found
   */
  /**
   * Handle form submission for the edit action
   */
  async update({ params, request, response }: HttpContext) {
    const groupId = params.id

    const data = request.all()

    if (!data.name) {
      return response.badRequest({ message: 'Name required' })
    }

    try {
      const group = await ZnPermissionGroup.find(groupId)
      if (!group) {
        return response.badRequest({ message: 'Group not found' })
      }

      group.name = data.name
      group.orderBy = data.orderBy
      await group.save()

      data.resources.map(async (resource: ZnPermissionResource) => {
        const resour = await ZnPermissionResource.find(resource.id)
        if (resour) {
          resour.name = resource.name
          resour.orderBy = resource.orderBy
          resour.groupId = group.id
          await resour.save()

          resource.permissions.map(async (permission) => {
            const permis = await ZnPermission.find(permission.id)
            if (permis) {
              permis.name = permission.name
              await permis.save()
            }
          })
        }
      })

      const ungrouped = await group
        .related('resources')
        .query()
        .whereNotIn(
          'id',
          data.resources.map((resour: ZnPermissionResource) => resour.id)
        )

      ungrouped.map(async (resource) => {
        resource.groupId = null
        await resource.save()
      })
    } catch (error) {
      console.log(error)

      return response.internalServerError({
        message: 'Something went wrong',
        error: error,
      })
    }
  }

  /**
   * @destroy
   * @tag Admin Permission
   */
  /**
   * Edit individual record
   */
  async destroy({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', null, null)

    const groupId = params.id

    try {
      const group = await ZnPermissionGroup.find(groupId)

      if (!group) {
        return response.badRequest({ message: 'Group not found' })
      }

      await group.delete()

      return response.ok({})
    } catch (error) {
      return response.badRequest(error)
    }
  }
}
