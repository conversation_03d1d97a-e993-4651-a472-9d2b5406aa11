import type { HttpContext } from '@adonisjs/core/http'
import { BarcodeReportService } from '../../../services/report/barcode_report_service.js'
import ZnProductVariant from '#models/zn_product_variant'
import { uploadReports } from '../../../../services/media/index.js'
import { ACTION, RESOURCE } from '#constants/authorization'

export default class AdminInventoryReportController {
  private barcodeReportService: BarcodeReportService

  constructor() {
    this.barcodeReportService = new BarcodeReportService()
  }

  /**
   * @getBarcodeReport
   * @tag Admin Barcode Report
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 20) - @type(number)
   * @paramQuery search - Search Term - @type(search)
  /**
   * Display a list of resource
   */
  async getBarcodeReport({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.BARCODE_REPORT)

    const {
      page = 1,
      limit = 20,
      search,
      hasShopifyBarcode,
      hasFulfilBarcode,
      warehouseIds,
    } = request.qs()

    try {
      const hasShopifyBarcodeBoolean = JSON.parse(hasShopifyBarcode.toLowerCase())
      const hasFulfilBarcodeBoolean = JSON.parse(hasFulfilBarcode.toLowerCase())

      const report = await this.barcodeReportService.getPaginatedBarcodeReport(
        {
          search,
          hasShopifyBarcode: hasShopifyBarcodeBoolean,
          hasFulfilBarcode: hasFulfilBarcodeBoolean,
          warehouseIds,
        },
        { page, limit }
      )

      return response.ok(report)

    } catch (error) {
      return response.internalServerError({
        message: "Something went wrong!",
        error
      })
    }
  }

  /**
   * @exportBarcodeReport
   * @tag Admin Barcode Report
   * @paramQuery search - Search Term - @type(search)
  /**
   * Expoer Barcode Report
   */
  async exportBarcodeReport({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.EXPORT, RESOURCE.BARCODE_REPORT)

    try {
      const {
        search,
        hasShopifyBarcode,
        hasFulfilBarcode,
        warehouseIds,
      } = request.qs()

      const hasShopifyBarcodeBoolean = JSON.parse(hasShopifyBarcode.toLowerCase())
      const hasFulfilBarcodeBoolean = JSON.parse(hasFulfilBarcode.toLowerCase())

      const report = await this.barcodeReportService.getAllBarcodeReport({
        search,
        hasShopifyBarcode: hasShopifyBarcodeBoolean,
        hasFulfilBarcode: hasFulfilBarcodeBoolean,
        warehouseIds,
      })

      const workbook = await this.barcodeReportService.createBarcodeReportWorkbook(report)

      const buffer = (await workbook.xlsx.writeBuffer()) as unknown as Buffer

      const reports = await uploadReports([{ buffer: buffer }])

      return response.ok({
        filename: `Barcode Report.xlsx`,
        url: reports[0].url,
      })

    } catch (error) {
      return response.internalServerError({
        message: "Something went wrong!",
        error
      })
    }
  }

  /**
   * @getProductVariantFromBarcode
   * @tag Admin Barcode Report
   * @paramQuery barcode - Barcode - @type(string)
  /**
   * Display a list of resource
   */
  async getProductVariantFromBarcode({ request, response }: HttpContext) {

    const { barcode } = request.qs()

    try {
      if (!barcode) {
        return response.badRequest('Barcode requires')
      }

      const variant = await ZnProductVariant.query()
        .where({ barcode })
        .preload('product')
        .preload('image')
        .first()

      if (!variant) {
        return response.notFound('Product Not Found')
      }

      return response.ok(variant)

    } catch (error) {
      return response.internalServerError({
        message: "Something went wrong!",
        error
      })
    }
  }
}
