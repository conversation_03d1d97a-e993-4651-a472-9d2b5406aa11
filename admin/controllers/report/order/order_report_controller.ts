import { OrderReportService } from '../../../services/report/order_report_service.js'
import type { HttpContext } from '@adonisjs/core/http'
import ExcelJS from 'exceljs'
import moment from 'moment'
import { ACTION, RESOURCE } from '../../../../app/constants/authorization.js'
import { uploadReports } from '../../../../services/media/index.js'

export default class AdminReportOrderController {
  private orderReportService: OrderReportService

  constructor() {
    this.orderReportService = new OrderReportService()
  }

  /**
   * @getOrderReport
   * @tag Admin Order Report
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 20) - @type(number)
   * @paramQuery orderDateStart - Start Date - @type(date) @example(2024-10-31)
   * @paramQuery orderDateEnd - End Date - @type(date) @example(2024-12-25)
  /**
   * Display a list of resource
   */
  async getOrderReport({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.ORDER_REPORT)

    const { page = 1, limit = 20, interval, startDate, endDate } = request.qs()

    try {
      const orderReport = await this.orderReportService.fetchOrderReport(
        {
          interval,
          startDate,
          endDate,
        },
        { page, limit }
      )

      const totalRows = orderReport[0]?.totalRows || 0

      const result = {
        meta: {
          total: totalRows,
          lastPage: Math.ceil(totalRows / limit),
        },
        data: orderReport,
      }

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }
  /**
   * @exportOrderReport
   * @tag Admin Order Report
  /**
   * Export Order Report
   */
  async exportOrderReport({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.EXPORT, RESOURCE.ORDER_REPORT)

    const { interval, startDate, endDate } = request.qs()

    const orderReport = await this.orderReportService.fetchOrderReport({
      interval,
      startDate,
      endDate,
    })

    try {
      const workbook = new ExcelJS.Workbook()
      const worksheet = workbook.addWorksheet('Order Report')

      worksheet.addRow(['Interval', 'Orders', 'Average Units Orders', 'Average Order Value'])

      for (const record of orderReport) {
        let orderInterval = record.orderInterval
        if (interval == 'hour_of_day') {
          orderInterval = moment(orderInterval, ['HH']).format('h a')
        }

        worksheet.addRow([
          orderInterval,
          record.totalOrders,
          parseFloat(record.avgOrderedUnits).toFixed(2),
          parseFloat(record.avgTotalPrice).toFixed(2),
        ])
      }

      const buffer = (await workbook.xlsx.writeBuffer()) as unknown as Buffer

      const reports = await uploadReports([{ buffer: buffer }])

      const startDateString = startDate ? ` from ${startDate}` : ''
      const endDateString = endDate ? ` to ${endDate} ` : ''

      const filename = 'Order Report' + startDateString + endDateString + '.xlsx'

      return response.ok({
        filename,
        url: reports[0].url,
      })
    } catch (error) {
      return response.badRequest(error)
    }
  }
}
