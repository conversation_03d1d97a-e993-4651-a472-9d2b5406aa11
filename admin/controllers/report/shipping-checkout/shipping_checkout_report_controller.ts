import { SHIPPING_CHECKOUT_TEXT } from '#adminControllers/order/shipping_checkout_constants'
import ZnProductVariant from '#models/zn_product_variant'
import ZnShippingCheckout, { EShippingCheckoutStatus } from '#models/zn_shipping_checkout'
import type { HttpContext } from '@adonisjs/core/http'
import moment from 'moment'
import { uploadReports } from '../../../../services/media/index.js'
import { ShippingCheckoutService } from '../../../services/order/shipping_checkout_service.js'
import { ACTION, RESOURCE } from '#constants/authorization'
import ZnAdmin from '#models/zn_admin'

export default class AdminReportShippingCheckoutController {
  private shippingCheckoutService = new ShippingCheckoutService()

  constructor() { }

  /**
   * @getShippingCheckoutReport
   * @tag Admin Shipping Checkout Report
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 20) - @type(number)
   * @paramQuery search - Search Term - @type(string)
   * @paramQuery startDate - Start Date - @type(date) @example(2024-10-31)
   * @paramQuery endDate - End Date - @type(date) @example(2024-12-25)
  /**
   * Display a list of resource
   */
  async getShippingCheckoutReport({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.SHIPPING_CHECKOUT_REPORT)

    try {
      const {
        page = 1,
        limit = 20,
        fulfilNumber,
        skuBarcode,
        startDate,
        endDate,
        checkerIds,
      } = request.qs()

      const query = ZnShippingCheckout.query()
        .whereNot('status', EShippingCheckoutStatus.SHOW)
        .preload('admin')
        .preload('items', (itemQuery) => {
          itemQuery
            .preload('variant')
            .orderBy('createdAt', 'desc')
        })
        .orderBy('createdAt', 'desc')

      if (fulfilNumber) {
        query.where('fulfilNumber', fulfilNumber)
      }

      if (skuBarcode) {
        query.whereHas('items', (itemQuery) => {
          itemQuery
            .where('sku', skuBarcode)
            .orWhere('barcode', skuBarcode)
        })
      }

      if (startDate) {
        query.where('createdAt', '>=', startDate)
      }

      if (endDate) {
        query.where('createdAt', '<=', moment(endDate).endOf('day').toISOString())
      }

      if (Array.isArray(checkerIds) && checkerIds.length > 0) {
        query.whereIn('adminId', checkerIds)
      }

      const result = await query.paginate(page, limit)

      return response.ok(result)

    } catch (error) {
      console.log(error);
      return response.internalServerError({
        message: "Something went wrong",
        error
      })
    }
  }

  async getShippingAdmins({ request, response }: HttpContext) {
    try {
      const {
        page = 1,
        limit = 10
      } = request.all()

      const shippingCheckoutAdmins = await ZnShippingCheckout.query()
        .distinct('adminId')

      const shippingAdminIds = shippingCheckoutAdmins.map(checkout => checkout.adminId)

      const query = ZnAdmin.query()
        .whereIn('id', shippingAdminIds)

      const result = await query.paginate(page, limit)

      return response.ok(result)

    } catch (error) {
      console.log(error);
      return response.internalServerError('Something went wrong!')
    }
  }

  /**
   * @showFulfilOrder
   * @tag Admin Shipping Checkout Report
   * @summary Show order from Fulfil
   * @paramQuery fulfilNumber - Fulfil number - @type(string) @required
   * @responseBody 200 - {"data":[{"id":"","count":0,"quantity":1,"barcode":"","sku":"","variantId":"","variant":"<ZnProductVariant>"}]} - Ok
   */
  /**
   * Show individual record
   */
  async showFulfilOrder({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.SHIPPING_CHECKOUT_REPORT)

    try {
      const { fulfilNumber } = request.qs()

      if (!fulfilNumber) {
        return response.badRequest(SHIPPING_CHECKOUT_TEXT.SHIPPING_CHECKOUT_NUMBER_MISSING)
      }

      const data = await this.shippingCheckoutService.getOrderLinesByFulfilNumber(fulfilNumber)

      if (!data?.[0]) {
        return response.notFound(SHIPPING_CHECKOUT_TEXT.SHIPPING_CHECKOUT_ORDER_NOT_FOUND)
      }

      const variantSkus = data.map(datum => datum.sku)
      const variants = await ZnProductVariant.query()
        .whereIn('sku', variantSkus)
        .preload('image')
        .preload('product')
        .preload('optionValues', (optionValueQuery) => {
          optionValueQuery.preload('option')
        })

      const orderLines = data.map(datum => {
        const variant = variants.find(vari => vari.sku == datum.sku)?.serialize()

        // const barcode = datum.codes.find((code: any) => code.type == 'upc')?.code
        return {
          id: datum.id,
          count: 0,
          quantity: datum.quantity,
          barcode: datum.barcode,
          sku: datum.sku,
          productName: datum.productName,
          variantName: datum.variantName,
          variantId: variant?.id,
          variant,
        }
      })


      return response.ok(orderLines)

    } catch (error) {
      console.log(error);
      return response.internalServerError({
        message: SHIPPING_CHECKOUT_TEXT.SHIPPING_CHECKOUT_ERROR,
        error
      })
    }
  }

  /**
   * @exportCheckoutErrorReport
   * @tag Admin Shipping Checkout Report
   * @summary Report Checkout Error
   * @paramQuery startDate - Start Date - @type(date) @example(2024-10-31)
   * @paramQuery startDate - Start Date - @type(date) @example(2024-10-31)
   * @paramQuery endDate - End Date - @type(date) @example(2024-12-25)
   * @responseBody 200 - {"filename":"","url":""} - Ok
   */
  /**
   * Show individual record
   */
  async exportCheckoutErrorReport({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.EXPORT, RESOURCE.SHIPPING_CHECKOUT_REPORT)

    try {
      const {
        reportType,
        fulfilNumber,
        startDate,
        endDate,
        checkerIds,
      } = request.qs()

      const params = {
        fulfilNumber,
        startDate,
        endDate,
        checkerIds,
      }

      let workbook, filename
      switch (reportType) {
        case 'errors': {
          workbook = await this.shippingCheckoutService.createCheckoutErrorsReportWorkbook(params)

          filename = `Checkout Errors Report.xlsx`

          break
        }

        case 'fixes': {
          workbook = await this.shippingCheckoutService.createCheckoutFixesReportWorkbook(params)

          filename = `Checkout Fixes Report.xlsx`

          break
        }

        case "dailies": {
          workbook = await this.shippingCheckoutService.createCheckoutDailiesReportWorkbook(params)

          filename = `Checkout Dailies Report.xlsx`

          break
        }

        case "personals": {
          workbook = await this.shippingCheckoutService.createCheckoutPersonalsReportWorkbook(params)

          filename = `Checkout Dailies Report.xlsx`

          break
        }
      }

      if (!workbook || !filename) { return response.internalServerError("Something went wrong!") }

      const buffer = (await workbook.xlsx.writeBuffer()) as unknown as Buffer

      const reports = await uploadReports([{ buffer: buffer }])

      return response.ok({
        filename,
        url: reports[0].url,
      })

      // await workbook.xlsx.writeFile('report.xlsx')

    } catch (error) {
      console.log(error);
      return response.internalServerError({
        message: SHIPPING_CHECKOUT_TEXT.SHIPPING_CHECKOUT_ERROR,
        error
      })
    }
  }

}
