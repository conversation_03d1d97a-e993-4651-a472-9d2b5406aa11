import WeeklyTrackingReportNotification from '#mails/report/weekly_tracking_report_notification'
import ZnUser from '#models/zn_user'
import ReportService from '#services/report_service'
import env from '#start/env'
import { HttpContext } from '@adonisjs/core/http'
import mail from '@adonisjs/mail/services/main'
import { DateTime } from 'luxon'

export default class AdminReportController {
  async sendReport({ request }: HttpContext) {
    const { email } = request.all()

    try {
      const user = await this.getUserByEmail(email)
      if (!user) return

      const { startDate, endDate } = this.getReportDateRange()
      const report = await this.generateReport(user.id, startDate, endDate)

      const finalCategories = this.prepareCategoryData(report.categories)

      const viewChartUrl = this.generateChartUrl(finalCategories, 'Total View', 'totalView')
      const callChartUrl = this.generateChartUrl(finalCategories, 'Total Call', 'totalCall')
      const chartUrl = this.generateStoreChartUrl(report)

      await this.sendReportEmail(user, report, chartUrl, viewChartUrl, callChartUrl)

      console.log(`Sent report email to user: ${user.email}`)
    } catch (error) {
      console.error(`Error sending report email for user ${email}:`, error.message)
    }
  }

  private async getUserByEmail(email: string) {
    const user = await ZnUser.query().where('email', email).first()
    if (!user) {
      console.error(`User with email ${email} not found`)
    }
    return user
  }

  private getReportDateRange() {
    const endDate = DateTime.now().minus({ days: 1 }).endOf('day')
    const startDate = endDate.minus({ days: 6 }).startOf('day')
    return { startDate, endDate }
  }

  private async generateReport(userId: string, startDate: DateTime, endDate: DateTime) {
    const reportService = new ReportService(startDate, endDate, userId)
    return reportService.getDetailedReport()
  }

  private prepareCategoryData(categories: any[]) {
    const maxCategories = 4
    const sortedCategories = categories.sort((a, b) => b.totalView - a.totalView)
    const topCategories = sortedCategories.slice(0, maxCategories)
    const otherCategories = sortedCategories.slice(maxCategories)

    const otherCategory = {
      categoryName: 'Other',
      totalView: otherCategories.reduce((sum, cat) => sum + cat.totalView, 0),
      totalCall: otherCategories.reduce((sum, cat) => sum + cat.totalCall, 0),
    }

    return otherCategories.length > 0 ? [...topCategories, otherCategory] : topCategories
  }

  private generateChartUrl(categories: any[], label: string, dataKey: string) {
    const categoryData = categories.map((category) => ({
      label: category.categoryName,
      data: category[dataKey],
    }))

    const chartData = {
      labels: categoryData.map((category) => category.label),
      datasets: [
        {
          label,
          data: categoryData.map((category) => category.data),
          backgroundColor: ['#f87171', '#34d399', '#7dd3fc', '#fbbf24', '#a78bfa'],
        },
      ],
    }

    const chartConfig = {
      type: 'doughnut',
      data: chartData,
      options: {
        plugins: {
          doughnutlabel: {
            labels: [
              {
                text: categoryData.reduce((sum, cat) => sum + cat.data, 0).toString(),
                font: { size: 20 },
              },
              {
                text: label,
                font: { size: 16 },
              },
            ],
          },
        },
        responsive: true,
        maintainAspectRatio: false,
      },
    }

    return `https://quickchart.io/chart?c=${encodeURIComponent(JSON.stringify(chartConfig))}`
  }

  private generateStoreChartUrl(report: any) {
    const maxStores = 5
    const chartData = {
      labels: report.stores
        .slice(0, maxStores)
        .map((store: any) =>
          store.storeName.length > 5 ? `${store.storeName.slice(0, 7)}...` : store.storeName
        ),
      datasets: [
        {
          label: 'Call',
          data: report.stores.slice(0, maxStores).map((store: any) => store.totalCall),
          backgroundColor: '#7dd3fc',
        },
        {
          label: 'View',
          data: report.stores.slice(0, maxStores).map((store: any) => store.totalView),
          backgroundColor: '#f87171',
        },
      ],
    }

    const chartConfig = {
      type: 'bar',
      data: chartData,
      options: {
        plugins: {
          legend: {
            position: 'bottom',
          },
        },
        scales: {
          x: { stacked: true },
          y: { stacked: true, beginAtZero: true },
        },
        responsive: true,
        maintainAspectRatio: false,
      },
    }

    return `https://quickchart.io/chart?c=${encodeURIComponent(JSON.stringify(chartConfig))}`
  }

  private async sendReportEmail(
    user: any,
    report: any,
    chartUrl: string,
    viewChartUrl: string,
    callChartUrl: string
  ) {
    const serverDomain = env.get('BASE_URL') || ''
    await mail.sendLater(
      new WeeklyTrackingReportNotification(
        user.email,
        report,
        serverDomain,
        chartUrl,
        viewChartUrl,
        callChartUrl,
        `${user.firstName} ${user.lastName}`
      )
    )
  }
}
