import { SaleReportService } from '../../../services/report/sale_report_service.js'
import type { HttpContext } from '@adonisjs/core/http'
import ExcelJS from 'exceljs'
import { ACTION, RESOURCE } from '../../../../app/constants/authorization.js'
import { uploadReports } from '../../../../services/media/index.js'

export default class AdminReportSaleController {
  private saleReportService: SaleReportService

  constructor() {
    this.saleReportService = new SaleReportService()
  }

  /**
   * @getSaleReport
   * @tag Admin Sale Report
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 20) - @type(number)
   * @paramQuery startDate - Start Date - @type(date) @example(2024-10-31)
   * @paramQuery endDate - End Date - @type(date) @example(2024-12-25)
   * @paramQuery sku - SKU - @type(string)
  /**
   * Display a list of resource
   */
  async getSaleReport({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.SALE_REPORT)

    const { page = 1, limit = 20, startDate, endDate, sku } = request.qs()

    try {
      const saleReport = await this.saleReportService.fetchSaleReport(
        {
          startDate,
          endDate,
          sku,
        },
        { page, limit }
      )

      const totalRows = saleReport[0]?.totalRows || 0

      const result = {
        meta: {
          total: totalRows,
          lastPage: Math.ceil(totalRows / limit),
        },
        data: saleReport,
      }

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }
  /**
   * @exportSaleReport
   * @tag Admin Sale Report
   * @paramQuery startDate - Start Date - @type(date) @example(2024-10-31)
   * @paramQuery endDate - End Date - @type(date) @example(2024-12-25)
   * @paramQuery sku - SKU - @type(string)
  /**
   * Export Sale Report
   */
  async exportSaleReport({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.EXPORT, RESOURCE.SALE_REPORT)

    const { startDate, endDate, sku } = request.qs()

    const saleReport = await this.saleReportService.fetchSaleReport({
      startDate,
      endDate,
      sku,
    })

    try {
      const workbook = new ExcelJS.Workbook()
      const worksheet = workbook.addWorksheet('Sale Report')

      worksheet.addRow([
        'Sale Date',
        'Title',
        'Vendor',
        'Product Type',
        'Sku',
        'Sale Price',
        'Sale Quantity',
        'Sale Amount',
      ])

      for (const record of saleReport) {
        worksheet.addRow([
          record.saleDate,
          record.title,
          record.vendor,
          record.productType,
          record.sku,
          record.salePrice,
          record.saleQuantity,
          record.saleAmount,
        ])
      }

      const buffer = (await workbook.xlsx.writeBuffer()) as unknown as Buffer

      const reports = await uploadReports([{ buffer: buffer }])

      const skuString = sku ? ` of ${sku}` : ''
      const startDateString = startDate ? ` from ${startDate}` : ''
      const endDateString = endDate ? ` to ${endDate} ` : ''

      return response.ok({
        filename: `Sale Report${skuString}${startDateString}${endDateString}.xlsx`,
        url: reports[0].url,
      })
    } catch (error) {
      return response.badRequest(error)
    }
  }
}
