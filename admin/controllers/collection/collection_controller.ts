import ZnCollection from '#models/zn_collection'
import ZnProduct from '#models/zn_product'
import { HttpContext } from '@adonisjs/core/http'
import { ACTION, RESOURCE } from '../../../app/constants/authorization.js'
import { uploadMediasFromLinks } from '../../../services/media/index.js'
import { createCollectionValidator } from '../../validators/collection/collection_validator.js'
import ZnMedia from '#models/zn_media'
import { MEDIA_TYPE } from '#constants/media'

export default class AdminCollectionController {
  /**
   * @index
   * @tag Admin Collection
   * @summary Read all collections
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @responseBody 200 - <ZnCollection[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null,"price":"200","isDraft":"0","isFavourite":"1","expired":"0","categories":["ZnCategory"]).with(thumbnail, medias, user).paginated() - Read all collections descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display a list of resource
   */
  async index({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.COLLECTION)

    try {
      const { page = 1, limit = 10, search } = request.qs()

      const query = ZnCollection.query()
        .whereNull('deletedAt')
        // .preload('products')
        .preload('image')
        .withCount('products', (query) => {
          query.whereNull('deletedAt')
        })

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder.whereRaw('LOWER(title) LIKE LOWER(?)', [`%${search}%`])
        })
      }

      query.orderBy('updatedAt', 'desc')

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @select
   * @tag Admin Collection
   * @summary Read collection selection
   * @paramQuery search - Search term - @type(string)
   * @responseBody 200 - <ZnCollection[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read collection selection descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Show individual record
   */
  public async select({ request, response }: HttpContext) {
    const { page = 1, search, filter, excludeIds = [] } = request.qs()

    try {
      const query = ZnCollection.query()
        .preload('image')
        .whereNull('deletedAt')
        .where('status', true)

      if (search) {
        query.whereRaw('LOWER(title) LIKE LOWER(?)', [`%${search}%`])
      }

      if (filter) {
        query.whereNotIn('id', filter)
      }

      if (excludeIds.length > 0) {
        query.whereNotIn('id', excludeIds)
      }

      const result = await query.paginate(page, 5)

      return response.ok(result)
    } catch (error) {
      return response.internalServerError(error)
    }
  }

  /**
   * @create
   * @tag Admin Collection
   * @summary Return info for creation
   * @responseBody 200 - {"info":{}} - Return info for creation
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display form to create a new record
   */
  async create({ bouncer, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.COLLECTION)

    try {
      return response.ok({
        info: {},
      })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @store
   * @tag Admin Collection
   * @summary Create action
   * @requestBody <ZnCollection>
   * @responseBody 201 - <ZnCollection>.append("id":""") - Create action descriptively
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The title field must be defined","rule":"required","field":"title"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Handle form submission for the create action
   */
  async store({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.COLLECTION)

    // @ts-ignore
    const data = request.all()

    const payload = await createCollectionValidator.validate(data)

    try {
      // get image from db to save both imageId and imageUrl
      let image
      if (payload.imageId) {
        image = await ZnMedia.find(payload.imageId)
      }

      const created = await ZnCollection.create({
        title: payload.title,
        description: payload.description,
        imageId: image?.id,
        imageUrl: image?.url,
        imageAltText: payload.imageAltText,
        status: payload.status,
      })

      if (payload.addedProductIds) {
        await created.related('products').sync(payload.addedProductIds)
      }

      await created.save()

      return response.created(created)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @show
   * @tag Admin Collection
   * @summary Read a collection
   * @paramPath id - ID of Collection - @type(string) @required
   * @responseBody 200 - <ZnCollection>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null,"mine":false) - Read a collection descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Collection not found"} - Not Found
   */
  /**
   * Show individual record
   */
  async show({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.COLLECTION)

    try {
      const collectionId = params.id

      const collection = await ZnCollection.query()
        // .preload('products')
        .preload('image')
        .where('id', collectionId)
        .first()

      return response.ok({
        ...collection?.serialize(),
      })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @edit
   * @tag Admin Collection
   * @summary Return info for updating
   * @responseBody 200 - {"options":{"users":["ZnUser"],"collectionCategories":["ZnCollectionCategory"],"stores":["ZnStore"],"countries":["ZnCountry"],"states":["ZnState"],"cities":["ZnCity"]},"collection":"<ZnCollection>"} - Return info for creation (May crash the render for having too many cities)
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Edit individual record
   */
  async edit({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.COLLECTION)

    try {
      const collectionId = params.id

      const collection = await ZnCollection.query()
        .where('id', collectionId)
        .preload('image')
        // .preload('products')
        .first()

      if (!collection) {
        return response.notFound({ message: 'Collection not found' })
      }

      return response.ok({
        data: {
          ...collection?.serialize(),
        },
        info: {},
      })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @update
   * @tag Admin Collection
   * @summary Update a collection
   * @description Update a collection descriptively
   * @paramPath id - ID of Collection - @type(string) @required
   * @requestBody <ZnCollection>
   * @responseBody 200 - <ZnCollection>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The title field must be defined","rule":"required","field":"title"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Collection not found"} - Not Found
   */
  /**
   * Handle form submission for the edit action
   */
  async update({ bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.COLLECTION)

    const collectionId = params.id

    const collection = await ZnCollection.query()
      .preload('products')
      .where('id', collectionId)
      .first()

    if (!collection) {
      return response.notFound({ message: 'Collection not found' })
    }

    const data = request.all()

    const payload = await createCollectionValidator.validate(data)

    try {
      collection.title = payload.title
      collection.description = payload.description || collection.description
      collection.status = payload.status

      // get image from db to save both imageId and imageUrl
      let image: ZnMedia | null = null
      if (payload.imageId) {
        image = await ZnMedia.find(payload.imageId)
      } else if (payload.imageUrl) {
        const images = await uploadMediasFromLinks([payload.imageUrl], MEDIA_TYPE.IMAGE)
        if (images.length > 0) {
          image = images[0]
        }
      }

      collection.imageId = image?.id || null
      collection.imageUrl = image?.url || null
      collection.imageAltText = payload.imageAltText || collection.imageAltText

      let productIds = collection.products.map((prod) => prod.id)

      if (payload.removedProductIds) {
        productIds = productIds.filter((id) => !payload.removedProductIds?.includes(id))
      }

      if (payload.addedProductIds) {
        productIds = productIds.concat(payload.addedProductIds)
      }

      await collection.related('products').sync(productIds)

      const updated = await collection.save()

      return response.ok(updated)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @destroy
   * @tag Admin Collection
   * @summary Soft-delete a collection
   * @description Soft-delete a collection descriptively
   * @paramPath id - ID of Collection - @type(string) @required
   * @responseBody 200 - {"message":"Collection soft-deleted successfully"} - OK
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Collection not found"} - Not Found
   */
  /**
   * Delete record
   */
  async destroy({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.COLLECTION)

    const collectionId = params.id

    const collection = await ZnCollection.query().where('id', collectionId).first()

    if (!collection) {
      return response.notFound({ message: 'Collection not found' })
    }

    await collection.softDelete()

    return response.ok({ message: 'Collection soft-deleted successfully' })
  }

  /**
   * @selectProduct
   * @tag Admin Collection
   * @summary Read product selection
   * @paramQuery page - Page number (default 1) - @type(number)
   * @paramQuery search - Search term - @type(string)
   * @requestBody {}
   * @responseBody 200 - <ZnProduct[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read product selection descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Show individual record
   */
  public async selectProduct({ request, response }: HttpContext) {
    const { page = 1, search, filter } = request.all()

    try {
      const query = ZnProduct.query().whereNull('deletedAt').preload('image').withCount('variants')

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder.whereRaw('LOWER(title) LIKE LOWER(?)', [`%${search}%`])
          queryBuilder.orWhereHas('variants', (variantQuery) => {
            variantQuery.where('sku', search)
          })
        })
      }

      if (filter) {
        query.whereNotIn('id', filter)
      }

      const result = await query.paginate(page, 5)

      return response.ok(result)
    } catch (error) {
      return response.internalServerError(error)
    }
  }

  /**
   * @listProduct
   * @tag Admin Collection
   * @summary Read all products of a collection
   * @paramQuery collectionId - ID of Collection - @type(string) @required
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @paramQuery search - Search term - @type(string)
   * @requestBody {}
   * @responseBody 200 - <ZnProduct[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null).paginated() - Read all produtcs descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display a list of resource
   */
  async listProduct({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.COLLECTION)

    try {
      const {
        page = 1,
        limit = 10,
        search,
        filter,
        collectionId,
        removedProductIds,
      } = request.all()

      const query = ZnProduct.query().preload('image').whereNull('deletedAt').withCount('variants')

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder.whereRaw('LOWER(title) LIKE LOWER(?)', [`%${search}%`])
          queryBuilder.orWhereHas('variants', (variantQuery) => {
            variantQuery.where('sku', search)
          })
        })
      }

      if (filter) {
        query.where((queryBuilder) => {
          filter.map((fil: string) =>
            queryBuilder.orWhereRaw(`LOWER(${fil.split('=')[0]}) LIKE LOWER(?)`, [
              `%${fil.split('=')[1]}%`,
            ])
          )
        })
      }

      if (collectionId) {
        query.whereHas('collections', (collectionQuery) => {
          collectionQuery.where('zn_collections.id', collectionId)
        })
      }

      if (removedProductIds) {
        query.whereNotIn('id', removedProductIds)
      }

      query.orderBy('updatedAt', 'desc')

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @checkProduct
   * @tag Admin Collection
   * @summary Check if product in collection
   * @requestBody {"collectionId":"","productId":"","removedProductIds":[]}
   * @responseBody 200 - true - Check if product in collection descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display a list of resource
   */
  async checkProduct({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.COLLECTION)

    try {
      const { collectionId = '', productId, removedProductIds = [] } = request.all()

      const collection = await ZnCollection.query()
        .preload('products')
        .where('id', collectionId)
        .first()

      if (!collection) {
        return response.notFound({ message: 'Collection not found' })
      }

      const product = collection.products
        .filter((prod) => !removedProductIds.includes(prod.id))
        .find((prod) => prod.id == productId)

      return response.ok(!!product)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @getCollectionAllProductIds
   * @tag Admin Collection
   * @summary Get all product ids from a collection
   * @paramPath id - ID of Collection - @type(string) @required
   * @responseBody 200 - [] - Get all product ids from a collection descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display a list of resource
   */
  async getCollectionAllProductIds({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.COLLECTION)

    try {
      const { id } = request.params()

      const collection = await ZnCollection.query().preload('products').where('id', id).first()

      if (!collection) {
        return response.notFound({ message: 'Collection not found' })
      }

      const productIds = collection.products.map((prod) => prod.id)

      return response.ok(productIds)
    } catch (error) {
      console.log(error)

      return response.badRequest(error)
    }
  }
}
