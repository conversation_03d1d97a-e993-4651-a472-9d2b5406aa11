import ZnProductVariant from '#models/zn_product_variant'
import ZnSupplier from '#models/zn_supplier'
import type { HttpContext } from '@adonisjs/core/http'
import { createSupplierValidator } from '../../validators/supplier/supplier_validator.js'

export default class SuppliersController {
  /**
   * @index
   * @tag Admin Supplier
   * @summary Read all suppliers
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @paramQuery search - Search terrm - @type(string)
   * @responseBody 200 - <ZnSupplier[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null).paginated() - Read all suppliers descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display a list of resource
   */
  async index({ request, response }: HttpContext) {
    const { page = 1, limit = 10, search } = request.qs()

    try {
      const query = ZnSupplier.query().withCount('variants')

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder
            .whereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
            .orWhere('code', search)
        })
      }

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      console.log(error)
      return response.internalServerError(error)
    }
  }

  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {}

  /**
   * @store
   * @tag Admin Supplier
   * @summary Create action
   * @requestBody <ZnSupplier>
   * @responseBody 201 - <ZnSupplier>.append("id":"","createdAt":"","updatedAt":"") - Create action descriptively
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The supplierName field must be defined","rule":"required","field":"supplierName"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Handle form submission for the create action
   */
  async store({ request, response }: HttpContext) {
    const data = request.all()

    const payload = await createSupplierValidator.validate(data)

    try {
      const created = await ZnSupplier.create({
        name: payload.name,
        fulfilSupplierId: payload.fulfilSupplierId || null,
        code: payload.code || null,
      })

      return response.created(created)
    } catch (error) {
      return response.internalServerError(error)
    }
  }

  /**
   * @show
   * @tag Admin Supplier
   * @summary Read a supplier
   * @responseBody 200 - <ZnSupplier>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read a supplier descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Show individual record
   */
  async show({ params, response }: HttpContext) {
    const supplierId = params.id

    try {
      const supplier = await ZnSupplier.query().where('id', supplierId).first()

      if (!supplier) {
        return response.notFound({ message: 'Supplier not found.' })
      }

      return response.ok(supplier)
    } catch (error) {
      return response.internalServerError(error)
    }
  }

  /**
   * Edit individual record
   */
  async edit({ params, response }: HttpContext) {
    const supplierId = params.id

    try {
      const supplier = await ZnSupplier.query().where('id', supplierId).first()

      if (!supplier) {
        return response.notFound({ message: 'Supplier not found.' })
      }

      return response.ok({
        data: supplier.serialize(),
      })
    } catch (error) {
      return response.internalServerError(error)
    }
  }

  /**
   * Handle form submission for the edit action
   */
  async update({ params, request, response }: HttpContext) {
    const data = request.all()

    const supplierId = params.id

    const supplier = await ZnSupplier.find(supplierId)

    if (!supplier) {
      return response.notFound({ message: 'Supplier not found.' })
    }

    const payload = await createSupplierValidator.validate(data)

    try {
      supplier.name = payload.name || supplier.name
      supplier.fulfilSupplierId = payload.fulfilSupplierId || supplier.fulfilSupplierId
      supplier.code = payload.code || supplier.code

      const updated = await supplier.save()

      return response.ok(updated)
    } catch (error) {
      return response.internalServerError(error)
    }
  }

  /**
   * @destroy
   * @tag Admin Supplier
   * @summary Soft-delete a supplier
   * @description Soft-delete a supplier descriptively
   * @paramPath id - ID of Supplier - @type(string) @required
   * @responseBody 200 - {"message":"Supplier soft-deleted successfully"} - OK
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Supplier not found"} - Not Found
   */
  /**
   * Delete record
   */
  async destroy({ params, response }: HttpContext) {
    const supplierId = params.id

    try {
      const supplier = await ZnSupplier.find(supplierId)

      if (!supplier) {
        return response.notFound({ message: 'Supplier not found' })
      }

      await supplier.softDelete()

      return response.ok({ message: 'Supplier soft-deleted successfully' })
    } catch (error) {
      return response.internalServerError(error)
    }
  }

  /**
   * @listVariants
   * @tag Admin Supplier
   * @summary Read all variants of a supplier
   * @paramQuery supplierId - ID of Supplier - @type(string) @required
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @paramQuery search - Search term - @type(string)
   * @responseBody 200 - <ZnInventory[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null).paginated() - Read all inventories descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display a list of resource
   */
  async listVariants({ params, request, response }: HttpContext) {
    const supplierId = params.id

    const supplier = await ZnSupplier.find(supplierId)

    if (!supplier) {
      return response.notFound({ message: 'Supplier not found.' })
    }

    try {
      const { page = 1, limit = 10, search } = request.qs()

      const query = ZnProductVariant.query()
        .preload('image')
        .whereHas('supplier', (supplierQuery) => {
          supplierQuery.where('id', supplierId)
        })

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder.where('sku', search)
        })
      }

      query.orderBy('updatedAt', 'desc')

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }
}
