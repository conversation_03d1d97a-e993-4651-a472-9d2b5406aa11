import ZnPostCategory from '#models/zn_post_category'
import { createPostCategoryValidator } from '#validators/post_category'
import { HttpContext } from '@adonisjs/core/http'
import { ACTION, RESOURCE } from '../../../app/constants/authorization.js'

export default class AdminPostCategoryController {
  /**
   * @index
   * @tag Admin Post-Category
   * @summary Read all categories
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @responseBody 200 - <ZnPostCategory[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null,"postsCount":7).with(thumbnail, posts, parentCategory, subCategories).paginated() - Read all categories descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  async index({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.POST_CATEGORY)

    try {
      const { page = 1, limit = 10, search } = request.qs()

      const query = ZnPostCategory.query()
        .whereNull('parentId')
        .preload('thumbnail')
        // .preload('posts')
        .withCount('posts', (query) => {
          query.where('isDraft', 0).whereNull('deletedAt')
        })
        .preload('parentCategory', (query) => {
          query.withCount('posts', (subQuery) => {
            subQuery.where('isDraft', 0).whereNull('deletedAt')
          })
        })
        .preload('subCategories', (query) => {
          query.withCount('posts', (subQuery) => {
            subQuery.where('isDraft', 0).whereNull('deletedAt')
          })
        })
        .orderByRaw('CASE WHEN orderBy IS NULL THEN 1 ELSE 0 END, orderBy ASC')
        .orderBy('name', 'asc')

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder
            .whereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(colorCode) LIKE LOWER(?)', [`%${search}%`])
        })
      }

      let result
      if (page && limit) {
        result = await query.paginate(page, limit)
      } else {
        result = await query
      }

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @create
   * @tag Admin Post-Category
   * @summary Return info for creation
   * @responseBody 200 - {"info":{}} - Return info for creation descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display form to create a new record
   */
  async create({ bouncer, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.POST_CATEGORY)

    try {
      return response.ok({
        info: {},
      })
    } catch (error) {
      return response.badRequest(error)
    }
  }
  /**
   * @store
   * @tag Admin Post-Category
   * @summary Create action
   * @requestBody <ZnPostCategory>
   * @responseBody 201 - <ZnPostCategory>.append("id":"","createdAt":"","updatedAt":"") - Create action descriptively
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The name field must be defined","rule":"required","field":"name"}]} - Bad Request
   * @responseBody 401 - Unauthorized access
   */
  public async store({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.POST_CATEGORY)

    const data = request.all()

    const payload = await createPostCategoryValidator.validate(data)

    try {
      const created = await ZnPostCategory.create({
        name: payload.name,
        colorCode: payload.colorCode,
        thumbnailId: payload.thumbnailId ?? undefined,
        parentId: payload.parentId ?? undefined,
        orderBy: payload.orderBy ?? undefined,
      })

      return response.created(created)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @show
   * @tag Admin Post-Category
   * @summary Read a category
   * @paramPath id - ID of Category - @type(string) @required
   * @responseBody 200 - <ZnPostCategory>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null,"postsCount":7).with(thumbnail, posts, parentCategory, subCategories) - Read a category descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Category not found"} - Not Found
   */
  public async show({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.POST_CATEGORY)

    try {
      const categoryId = params.id

      const category = await ZnPostCategory.query()
        .preload('thumbnail')
        .preload('posts')
        .withCount('posts', (query) => {
          query.where('isDraft', 0).whereNull('deletedAt')
        })
        .preload('parentCategory', (query) => {
          query.withCount('posts', (subQuery) => {
            subQuery.where('isDraft', 0).whereNull('deletedAt')
          })
        })
        .preload('subCategories', (query) => {
          query.withCount('posts', (subQuery) => {
            subQuery.where('isDraft', 0).whereNull('deletedAt')
          })
        })
        .where('id', categoryId)
        .first()

      if (!category) {
        return response.notFound('Category not found')
      }

      const categoryJSON = category.serialize()

      return response.ok(categoryJSON)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  async select({ request, response }: HttpContext) {
    const { page = 1, search, filter } = request.qs()
    try {
      const query = ZnPostCategory.query()
      // .whereNotNull('parentId')
      // .preload('thumbnail')
      // .preload('posts')
      // .withCount('posts', (query) => {
      //   query.where('isDraft', 0).whereNull('deletedAt')
      // })
      // .preload('parentCategory', (query) => {
      //   query.withCount('posts', (subQuery) => {
      //     subQuery.where('isDraft', 0).whereNull('deletedAt')
      //   })
      // })
      // .preload('subCategories', (query) => {
      //   query.withCount('posts', (subQuery) => {
      //     subQuery.where('isDraft', 0).whereNull('deletedAt')
      //   })
      // })
      // .orderByRaw('CASE WHEN orderBy IS NULL THEN 1 ELSE 0 END, orderBy ASC')
      // .orderBy('name', 'asc')

      if (search) {
        query.whereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
      }

      if (filter) {
        query.whereNotIn('id', filter)
      }

      const result = await query.paginate(page, 5)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @edit
   * @tag Admin Post-Category
   * @summary Return info for updating
   * @responseBody 200 - {"data":"<ZnPostCategory>","info":{}} - Return info for updating descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Category not found"} - Not Found
   */
  /**
   * Edit individual record
   */
  async edit({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.POST_CATEGORY)

    try {
      const categoryId = params.id

      const category = await ZnPostCategory.query()
        .preload('thumbnail')
        .where('id', categoryId)
        .first()

      if (!category) {
        return response.notFound({ message: 'Category not found' })
      }

      return response.ok({
        data: {
          ...category?.serialize(),
        },
        info: {},
      })
    } catch (error) {
      return response.badRequest(error)
    }
  }
  /**
   * @update
   * @tag Admin Post-Category
   * @summary Update a category
   * @description Update a category descriptively
   * @paramPath id - ID of Category - @type(string) @required
   * @requestBody <ZnPostCategory>
   * @responseBody 200 - <ZnCategory>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The name field must be defined","rule":"required","field":"name"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Category not found"} - Not Found
   */
  public async update({ bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.POST_CATEGORY)

    const categoryId = params.id

    const category = await ZnPostCategory.query().where('id', categoryId).first()

    if (!category) {
      return response.notFound({ message: 'Category not found' })
    }

    const data = request.all()

    const payload = await createPostCategoryValidator.validate(data)

    try {
      category.name = payload.name
      category.colorCode = payload.colorCode
      category.thumbnailId = payload.thumbnailId ? payload.thumbnailId : category.thumbnailId
      category.orderBy = payload.orderBy || category.orderBy

      const updated = await category.save()

      return response.ok(updated)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @destroy
   * @tag Admin Post-Category
   * @summary Soft-delete a category
   * @description Soft-delete a category descriptively
   * @paramPath id - ID of Category - @type(string) @required
   * @responseBody 200 - {"message":"Category soft-deleted successfully"} - OK
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Category not found"} - Not Found
   */
  public async destroy({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.POST_CATEGORY)

    const categoryId = params.id

    const category = await ZnPostCategory.query().where('id', categoryId).first()

    if (!category) {
      return response.notFound({ message: 'Category not found' })
    }

    await category.softDelete()

    return response.ok({ message: 'Category soft-deleted successfully' })
  }
}
