import ZnGift from '#models/zn_gift'
import { HttpContext } from '@adonisjs/core/http'
import db from '@adonisjs/lucid/services/db'
import { DateTime } from 'luxon'
import { ACTION, RESOURCE } from '../../../app/constants/authorization.js'
import { createGiftValidator } from '../../validators/gift/gift_validator.js'

export default class AdminGiftController {
  /**
   * @store
   * @tag Admin Gift
   * @summary Create action
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Handle form submission for the create action
   */
  async store({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.GIFT)

    // @ts-ignore
    const data = request.all()

    const payload = await createGiftValidator.validate(data)

    try {
      const created = await ZnGift.create({
        title: payload.title,
        description: payload.description,
        minAmount: payload.minAmount,
        thumbnailId: payload.thumbnailId,
        status: payload.status,
        applicableYears: payload.applicableYears,
        numberOfUses: payload.numberOfUses,
        isUnlimited: payload.isUnlimited,
        isConditional: payload.isConditional,
        startDate: payload.startDate ? DateTime.fromJSDate(payload.startDate as any).toUTC() : null,
        endDate: payload.endDate ? DateTime.fromJSDate(payload.endDate as any).toUTC() : null,
      })

      // Sync gift variants
      const giftId = created.id
      if (payload.variants.length > 0) {
        await db.table('zn_gift_variants').multiInsert(
          payload.variants.map((variant) => ({
            giftId,
            variantId: variant.variantId,
            giftPrice: variant.giftPrice,
          }))
        )
      }

      // Sync gift condition variants
      if (payload.conditionVariants.length > 0) {
        await created
          .related('conditionVariants')
          .sync(payload.conditionVariants.map((variant) => variant.variantId))
      }

      return response.created(created)
    } catch (error) {
      console.log(error)
      return response.internalServerError({
        message: 'Something went wrong',
        error: error,
      })
    }
  }

  /**
   * @show
   * @tag Admin Gift
   * @summary Read a gift
   * @paramPath id - ID of Gift - @type(string) @required
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Gift not found"} - Not Found
   */
  /**
   * Show individual record
   */
  async show({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.GIFT)

    try {
      const giftId = params.id

      const gift = await ZnGift.query()
        .preload('thumbnail')
        .preload('variants', (query) => {
          query.preload('image')
        })
        .preload('conditionVariants', (query) => {
          query.preload('image')
        })
        .where('id', giftId)
        .first()
      if (gift) {
        const variants = gift?.variants?.map((variant) => {
          const giftPrice = variant.$extras.pivot_giftPrice
          return {
            ...variant.toJSON(),
            giftPrice: giftPrice,
          }
        })

        return response.ok({
          ...gift?.toJSON(),
          variants,
        })
      }
      return response.ok(null)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @index
   * @tag Admin Gift
   * @summary Read all gifts
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display a list of resource
   */
  async index({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.GIFT)

    try {
      const { page = 1, limit = 10, search, sort, sortBy } = request.qs()

      const query = ZnGift.query().preload('thumbnail').whereNull('deletedAt')

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder.whereRaw('LOWER(title) LIKE LOWER(?)', [`%${search}%`])
        })
      }

      if (sort || sortBy) {
        if (sortBy === 'newest') {
          query.orderBy('updatedAt', 'desc')
        }

        if (sort && sort.length > 0) {
          query.orderBy(sort[0], sort[1])
        }
      } else {
        query.orderBy('updatedAt', 'desc')
      }

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @update
   * @tag Admin Gift
   * @summary Update a gift
   * @description Update a gift descriptively
   * @paramPath id - ID of Gift - @type(string) @required
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Gift not found"} - Not Found
   */
  /**
   * Handle form submission for the edit action
   */
  async update({ bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.GIFT)

    const giftId = params.id
    const gift = await ZnGift.query().where('id', giftId).first()

    if (!gift) {
      return response.notFound({ message: 'Gift not found' })
    }

    const data = request.all()
    const payload = await createGiftValidator.validate(data)
    try {
      // const previousStatus = gift.status

      gift.title = payload.title || gift.title
      gift.description = payload.description || gift.description
      gift.minAmount = payload.minAmount || gift.minAmount
      gift.thumbnailId = payload.thumbnailId || gift.thumbnailId
      gift.status = payload.status || gift.status
      gift.applicableYears = payload.applicableYears || gift.applicableYears
      gift.numberOfUses = payload.numberOfUses || gift.numberOfUses
      gift.isUnlimited = payload.isUnlimited ?? gift.isUnlimited
      gift.isConditional = payload.isConditional ?? gift.isConditional

      gift.startDate = payload.startDate
        ? DateTime.fromJSDate(new Date(payload.startDate))
        : gift.startDate

      gift.endDate = payload.endDate ? DateTime.fromJSDate(new Date(payload.endDate)) : gift.endDate

      const updated = await gift.save()

      // Sync gift variants
      await db.from('zn_gift_variants').where('giftId', giftId).delete()
      await db.table('zn_gift_variants').multiInsert(
        payload.variants.map((variant) => {
          let giftPrice = variant.giftPrice
          if (giftPrice !== null) {
            giftPrice = Number(giftPrice)
          }
          return {
            giftId,
            variantId: variant.variantId,
            giftPrice: variant.giftPrice,
          }
        })
      )

      // Sync gift condition variants
      await gift
        .related('conditionVariants')
        .sync(payload.conditionVariants.map((variant) => variant.variantId))

      return response.ok(updated)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
        error: error,
      })
    }
  }

  /**
   * @destroy
   * @tag Admin Gift
   * @summary Soft-delete a giuft
   * @description Soft-delete a gift descriptively
   * @paramPath id - ID of GIft - @type(string) @required
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Gift not found"} - Not Found
   */
  /**
   * Delete record
   */
  async destroy({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.GIFT)

    const giftId = params.id

    const gift = await ZnGift.query().where('id', giftId).first()

    if (!gift) {
      return response.notFound({ message: 'Gift not found' })
    }

    await gift.softDelete()

    return response.ok({ message: 'Gift soft-deleted successfully' })
  }
}
