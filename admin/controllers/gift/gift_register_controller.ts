import { HttpContext } from '@adonisjs/core/http'
import ZnGiftRegister from '#models/zn_gift_register'
import { writeToString } from 'fast-csv'
import { format } from 'date-fns'

export default class AdminGiftRegisterController {
  async getOne({ params, response }: HttpContext) {
    try {
      const giftRegisterId = params.id

      const giftRegister = await ZnGiftRegister.query().where('id', giftRegisterId).first()

      return response.ok(giftRegister)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  async getList({ request, response }: HttpContext) {
    try {
      const { page = 1, limit = 10, sort, sortBy, giftId } = request.qs()

      const query = ZnGiftRegister.query().whereNull('deletedAt')

      if (giftId) {
        query.andWhere('giftId', giftId)
      }

      if (sort || sortBy) {
        if (sortBy === 'newest') {
          query.orderBy('updatedAt', 'desc')
        }

        if (sort && sort.length > 0) {
          query.orderBy(sort[0], sort[1])
        }
      } else {
        query.orderBy('updatedAt', 'desc')
      }

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  async softDelete({ params, response }: HttpContext) {
    const giftRegisterId = params.id

    const giftRegister = await ZnGiftRegister.query().where('id', giftRegisterId).first()

    if (!giftRegister) {
      return response.notFound({ message: 'Gift register not found' })
    }

    await giftRegister.softDelete()

    return response.ok({ message: 'Gift soft-deleted successfully' })
  }

  async export({ request, response }: HttpContext) {
    try {
      const { giftId } = request.qs()

      const query = ZnGiftRegister.query().whereNull('deletedAt')

      if (giftId) {
        query.andWhere('giftId', giftId)
      }

      const result = await query.select()
      const serializedResult = result.map((row) => row.toJSON())

      const csv = await writeToString(serializedResult, {
        headers: true,
      })
      const currentDate = format(new Date(), 'ddMMyyyy')
      const filename = `gift_register_download_${currentDate}.csv`

      response.header('Content-Type', 'text/csv')
      response.attachment(filename)

      return response.send(csv)
    } catch (error) {
      return response.badRequest(error)
    }
  }
}
