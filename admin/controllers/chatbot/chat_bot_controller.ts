/****************************************************************************************
 * Cha<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ontroller
 * A back-office companion to `<PERSON>tBotController` that lets staff moderate / participate
 * in every bot room without the per-user restrictions that apply to customers.
 *
 * Key differences vs ChatBotController
 * ────────────────────────────────────
 * • Auth: uses `ZnAdmin` guard (admin token) instead of end-user JWT.
 * • Token: gives admins SEND_MESSAGE | DISCONNECT_USER | DELETE_MESSAGE powers.
 * • Message writes: sender recorded as `{ adminId }`; no owner checks on clear/delete.
 * • Extras: endpoint to list all bot rooms (paginated   & ordered by latest activity).
 ****************************************************************************************/

import ZnAdmin from '#models/zn_admin'
import ZnChatMessage from '#models/zn_chat_message'
import ZnChatRoom from '#models/zn_chat_room'
import ZnCollection from '#models/zn_collection'
import ZnPost from '#models/zn_post'
import ZnProduct from '#models/zn_product'
import { IvschatService } from '#services/aws/ivschat_service'
import { HttpContext } from '@adonisjs/core/http'
import queue from '@rlanz/bull-queue/services/main'
import db from '@adonisjs/lucid/services/db'
import ZnChatThread from "#models/zn_chat_thread";
import OpenAI from "openai";
import ChatBotMessageJob from "#jobs/chat_bot_message_job";
import ThreadsDeleteJob from "#jobs/threads_delete_job";
import AnnotatedChatMessageJob from "#jobs/annotate_chat_message_job";
import {DateTime} from "luxon";
import ZnUser from "#models/zn_user";
import ZnAIAssistant from "#models/zn_ai_assistant";
import SendMessageToIvsJob from "#jobs/send_message_to_ivs_job";

export default class AdminChatbotController {
  private ivsChatService: IvschatService
  private openai: OpenAI

  constructor() {
    this.ivsChatService = new IvschatService()
    this.openai = new OpenAI()
  }

  /**
   * @createChatToken
   * @tag Admin Chat Bot
   * @summary Issue an IVS-Chat token for an admin in a chat-bot room
   * @paramPath id – ID of Chat Room – @required
   * @responseBody 200 – {"token":"","tokenExpirationTime":"2025-02-18T22:44:57.000Z"} – Token payload
   */

  async createChatToken({auth, params, response}: HttpContext) {
    const roomId = params.id
    const room = await ZnChatRoom.find(roomId)
    if (!room) {
      return response.notFound({ message: 'Chat Room not found'})
    }

    const admin = auth.getUserOrFail() as ZnAdmin
    await admin?.load('avatar')

    const attributes = {
      type: 'admin',
      username: admin.username,
      name: admin.name,
      avatarUrl: admin.avatar?.url
    }

    const token = await this.ivsChatService.createChatToken({
      roomArn: room.arn,
      userId: admin.id,
      capabilities: ['SEND_MESSAGE'],
      attributes
    })

    return response.ok(token)
  }

  async getChatRoom({auth, response}: HttpContext) {
    const requestUser = auth.getUserOrFail() as ZnAdmin
    try {
      const thread = await ZnChatThread.query()
        .where({ adminId: requestUser.id })
        .preload('room', (roomQuery) => {
          roomQuery
            .preload('messages', (messagesQuery) => {
              messagesQuery
                .preload('user', (userQuery) => {
                  userQuery.preload('avatarMedia')
                })
                .orderBy('createdAt', 'desc')
            })
        })
        .first()

      let room: ZnChatRoom | null = null
      if (!thread) {
        const ivsRoom = await this.ivsChatService.createRoom({ name: 'chat-bot-room' })

        const dbRoom = await ZnChatRoom.create({
          arn: ivsRoom.arn,
          name: 'chat-bot-room',
        })

        const thread = await this.openai.beta.threads.create()

        await ZnChatThread.create({
          roomId: dbRoom.id,
          adminId: requestUser.id,
          openaiThreadId: thread.id,
        })

        await dbRoom.load('messages')

        room = dbRoom
      } else {
        room = thread.room
      }
      if (room?.messages.length == 0) {

        const message = await ZnChatMessage.create({
          roomId: room.id,
          content: `How can I help you?`
        })

        room.messages.push(message)
      }
      const isStaffResponding = thread?.isStaffResponding ?? false
      return response.ok({
        ...room,
        isStaffResponding,
      })


    } catch (error) {
      console.log(error)
      return response.internalServerError(error)
    }
  }

  /**
   * @createChatMessage
   * @tag Chat Bot
   * @summary Post a user message and trigger the bot reply (auto-provisions room/thread)
   * @requestBody {"content":"Hi there!"}
   * @responseBody 200 - Sent
   */
  async createChatMessage({ auth, params, request, response }: HttpContext) {
    try {

      const roomId = params.id
      const room = await ZnChatRoom.find(roomId)
      let thread = await ZnChatThread.findBy({ roomId})
      if (!room || !thread) { return response.notFound("Chat Room Not Found") }
      const user = auth.getUserOrFail() as ZnAdmin

      console.log(`Room: ${room.id}, user: ${user.id} sending message ${JSON.stringify(request.body() as any)}`)

      if (thread.adminId != user.id && !thread.isStaffResponding) {
        return response.unauthorized("Staff must take over the room before replying")
      }

      const data = request.body() as any
      await queue.dispatch(
        SendMessageToIvsJob,
        {
          roomArn: room.arn,
          senderId: user.id,
          content: data.content,
          attributes: {
            type: 'admin',
          }
        },
        {attempts: 1, queueName: 'chatBot'}
      )

      console.log("Successfully sent message")

      if (!thread.isStaffResponding) {
        await queue.dispatch(
          ChatBotMessageJob,
          {
            roomId: room.id,
            data,
            userId: user.id,
          },
          { attempts: 1, queueName: 'chatBot' },
        )
      }

      console.log("Sent")
      return response.ok('Sent')
    } catch (error) {
      console.error(error)
      return response.internalServerError(error)
    }
  }

  async getChatMessages({ params, request, response }: HttpContext) {
    try {
      const limit = Number(request.input('limit', 10))
      const page = Number(request.input('page', 1))
      const search = (request.input('search', '') as string).trim()
      const roomId = params.id
      const room = await ZnChatRoom.find(roomId)
      if (!room) { return response.notFound("Chat Room Not Found") }
      const query = ZnChatMessage.query()
        .where({roomId})
        .preload('user', (userQuery) => {
          userQuery.preload('avatarMedia')
        })
        .orderBy('createdAt', 'desc')

      if (search) {
        const searchTerms = `%${search.split(' ').filter(Boolean).join('%')}%`
        query.andWhereILike('content', searchTerms)
      }

      const result = await query.paginate(page, limit)
      const {meta, data} = result.serialize()

      const getName = async (message: any) => {
        if (message.userId) {
          const user = await ZnUser.query().where('id', message.userId).first()
          if (user) return `${user.firstName} ${user.lastName}`
        }
        if (message.adminId) {
          const admin = await ZnAdmin.query().where('id', message.adminId).first()
          if (admin) return admin.username
        }

        if (message.assistantId) {
          const assistant = await ZnAIAssistant.query().where('id', message.assistantId).first()
          if (assistant) return assistant.role as string
        }

        return ''
      }

      const getAvatarUrl = async (message: any) => {
        if (message.userId) {
          const user = await ZnUser.query().where('id', message.userId).first()
          return user?.avatarUrl
        }
        return null
      }

      const enrichedData = await Promise.all(data.map( async (message) => {
        const userName = await getName(message)
        const avatarUrl = await getAvatarUrl(message)
        return {
          ...message,
          userName: userName,
          avatarUrl: avatarUrl,
        }
      }))
      return response.ok({...meta, data: enrichedData})
    } catch (error) {
      console.log(error)
      return response.internalServerError(error)
    }
  }

  /**
   * @clearChatMessages
   * @tag Chat Bot
   * @summary Clear chat messages
   * @paramPath id - ID of Chat Room - @required
   * @responseBody 200 - Chat messages cleared - Clear chat messages descriptively
   */
  async clearChatMessages({ auth, params, response }: HttpContext) {
    try {
      const roomId = params.id
      const room = await ZnChatRoom.find(roomId)

      const thread = await ZnChatThread.findBy({ roomId })
      await queue.dispatch(
        ThreadsDeleteJob,
        {threadIds: thread ? [thread.id] : []},
        { attempts: 1, queueName: "chatBot" }
      )
      if (!room || !thread) { return response.notFound("Chat Room Not Found") }

      const user = auth.getUserOrFail() as ZnAdmin
      if (thread.adminId != user.id) {
        return response.unauthorized("Cannot clear messages from another person's room")
      }

      const messages = await ZnChatMessage.query()
        .where({ roomId })

      for (const message of messages) {
        await message.softDelete()
      }

      return response.ok("Chat messages cleared")

    } catch (error) {
      console.log(error)
      return response.internalServerError(error)
    }
  }


  /**
   * @listChatResources
   * @tag Chat Bot
   * @summary List Chat Resources
   * @requestBody {"productIds":"['']","collectionIds":"['']","postIds":"['']"}
   * @responseBody 200 - {"products":["ZnProduct"],"collections":["ZnCollection"],"posts":["ZnPost"]} - Get chat suggestions descriptively
   */
  async listChatResources({ request, response }: HttpContext) {
    try {
      const {
        productIds,
        collectionIds,
        postIds,
      } = request.body()

      const productIdArray = this.getParsedArray(productIds)
      const collectionIdArray = this.getParsedArray(collectionIds)
      const postIdArray = this.getParsedArray(postIds)

      let products
      if (productIdArray) {
        products = await ZnProduct.query()
          .whereIn('id', productIdArray)
          .whereNot('status', 'draft')
          .where('isGift', false)
          .preload('variant')
          .preload('image')
          .preload('reviewsSummary')
      }

      let collections
      if (collectionIdArray) {
        collections = await ZnCollection.query()
          .whereIn('id', collectionIdArray)
          .where('status', true)
      }

      let posts
      if (postIdArray) {
        posts = await ZnPost.query()
          .whereIn('id', postIdArray)
          .where({
            expired: false,
            isDraft: false,
            isUnlist: false
          })
          .preload('thumbnail')
          .preload('medias')
      }


      return {
        products,
        collections,
        posts,
      }


    } catch (error) {
      console.log(error)
      return response.internalServerError({
        message: "Something went wrong!",
        error
      })
    }
  }

  /**
   * @annotateChatMessage
   * @tag Chat Bot
   * @summary Leave a private admin annotation on an existing chat message
   * @description
   *   Queues an **AnnotatedChatMessageJob** that inserts a new `zn_chat_messages`
   *   row whose `annotatedChatMessageId` points to the original message.
   *   The annotation is **only** visible to staff; end-users never see it.
   *
   * @paramPath id                 – ID of Chat Room                     – @required
   * @paramPath originalMessageId   – ID of Chat Message to annotate      – @required
   *
   * @requestBody
   *   { "content": "This message violates our community guidelines." }
   *
   * @responseBody 202 – "Annotation queued" – The annotation job has been dispatched
   */

  async annotateMessage({auth, params, response, request }: HttpContext) {
    try {
      const roomId = params.id
      const { content }       = request.only(['content'])
      const originalMessageId = params.originalMessageId
      const originalMessage = await ZnChatMessage.find(originalMessageId)
      if (!originalMessage) {
        return response.notFound({ message: 'Original message not found' })
      }
      const admin = auth.getUserOrFail() as ZnAdmin
      const thread = await ZnChatThread.findBy({ roomId })
      if (!thread || originalMessage.roomId !== roomId) {
        return response.unauthorized('You are not allowed to annotate in this room')
      }

      await queue.dispatch(
        AnnotatedChatMessageJob,
        {
          roomId: roomId,
          data: {
            annotatedChatMessageId: originalMessageId,
            content: content,
            adminId:  admin.id,
            sendTime: DateTime.utc().toISO()
          }
        },
        { attempts: 1, queueName: "chatBot" }
      )

      return response.accepted("Annotated chat")

    } catch (error) {
      console.log(error)
      return response.internalServerError({
        message: "Something went wrong!",
        error
      })
    }
  }

  public async getChatRooms({ request, response }: HttpContext) {
    const page = Number(request.qs().page ?? 1)
    const limit = Number(request.qs().limit ?? 20)

    const roomsFilter = await db.query()
      .from('zn_chat_rooms')
      .leftJoin('zn_chat_messages', 'zn_chat_rooms.id', 'zn_chat_messages.roomId')
      .groupBy('zn_chat_rooms.id')
      .orderByRaw('COALESCE(MAX(zn_chat_messages.createdAt), zn_chat_rooms.createdAt) DESC')
      .select('zn_chat_rooms.*',db.raw('MAX(zn_chat_messages.createdAt) AS latestMessageAt'))
      .where('name', 'chat-bot-room')
      .paginate(page, limit)

    const rooms = await ZnChatRoom.query()
      .whereIn('id', roomsFilter.map(room => room.id))


    const data = await Promise.all(rooms.map(async (room) => {
      const metaEntry = roomsFilter.toJSON().data.find(data => data.id === room.id)
      const latestMessageAt = metaEntry.latestMessageAt
      const userName = await this.getDisplayName(room.id)
      const latestMessage = await ZnChatMessage.query().where('roomId', room.id).orderBy('createdAt', 'desc').first()
      const lastMessage = latestMessage?.content ?? ""
      const thread     = await ZnChatThread.findBy('roomId', room.id)
      const isStaffResponding = thread?.isStaffResponding ?? false
      return Object.assign(room.serialize(), {latestMessageAt, userName, lastMessage, isStaffResponding})
    }))

    return response.ok({
      meta: roomsFilter.toJSON().meta,
      data: data,
    })
  }

  private async getDisplayName(roomId: string) {
    let thread = await ZnChatThread.query().where('roomId', roomId).first()
    let displayName = "Couldn't find a chat name"
    if (!thread) {
      return displayName
    }

    let userType : "user" | "admin" | undefined
    let userId : string | undefined
    let user = null
    if (thread.userId) {
      userId = thread.userId
      userType = "user"
    } else if (thread.adminId) {
      userId = thread.adminId
      userType = "admin"
    }

    if (!userId || !userType) {
      return displayName
    }

    switch (userType) {
      case "user":
        user = await ZnUser.query().where('id', userId).first()
        displayName = user ? user.firstName + " " + user.lastName : displayName
        break
      case "admin":
        user = await ZnAdmin.query().where('id', userId).first()
        displayName = user ? user.username : displayName
        break
      default:
        break
    }
    return displayName
  }

  async takeOverRoom({ auth, params, response}: HttpContext) {
    try {
      const admin = auth.getUserOrFail() as ZnAdmin
      const roomId = params.id
      const thread = await ZnChatThread.query().where('roomId', roomId).first()
      const room = await ZnChatRoom.query().where('id', roomId).first()
      console.log(`${admin.username} tried to take over`)
      if (!thread) {
        return response.notFound({message: 'Chat thread not found'})
      }

      if (!room) {
        return response.notFound({message: 'Room not found'})
      }

      if (thread.isStaffResponding) {
        return response.ok({message: 'Staff is responding'})
      }

      await queue.dispatch(
        SendMessageToIvsJob,
        {
          roomArn: room.arn,
          senderId: admin.id,
          content:  `Zurno Customer Service has entered the chat.`,
          attributes: {
            type: 'admin',
          }
        }, {attempts: 1, queueName: 'chatBot'}
      )

      thread.isStaffResponding = true
      await thread.save()
      return response.ok({ message: 'Staff take‑over activated' })
    } catch (error) {
      console.error(error)
      return response.internalServerError('Unable to take over room')
    }
  }

  /**
   * @stopTakingOver
   * @tag Chatbot
   * @summary Staff leaves the chat and relinquishes control
   * @paramPath id – ID of Chat Room – @required
   * @responseBody 200 – {"message":"Staff take‑over ended"}
   */
  async stopTakingOver({ auth, params, response }: HttpContext) {
    try {
      const admin  = auth.getUserOrFail() as ZnAdmin
      const roomId = params.id

      const thread = await ZnChatThread.findBy('roomId', roomId)
      const room = await ZnChatRoom.query().where('id', roomId).first()
      if (!thread) {
        return response.notFound({ message: 'Chat thread not found' })
      }

      if (!room) {
        return response.notFound({ message: 'Chat room not found' })
      }

      if (!thread.isStaffResponding) {
        return response.ok({ message: 'Staff is not currently responding' })
      }

      await queue.dispatch(
        SendMessageToIvsJob,
        {
          roomArn: room.arn,
          senderId: admin.id,
          content:  `Zurno Customer Service has left the chat.`,
          attributes: {
            type: 'admin',
          }
          }, {attempts: 1, queueName: 'chatBot'}
      )

      thread.isStaffResponding = false
      await thread.save()

      return response.ok({ message: 'Staff take‑over ended' })
    } catch (error) {
      console.error(error)
      return response.internalServerError('Unable to stop taking over room')
    }
  }

  private getParsedArray(input?: string | string[]) {
    if (!input) { return }

    let outputArray = []
    if (Array.isArray(input)) {
      outputArray = input
    } else {
      try {
        outputArray = JSON.parse(input)
      } catch {
        let leftBracketIdx = input.indexOf('[')
        let rightBracketIdx = input.indexOf(']')
        if (rightBracketIdx < 0) { rightBracketIdx = input.length }

        outputArray = input
          .slice(leftBracketIdx + 1, rightBracketIdx)
          .replaceAll('\'', '')
          .split(',')
      }
    }

    return outputArray
  }
}
