import { GPTTranslationService } from '#services/gpt/translation_service'
import { GPTTranslationValidator } from '#validators/gpt'
import type { HttpContext } from '@adonisjs/core/http'
export default class AdminGPTController {
  private translationService
  constructor() {
    this.translationService = new GPTTranslationService()
  }
  /**
   * @translation
   * @tag GPT
   * @requestBody {"text": "Sample text" ,"translateTo": "vi"}
   * @responseBody 200 - {"text": "Sample text" ,"translateTo": "vi", "translateFrom": "en"}
   */
  async translation({ request, response }: HttpContext) {
    const payload = await request.validateUsing(GPTTranslationValidator)
    const data = await this.translationService.translation(payload)
    return response.ok(data)
  }
}
