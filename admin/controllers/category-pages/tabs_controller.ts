import ZnCategoryPagesTabs from '#models/zn_category_pages_tabs'
import { CategoryPagesService } from '#services/firebase/category_page_service'
import { appCreateCategoryPagesTabsValidator } from '#validators/app/category-pages/category_pages'
import { HttpContext } from '@adonisjs/core/http'

export default class AdminCategoryPagesTabsController {
  private categoryPagesService = new CategoryPagesService()

  /**
   * @create
   * @tag Admin Tabs
   * @summary Create a tab
   * @requestBody <ZnCategoryPagesTabs>
   * @responseBody 201 - <ZnCategoryPagesTabs>.append("id":"","createdAt":"","updatedAt":"") - Create a tab descriptively
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The title field must be defined","rule":"required","field":"title"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  public async create({ request, response }: HttpContext) {
    try {
      const data = request.all()

      const payload = await appCreateCategoryPagesTabsValidator.validate(data)

      const created = await ZnCategoryPagesTabs.create({
        title: payload.title,
        key: payload.key,
      })

      this.categoryPagesService.writeTab(created)

      return response.created(created)
    } catch (error) {
      if (error.messages) {
        return response.badRequest({
          message: 'Validation failed',
          errors: error.messages,
        })
      }

      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @list
   * @tag Admin Tabs
   * @summary Read all tabs
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @responseBody 200 - <ZnCategoryPagesTabs[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null).paginated() - Read all tabs descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  async list({ request, response }: HttpContext) {
    try {
      const { page = 1, limit = 10 } = request.qs()

      const result = await ZnCategoryPagesTabs.query().whereNull('deletedAt').paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @show
   * @tag Admin Tabs
   * @summary Read a tabs
   * @paramPath id - ID of Tab - @type(string) @required
   * @responseBody 200 - <ZnCategoryPagesTabs>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read a tab descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Tab not found"} - Not Found
   */
  public async show({ params, response }: HttpContext) {
    try {
      const tab = await this.tabIfExists(params, response)
      if (!tab) {
        return
      }

      return response.ok(tab)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @update
   * @tag Admin Tabs
   * @summary Update a tab
   * @description Update a tab descriptively
   * @paramPath id - ID of Tab - @type(string) @required
   * @requestBody <ZnCategoryPagesTabs>
   * @responseBody 200 - <ZnCategoryPagesTabs>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The title field must be defined","rule":"required","field":"title"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Tab not found"} - Not Found
   */
  public async update({ request, response, params }: HttpContext) {
    try {
      const tab = await this.tabIfExists(params, response)
      if (!tab) {
        return
      }

      const data = request.all()

      const payload = await appCreateCategoryPagesTabsValidator.validate(data)

      tab.title = payload.title
      tab.key = payload.key

      const updated = await tab.save()

      this.categoryPagesService.writeTab(updated)

      return response.ok(updated)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @softDelete
   * @tag Admin Tabs
   * @summary Soft-delete a tab
   * @description Soft-delete a tab descriptively
   * @paramPath id - ID of Tab - @type(string) @required
   * @responseBody 200 - {"message":"Tab soft-deleted successfully"}
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Tab not found"} - Not Found
   */
  public async softDelete({ params, response }: HttpContext) {
    try {
      const tab = await this.tabIfExists(params, response)
      if (!tab) {
        return
      }

      await tab?.softDelete()

      this.categoryPagesService.deleteTab(tab)

      return response.ok({ message: 'Tab soft-deleted successfully' })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  private async tabIfExists(params: HttpContext['params'], response: HttpContext['response']) {
    const tabId = params.id

    const tab = await ZnCategoryPagesTabs.query().where('id', tabId).first()

    if (!tab) {
      response.notFound({ message: 'Tab not found' })
    }

    return tab
  }
}
