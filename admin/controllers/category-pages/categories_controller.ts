import ZnCategoryPagesCategories from '#models/zn_category_pages_categories'
import { CategoryPagesService } from '#services/firebase/category_page_service'
import { appCreateCategoryPagesCategoriesValidator } from '#validators/app/category-pages/category_pages'
import { HttpContext } from '@adonisjs/core/http'

export default class AdminCategoryPagesCategoriesController {
  private categoryPagesService = new CategoryPagesService()

  /**
   * @create
   * @tag Admin Categories
   * @summary Create a category
   * @requestBody <ZnCategoryPagesCategories>
   * @responseBody 201 - <ZnCategoryPagesCategories>.append("id":"","createdAt":"","updatedAt":"") - Create a category descriptively
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The handle field must be defined","rule":"required","field":"handle"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  public async create({ request, response }: HttpContext) {
    try {
      const data = request.all()

      const payload = await appCreateCategoryPagesCategoriesValidator.validate(data)

      const created = await ZnCategoryPagesCategories.create({
        handle: payload.handle,
        key: payload.key,
      })

      this.categoryPagesService.writeCategory(created)

      return response.created(created)
    } catch (error) {
      if (error.messages) {
        return response.badRequest({
          message: 'Validation failed',
          errors: error.messages,
        })
      }

      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @list
   * @tag Admin Categories
   * @summary Read all categories
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @responseBody 200 - <ZnCategoryPagesCategories[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null).paginated() - Read all categories descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  async list({ request, response }: HttpContext) {
    try {
      const { page = 1, limit = 10 } = request.qs()

      const result = await ZnCategoryPagesCategories.query()
        .whereNull('deletedAt')
        .paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @show
   * @tag Admin Categories
   * @summary Read a category
   * @paramPath id - ID of Category - @type(string) @required
   * @responseBody 200 - <ZnCategoryPagesCategories>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read a category descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Category not found"} - Not Found
   */
  public async show({ params, response }: HttpContext) {
    try {
      const category = await this.categoryIfExists(params, response)
      if (!category) {
        return
      }

      return response.ok(category)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @update
   * @tag Admin Categories
   * @summary Update a category
   * @description Update a category descriptively
   * @paramPath id - ID of Category - @type(string) @required
   * @requestBody <ZnCategoryPagesCategories>
   * @responseBody 200 - <ZnCategoryPagesCategories>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The handle field must be defined","rule":"required","field":"handle"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Category not found"} - Not Found
   */
  public async update({ request, response, params }: HttpContext) {
    try {
      const category = await this.categoryIfExists(params, response)
      if (!category) {
        return
      }

      const data = request.all()

      const payload = await appCreateCategoryPagesCategoriesValidator.validate(data)

      category.handle = payload.handle
      category.key = payload.key

      const updated = await category.save()

      this.categoryPagesService.writeCategory(updated)

      return response.ok(updated)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @softDelete
   * @tag Admin Categories
   * @summary Soft-delete a category
   * @description Soft-delete a category descriptively
   * @paramPath id - ID of Category - @type(string) @required
   * @responseBody 200 - {"message":"Category soft-deleted successfully"}
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Category not found"} - Not Found
   */
  public async softDelete({ params, response }: HttpContext) {
    try {
      const category = await this.categoryIfExists(params, response)
      if (!category) {
        return
      }

      await category?.softDelete()

      this.categoryPagesService.deleteCategory(category)

      return response.ok({ message: 'Category soft-deleted successfully' })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  private async categoryIfExists(params: HttpContext['params'], response: HttpContext['response']) {
    const categoryId = params.id

    const category = await ZnCategoryPagesCategories.query().where('id', categoryId).first()

    if (!category) {
      response.notFound({ message: 'Category not found' })
    }

    return category
  }
}
