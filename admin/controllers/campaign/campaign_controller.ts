import { ACTION, RESOURCE } from '#constants/authorization'
import Send<PERSON><PERSON>aignNot<PERSON>Job from '#jobs/send_campaign_notification_job'
import ZnCampaign, { ECampaignSendType, ECampaignStatus } from '#models/zn_campaign'
import { CampaignService } from '#services/campaign_service'
import { HttpContext } from '@adonisjs/core/http'
import queue from '@rlanz/bull-queue/services/main'
import { DateTime } from 'luxon'
import {
  createCampaignValidator,
  filterCampaignValidator,
} from '../../validators/campaign/campaign_validator.js'

export default class AdminCampaignController {
  private campaignService = new CampaignService()
  /**
   * @index
   * @tag Admin Campaign
   * @summary Read all campaigns
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @responseBody 200 - <ZnCampaign[]>.append("id":"").paginated() - Read all campaigns descriptively
   */
  /**
   * Display a list of resource
   */
  async index({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.CAMPAIGN)
    try {
      const { page = 1, limit = 10, search } = request.qs()

      const query = ZnCampaign.query()

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder
            .whereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(description) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(resourceId) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(url) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(sendType) LIKE LOWER(?)', [`%${search}%`])
        })
      }

      query.orderBy('createdAt', 'desc')

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @filter
   * @tag Admin Campaign
   * @summary Filter all campaigns
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @responseBody 200 - <ZnCampaign[]>.append("id":"").paginated() - Filter all campaigns descriptively
   */
  async filter({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.CAMPAIGN)

    const data = request.body()
    const payload = await filterCampaignValidator.validate(data)

    const { page = 1, limit = 10 } = request.qs()

    try {
      const query = ZnCampaign.query()

      if (payload.search) {
        query.whereRaw(`LOWER(${payload.search.field}) LIKE LOWER(?)`, [`%${payload.search.term}%`])
      }

      // if (filter) {
      //   filter.map((fil: any) => {
      //     if (typeof fil == typeof []) {
      //       fil = fil.join("")
      //     }

      //     const name = fil.split("=")[0]
      //     const value = fil.split("=")[1]

      //     if (name == "startAtFrom") {
      //       query.whereRaw(`startAt >= "${DateTime.fromJSDate(new Date(value)).toUTC().toJSDate().toISOString()}"`)
      //       return
      //     }

      //     if (name == "startAtTo") {
      //       query.whereRaw(`startAt <= "${DateTime.fromJSDate(new Date(value)).toUTC().toJSDate().toISOString()}"`)
      //       return
      //     }

      //     query.where(name, value)
      //   })
      // }

      if (payload.sort) {
        query.orderBy(payload.sort.field, payload.sort.direction)
      }

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @store
   * @tag Admin Campaign
   * @summary Create action
   * @requestBody <ZnCampaign>
   * @responseBody 201 - <ZnCampaign>.append("id":"","createdAt":"","updatedAt":"") - Create action descriptively
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The name field must be defined","rule":"required","field":"name"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Handle form submission for the create action
   */
  async store({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.CAMPAIGN)

    const data = request.all()

    const payload = await createCampaignValidator.validate(data)

    // if (!payload.isDraft) {
    //   await bouncer.authorize("authorization", ACTION.ACTIVE, RESOURCE.CAMPAIGN)
    // }

    try {
      const created = await ZnCampaign.create({
        name: payload.name,
        description: payload.description || '',
        type: payload.type,

        resourceId: payload.resourceId ?? null,
        resourceTable: payload.resourceTable ?? null,
        url: payload.url ?? null,
        isPublished: payload.isPublished,

        status: ECampaignStatus.Deactive,
        sendType: payload.sendType,
        isDraft: payload.isDraft,
      })

      if (payload.users) {
        const campaignUsers = await this.campaignService.getCampaignUsers({
          ...payload.users,
        })
        await created.related('users').sync(campaignUsers.map((user) => user.id))
      }

      created.sms = payload.sms || null
      created.mailName = payload.mailName || null

      if (created.sendType === ECampaignSendType.Instant) {
        if (!payload.isDraft) {
          created.startAt = DateTime.local().toJSDate() as any
          created.status = ECampaignStatus.Sent

          // await this.campaignService.sendCampaign({
          //   campaign: created,
          //   userIds: payload.userIds,
          // })

          // if (payload.sms) {
          //   await this.campaignService.sendSMS({
          //     campaign: created,
          //     message: payload.sms,
          //     userIds: payload.userIds
          //   })
          // }

          // if (payload.mailName) {
          //   await this.campaignService.sendMail({
          //     campaign: created,
          //     mailName: payload.mailName,
          //     userIds: payload.userIds
          //   })
          // }

          await created.save()

          await queue.dispatch(SendCampaignNotificationJob, { campaign: created })
          // setTimeout(() => {
          //   this.campaignService.sendNotification(created.id)
          // }, 200)
        }
      } else if (created.sendType === ECampaignSendType.Scheduled && payload.startAt) {
        if (!payload.isDraft) {
          if (DateTime.fromJSDate(payload.startAt).toUTC() <= DateTime.now().toUTC()) {
            return response.badRequest({ message: 'Start time must be in the future.' })
          }
          created.status = ECampaignStatus.Active
        }

        created.startAt = DateTime.fromJSDate(payload.startAt).toUTC().toJSDate() as any

        await created.save()
      }

      return response.created(created)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @show
   * @tag Admin Campaign
   * @summary Read a campaign
   * @paramPath id - ID of Campaign - @type(string) @required
   * @responseBody 200 - <ZnCampaign>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read a campaign descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Campaign not found"} - Not Found
   */
  /**
   * Show individual record
   */
  async show({ params, response }: HttpContext) {
    try {
      const campaignId = params.id
      const campaign = await ZnCampaign.query()
        .preload('collection')
        .preload('product')
        .preload('event')
        .where('id', campaignId)
        .first()

      if (!campaign) {
        return response.notFound({ message: 'Campaign not found' })
      }

      let mailTemplate = null
      if (campaign.mailName) {
        mailTemplate = {
          name: campaign.mailName,
          html: await this.campaignService.getMailTemplate(campaign.mailName),
        }
      }

      return response.ok({
        ...campaign?.serialize(),
        mailTemplate,
      })
    } catch (error) {
      return response.internalServerError(error)
    }
  }

  /**
   * @update
   * @tag Admin Campaign
   * @summary Update campaign info
   * @description Update campaign info descriptively
   * @paramPath id - ID of Campaign - @type(string) @required
   * @requestBody <ZnCampaign>
   * @responseBody 200 - <ZnCampaign>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The name field must be defined","rule":"required","field":"name"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Campaign not found"} - Not Found
   */
  /**
   * Handle form submission for the edit action
   */
  async update({ bouncer, request, response, params }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.CAMPAIGN)

    const campaignId = params.id
    const campaign = await ZnCampaign.find(campaignId)
    if (!campaign) {
      return response.notFound({ message: 'Campaign not found' })
    }

    if (campaign.status === ECampaignStatus.Sent) {
      return response.badRequest({ message: 'Cannot edit a sent campaign.' })
    }

    const data = request.all()

    const payload = await createCampaignValidator.validate(data)

    try {
      campaign.name = payload.name || campaign.name
      campaign.description = payload.description || campaign.description
      campaign.type = payload.type || campaign.type

      campaign.resourceId = payload.resourceId || null
      campaign.resourceTable = payload.resourceTable || null
      campaign.url = payload.url || campaign.url
      campaign.isPublished = payload.isPublished

      if (payload.isDraft) {
        // await this.campaignService.clearJobs(campaign)
        campaign.status = ECampaignStatus.Deactive
        campaign.isDraft = true
        campaign.startAt = null
        campaign.sendType = null
      }

      campaign.sendType = payload.sendType || campaign.sendType

      if (payload.users) {
        const campaignUsers = await this.campaignService.getCampaignUsers({
          ...payload.users,
          campaignId: payload.users.clear ? undefined : campaign.id,
        })
        await campaign.related('users').sync(campaignUsers.map((user) => user.id))
      } else {
        await campaign.related('users').sync([])
      }

      campaign.sms = payload.sms || null
      campaign.mailName = payload.mailName || null

      switch (payload.sendType) {
        case ECampaignSendType.Instant: {
          // campaign.sendType = payload.sendType

          if (!payload.isDraft) {
            campaign.startAt = DateTime.local().toJSDate() as any
            campaign.status = ECampaignStatus.Sent
            campaign.isDraft = false

            // await this.campaignService.clearJobs(campaign)

            // await this.campaignService.sendNotification(campaign.id)
            await queue.dispatch(SendCampaignNotificationJob, { campaign })
            // await this.campaignService.sendCampaign({
            //   campaign,
            //   userIds: payload.userIds,
            // })

            // if (payload.sms) {
            //   await this.campaignService.sendSMS({
            //     campaign,
            //     message: payload.sms,
            //     userIds: payload.userIds
            //   })
            // }

            // if (payload.mailName) {
            //   await this.campaignService.sendMail({
            //     campaign,
            //     mailName: payload.mailName,
            //     userIds: payload.userIds
            //   })
            // }
          }
          break
        }
        case ECampaignSendType.Scheduled: {
          // const { startAt } = await activeCampaignValidator.validate(data)

          if (!payload.isDraft) {
            campaign.status = ECampaignStatus.Active
            campaign.isDraft = false
            if (DateTime.fromJSDate(payload.startAt!).toUTC() <= DateTime.now().toUTC()) {
              return response.badRequest({ message: 'Start time must be in the future.' })
            }
          }

          campaign.startAt = DateTime.fromJSDate(payload.startAt!).toUTC().toJSDate() as any
          // campaign.sendType = payload.sendType

          break
        }
        default: {
          break
        }
      }

      const updated = await campaign.save()

      return response.ok(updated)
    } catch (error) {
      console.log(error)

      return response.badRequest(error)
    }
  }

  /**
   * @destroy
   * @tag Admin Campaign
   * @summary Soft-delete a campaign
   * @description Soft-delete a campaign descriptively
   * @paramPath id - ID of Campaign - @type(string) @required
   * @responseBody 200 - {"message":"Campaign soft-deleted successfully"} - OK
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Campaign not found"} - Not Found
   */
  /**
   * Delete record
   */
  async destroy({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.CAMPAIGN)

    const campaignId = params.id
    const campaign = await ZnCampaign.find(campaignId)
    if (!campaign) {
      response.notFound({ message: 'Campaign not found' })
    }

    // if (campaign.status === 'sent') {
    //   return response.badRequest({ message: 'Cannot delete a sent campaign.' })
    // }

    try {
      // await this.campaignService.clearJobs(campaign)

      await campaign?.softDelete()
      return response.ok({ message: 'Campaign soft-deleted successfully' })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @publish
   * @tag Admin Campaign
   * @summary Toggle publish a campaign
   * @description Toggle publish a campaign descriptively
   * @paramPath id - ID of Campaign - @type(string) @required
   * @responseBody 200 - {"message":"Campaign Toggle publish successfully"} - OK
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Campaign not found"} - Not Found
   */
  /**
   * Toggle publish
   */
  async publish({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.CAMPAIGN)

    const campaignId = params.id
    const campaign = await ZnCampaign.find(campaignId)
    if (!campaign) {
      return response.notFound({ message: 'Campaign not found' })
    }

    try {
      await campaign.merge({ isPublished: !campaign.isPublished }).save()
      return response.ok({ message: 'Campaign soft-deleted successfully' })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @listUser
   * @tag Admin Campaign
   * @summary List users based on query
   * @description List users based on query descriptively
   * @paramQuery tags - Tags - @type(string)
   * @responseBody 200 - <ZnUser[]> - OK
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Delete record
   */
  async listUser({ request, response }: HttpContext) {
    const {
      page = 1,
      limit = 10,
      campaignId,
      includedIds = [],
      excludedIds = [],
      countryIds = [],
      stateIds = [],
      cityIds = [],
      tags = [],
    } = request.qs()

    try {
      // if (!tags) { return response.ok([]) }
      let productTags: string[] = []
      if (typeof tags == 'string') {
        productTags = tags.split(',')
      } else if (Array.isArray(tags)) {
        productTags = tags
      }
      const result = await this.campaignService.getCampaignUsers(
        {
          campaignId,
          includedIds,
          excludedIds,
          countryIds,
          stateIds,
          cityIds,
          tags: productTags,
        },
        {
          page,
          limit,
        }
      )

      return response.ok(result)
    } catch (error) {
      console.log(error)

      return response.badRequest(error)
    }
  }

  /**
   * @selectMailTemplates
   * @tag Admin Campaign
   * @summary Select users based on query
   * @description Select users based on query descriptively
   * @responseBody 200 - <ZnUser[]> - OK
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Delete record
   */
  async selectMailTemplates({ request, response }: HttpContext) {
    const { page = 1, search } = request.qs()

    try {
      const result = await this.campaignService.listMailTemplates({ page, search })

      return response.ok(result)
    } catch (error) {
      console.log(error)

      return response.internalServerError(error)
    }
  }
}
