import { MEDIA_TYPE } from '#constants/media'
import ZnHomeLayoutTopCategories from '#models/zn_home_layout_top_categories'
import { ACTION, RESOURCE } from '../../../app/constants/authorization.js'
// import { HomeLayoutService } from '#services/firebase/home_layout_service'
import { uploadMediasFromLinks } from '../../../services/media/index.js'
import { createHomeLayoutTopCategoriesValidator } from '../../validators/home-layout/home-layout.js'
import { HttpContext } from '@adonisjs/core/http'

export default class AdminHomeLayoutTopCategoriesController {
  // private homeLayoutService = new HomeLayoutService()

  /**
   * @create
   * @tag Admin Top-Categories
   * @summary Create a home layout top category
   * @requestBody <ZnHomeLayoutTopCategories>
   * @responseBody 201 - <ZnHomeLayoutTopCategories>.append("id":"","createdAt":"","updatedAt":"") - Create a home layout top category descriptively
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The uri field must be defined","rule":"required","field":"uri"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  public async create({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.TOP_CATEGORIES)

    const data = request.all()

    const payload = await createHomeLayoutTopCategoriesValidator.validate(data)

    try {
      let image
      if (payload.imageUrl) {
        const images = await uploadMediasFromLinks([payload.imageUrl], MEDIA_TYPE.IMAGE)
        image = images[0]
      }
      const created = await ZnHomeLayoutTopCategories.create({
        name: payload.name,
        collectionId: payload.collectionId,
        imageId: payload.imageId || image?.id,
      })

      // this.homeLayoutService.writeTopCategory(created)

      return response.created(created)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
        error: error,
      })
    }
  }

  /**
   * @list
   * @tag Admin Top-Categories
   * @summary Read all home layout top categories
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @responseBody 200 - <ZnHomeLayoutTopCategories[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null).paginated() - Read all home layout top categories descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  async list({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.TOP_CATEGORIES)

    try {
      const { page = 1, limit = 10 } = request.qs()

      const query = ZnHomeLayoutTopCategories.query()
        .whereNull('deletedAt')
        .preload('image')
        .preload('collection')

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @show
   * @tag Admin Top-Categories
   * @summary Read a home layout top category
   * @paramPath id - ID of Home-Layout Top-Categories - @type(string) @required
   * @responseBody 200 - <ZnHomeLayoutTopCategories>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read a home layout top category descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Home layout top categories not found"} - Not Found
   */
  public async show({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.TOP_CATEGORIES)

    try {
      const homeLayoutTopCategoriesId = params.id

      const homeLayoutTopCategories = await ZnHomeLayoutTopCategories.query()
        .preload('image')
        .preload('collection')
        .where('id', homeLayoutTopCategoriesId)
        .first()

      if (!homeLayoutTopCategories) {
        return response.notFound({ message: 'Home layout top categories not found' })
      }

      return response.ok(homeLayoutTopCategories)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @update
   * @tag Admin Top-Categories
   * @summary Update a home layout top category
   * @description Update a home layout top category descriptively
   * @paramPath id - ID of Home-Layout Top-Categories - @type(string) @required
   * @requestBody <ZnHomeLayoutTopCategories>
   * @responseBody 200 - <ZnHomeLayoutTopCategories>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The uri field must be defined","rule":"required","field":"uri"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Home layout top categories not found"} - Not Found
   */
  public async update({ bouncer, request, response, params }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.TOP_CATEGORIES)

    const homeLayoutTopCategories = await this.homeLayoutTopCategoriesIfExists(params, response)
    if (!homeLayoutTopCategories) {
      return
    }

    const data = request.all()

    const payload = await createHomeLayoutTopCategoriesValidator.validate(data)

    try {
      homeLayoutTopCategories.name = payload.name || homeLayoutTopCategories.name
      homeLayoutTopCategories.collectionId =
        payload.collectionId || homeLayoutTopCategories.collectionId

      let image
      if (payload.imageUrl) {
        const images = await uploadMediasFromLinks([payload.imageUrl], MEDIA_TYPE.IMAGE)
        image = images[0]
      }
      homeLayoutTopCategories.imageId =
        payload.imageId || image?.id || homeLayoutTopCategories.imageId

      const updated = await homeLayoutTopCategories.save()

      // this.homeLayoutService.writeTopCategory(updated)

      return response.ok(updated)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @softDelete
   * @tag Admin Top-Categories
   * @summary Soft-delete a home layout top category
   * @description Soft-delete a home layout top category descriptively
   * @paramPath id - ID of Home-Layout Top-Categories - @type(string) @required
   * @responseBody 200 - {"message":"Home layout top categories soft-deleted successfully"}
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Home layout top categories not found"} - Not Found
   */
  public async softDelete({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.TOP_CATEGORIES)

    try {
      const homeLayoutTopCategories = await this.homeLayoutTopCategoriesIfExists(params, response)
      if (!homeLayoutTopCategories) {
        return
      }

      await homeLayoutTopCategories?.softDelete()

      // this.homeLayoutService.deleteTopCategory(homeLayoutTopCategories)

      return response.ok({ message: 'Home layout top categories soft-deleted successfully' })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  private async homeLayoutTopCategoriesIfExists(
    params: HttpContext['params'],
    response: HttpContext['response']
  ) {
    const homeLayoutTopCategoriesId = params.id

    const homeLayoutTopCategories = await ZnHomeLayoutTopCategories.query()
      .where('id', homeLayoutTopCategoriesId)
      .first()

    if (!homeLayoutTopCategories) {
      response.notFound({ message: 'Home layout top categories not found' })
    }

    return homeLayoutTopCategories
  }
}
