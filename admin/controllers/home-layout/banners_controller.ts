import ZnHomeLayoutBanners from '#models/zn_home_layout_banners'
import { createHomeLayoutBannersValidator } from '../../validators/home-layout/home-layout.js'
import { HttpContext } from '@adonisjs/core/http'
import { ACTION, RESOURCE } from '../../../app/constants/authorization.js'
import { uploadMediasFromLinks } from '../../../services/media/index.js'
import { MEDIA_TYPE } from '#constants/media'

export default class AdminHomeLayoutBannersController {
  // private homeLayoutService = new HomeLayoutService()

  /**
   * @index
   * @tag Admin Banners
   * @summary Read all home layout banners
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @responseBody 200 - <ZnHomeLayoutBanners[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null).with(product, collection).paginated() - Read all home layout banners descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display a list of resource
   */
  async index({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.BANNER)

    try {
      const { page = 1, limit = 10, search } = request.qs()

      const query = ZnHomeLayoutBanners.query().whereNull('deletedAt').preload('media')

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder
            .where('bottomSubtitle', 'like', `%${search}%`)
            .orWhere('bottomTitle', 'like', `%${search}%`)
            .orWhere('discount', 'like', `%${search}%`)
            .orWhere('discountName', 'like', `%${search}%`)
        })
      }

      query.orderBy('orderBy', 'asc')

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @create
   * @tag Admin Banners
   * @summary Return data to create
   */
  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {}

  /**
   * @store
   * @tag Admin Banners
   * @summary Create a home layout banner
   * @requestBody <ZnHomeLayoutBanners>
   * @responseBody 201 - <ZnHomeLayoutBanners>.append("id":"","createdAt":"","updatedAt":"") - Create a home layout banner descriptively
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The value field must be defined","rule":"required","field":"value"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Handle form submission for the create action
   */
  public async store({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.BANNER)

    const data = request.all()

    const payload = await createHomeLayoutBannersValidator.validate(data)

    try {
      let media
      if (payload.mediaUrl) {
        const medias = await uploadMediasFromLinks([payload.mediaUrl], MEDIA_TYPE.IMAGE)
        if (medias.length > 0) {
          media = medias[0]
        }
      }
      const created = await ZnHomeLayoutBanners.create({
        mediaId: media?.id ?? payload.mediaId ?? undefined,
        bottomTitle: payload.bottomTitle,
        bottomSubtitle: payload.bottomSubtitle,
        discount: payload.discount,
        discountName: payload.discountName,
        resourceId: payload.resourceId,
        orderBy: payload.orderBy,
      })

      switch (payload.resourceType) {
        case 'product': {
          created.url = null
          created.productId = payload.productId || created.productId
          created.collectionId = null
          break
        }
        case 'collection': {
          created.url = payload.url || ''
          created.collectionId = payload.collectionId || created.collectionId
          created.productId = null
          break
        }
        default: {
          created.url = payload.url || ''
          created.productId = null
          created.collectionId = null
        }
      }

      const newCreated = await created.save()

      // this.homeLayoutService.writeBanner(created)

      return response.created(newCreated)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
        error: error,
      })
    }
  }

  /**
   * @show
   * @tag Admin Banners
   * @summary Read a home layout banner
   * @paramPath id - ID of Home-Layout Banners - @type(string) @required
   * @responseBody 200 - <ZnHomeLayoutBanners>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read a home layout banner descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Home layout banner not found"} - Not Found
   */
  /**
   * Show individual record
   */
  public async show({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.BANNER)

    try {
      const homeLayoutBannerId = params.id

      const homeLayoutBanner = await ZnHomeLayoutBanners.query()
        .preload('media')
        .preload('product')
        .preload('collection')
        .where('id', homeLayoutBannerId)
        .first()

      if (!homeLayoutBanner) {
        return response.notFound({ message: 'Home layout banner not found' })
      }

      // console.log(homeLayoutBanner.resource);

      return response.ok(homeLayoutBanner)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @edit
   * @tag Admin Banners
   * @summary Return data to update
   */
  /**
   * Edit individual record
   */
  async edit({}: HttpContext) {}

  /**
   * @update
   * @tag Admin Banners
   * @summary Update a home layout banner
   * @description Update a home layout banner descriptively
   * @paramPath id - ID of Home-Layout Banners - @type(string) @required
   * @requestBody <ZnHomeLayoutBanners>
   * @responseBody 200 - <ZnHomeLayoutBanners>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The value field must be defined","rule":"required","field":"value"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Home layout banner not found"} - Not Found
   */
  /**
   * Handle form submission for the edit action
   */
  public async update({ bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.BANNER)

    const homeLayoutBanner = await this.homeLayoutBannerIfExists(params, response)
    if (!homeLayoutBanner) {
      return
    }

    const data = request.all()

    const payload = await createHomeLayoutBannersValidator.validate(data)

    try {
      homeLayoutBanner.bottomTitle = payload.bottomTitle
      homeLayoutBanner.bottomSubtitle = payload.bottomSubtitle || ''
      homeLayoutBanner.discount = payload.discount || ''
      homeLayoutBanner.discountName = payload.discountName || ''
      // homeLayoutBanner.media = payload.media
      homeLayoutBanner.resourceId = payload.resourceId || ''
      homeLayoutBanner.url = payload.url || ''
      homeLayoutBanner.orderBy = payload.orderBy ?? null

      let media
      if (payload.mediaUrl) {
        const thumbnails = await uploadMediasFromLinks([payload.mediaUrl], MEDIA_TYPE.IMAGE)
        if (thumbnails.length > 0) {
          media = thumbnails[0]
        }
      }

      homeLayoutBanner.mediaId = media?.id || payload.mediaId || homeLayoutBanner.mediaId

      switch (payload.resourceType) {
        case 'product': {
          homeLayoutBanner.url = null
          homeLayoutBanner.productId = payload.productId || homeLayoutBanner.productId
          homeLayoutBanner.collectionId = null
          break
        }
        case 'collection': {
          homeLayoutBanner.url = null
          homeLayoutBanner.collectionId = payload.collectionId || homeLayoutBanner.collectionId
          homeLayoutBanner.productId = null
          break
        }
        default: {
          homeLayoutBanner.url = payload.url || ''
          homeLayoutBanner.productId = null
          homeLayoutBanner.collectionId = null
        }
      }

      const updated = await homeLayoutBanner.save()

      // this.homeLayoutService.writeBanner(updated)

      return response.ok(updated)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
        error: error,
      })
    }
  }

  /**
   * @destroy
   * @tag Admin Banners
   * @summary Soft-delete a home layout banner
   * @description Soft-delete a home layout banner descriptively
   * @paramPath id - ID of Home-Layout Banners - @type(string) @required
   * @responseBody 200 - {"message":"Home layout banner soft-deleted successfully"}
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Home layout banner not found"} - Not Found
   */
  public async destroy({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.BANNER)

    try {
      const homeLayoutBanner = await this.homeLayoutBannerIfExists(params, response)
      if (!homeLayoutBanner) {
        return
      }

      await homeLayoutBanner.softDelete()

      // this.homeLayoutService.deleteBanner(homeLayoutBanner)

      return response.ok({ message: 'Home layout banner soft-deleted successfully' })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  private async homeLayoutBannerIfExists(
    params: HttpContext['params'],
    response: HttpContext['response']
  ) {
    const homeLayoutBannerId = params.id

    const homeLayoutBanner = await ZnHomeLayoutBanners.query()
      .where('id', homeLayoutBannerId)
      .first()

    if (!homeLayoutBanner) {
      response.notFound({ message: 'Home layout banner not found' })
    }

    return homeLayoutBanner
  }
}
