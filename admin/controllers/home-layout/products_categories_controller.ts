import { MEDIA_TYPE } from '#constants/media'
import ZnHomeLayoutProductsCategories from '#models/zn_home_layout_products_categories'
import { ACTION, RESOURCE } from '../../../app/constants/authorization.js'
// import { HomeLayoutService } from '#services/firebase/home_layout_service'
import { uploadMediasFromLinks } from '../../../services/media/index.js'
import { createHomeLayoutProductsCategoriesValidator } from '../../validators/home-layout/home-layout.js'
import { HttpContext } from '@adonisjs/core/http'

export default class AdminHomeLayoutProductsCategoriesController {
  // private homeLayoutService = new HomeLayoutService()

  /**
   * @create
   * @tag Admin Products-Categories
   * @summary Create a home layout products category
   * @requestBody <ZnHomeLayoutProductsCategories>
   * @responseBody 201 - <ZnHomeLayoutProductsCategories>.append("id":"","createdAt":"","updatedAt":"") - Create a home layout products category descriptively
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The name field must be defined","rule":"required","field":"name"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  public async create({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.PRODUCTS_CATEGORIES)

    const data = request.all()

    const payload = await createHomeLayoutProductsCategoriesValidator.validate(data)

    try {
      let image
      if (payload.imageUrl) {
        const images = await uploadMediasFromLinks([payload.imageUrl], MEDIA_TYPE.IMAGE)
        image = images[0]
      }

      const created = await ZnHomeLayoutProductsCategories.create({
        name: payload.name,
        imageId: payload.imageId || image?.id,
        collectionId: payload.collectionId,
      })

      // this.homeLayoutService.writeProductsCategory(created)

      return response.created(created)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
        error: error,
      })
    }
  }

  /**
   * @list
   * @tag Admin Products-Categories
   * @summary Read all home layout products categories
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @responseBody 200 - <ZnHomeLayoutProductsCategories[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null).paginated() - Read all home layout products categories descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  async list({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.PRODUCTS_CATEGORIES)

    try {
      const { page = 1, limit = 10 } = request.qs()

      const query = ZnHomeLayoutProductsCategories.query()
        .whereNull('deletedAt')
        .preload('image')
        .preload('collection')

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @show
   * @tag Admin Products-Categories
   * @summary Read a home layout products category
   * @paramPath id - ID of Home-Layout Products-Categories - @type(string) @required
   * @responseBody 200 - <ZnHomeLayoutProductsCategories>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read a home layout products category descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Home layout products categories not found"} - Not Found
   */
  public async show({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.PRODUCTS_CATEGORIES)

    try {
      const homeLayoutProductsCategoriesId = params.id

      const homeLayoutProductsCategories = await ZnHomeLayoutProductsCategories.query()
        .preload('image')
        .preload('collection')
        .where('id', homeLayoutProductsCategoriesId)
        .first()

      if (!homeLayoutProductsCategories) {
        return response.notFound({ message: 'Home layout products categories not found' })
      }

      return response.ok(homeLayoutProductsCategories)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @update
   * @tag Admin Products-Categories
   * @summary Update a home layout products category
   * @description Update a home layout products category descriptively
   * @paramPath id - ID of Home-Layout Products-Categories - @type(string) @required
   * @requestBody <ZnHomeLayoutProductsCategories>
   * @responseBody 200 - <ZnHomeLayoutProductsCategories>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The name field must be defined","rule":"required","field":"name"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Home layout products categories not found"} - Not Found
   */
  public async update({ bouncer, request, response, params }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.PRODUCTS_CATEGORIES)

    const homeLayoutProductsCategories = await this.homeLayoutProductsCategoriesIfExists(
      params,
      response
    )
    if (!homeLayoutProductsCategories) {
      return
    }

    const data = request.all()

    const payload = await createHomeLayoutProductsCategoriesValidator.validate(data)

    try {
      homeLayoutProductsCategories.name = payload.name || homeLayoutProductsCategories.name
      homeLayoutProductsCategories.collectionId =
        payload.collectionId || homeLayoutProductsCategories.collectionId

      let image
      if (payload.imageUrl) {
        const images = await uploadMediasFromLinks([payload.imageUrl], MEDIA_TYPE.IMAGE)
        image = images[0]
      }
      homeLayoutProductsCategories.imageId =
        payload.imageId || image?.id || homeLayoutProductsCategories.imageId

      const updated = await homeLayoutProductsCategories.save()

      // this.homeLayoutService.writeProductsCategory(updated)

      return response.ok(updated)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @softDelete
   * @tag Admin Products-Categories
   * @summary Soft-delete a home layout products category
   * @description Soft-delete a home layout products category descriptively
   * @paramPath id - ID of Home-Layout Products-Categories - @type(string) @required
   * @responseBody 200 - {"message":"Home layout products categories soft-deleted successfully"}
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Home layout products categories not found"} - Not Found
   */
  public async softDelete({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.PRODUCTS_CATEGORIES)

    try {
      const homeLayoutProductsCategories = await this.homeLayoutProductsCategoriesIfExists(
        params,
        response
      )
      if (!homeLayoutProductsCategories) {
        return
      }

      await homeLayoutProductsCategories?.softDelete()

      // this.homeLayoutService.deleteProductsCategory(homeLayoutProductsCategories)

      return response.ok({ message: 'Home layout products categories soft-deleted successfully' })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  private async homeLayoutProductsCategoriesIfExists(
    params: HttpContext['params'],
    response: HttpContext['response']
  ) {
    const homeLayoutProductsCategoriesId = params.id

    const homeLayoutProductsCategories = await ZnHomeLayoutProductsCategories.query()
      .where('id', homeLayoutProductsCategoriesId)
      .first()

    if (!homeLayoutProductsCategories) {
      response.notFound({ message: 'Home layout products categories not found' })
    }

    return homeLayoutProductsCategories
  }
}
