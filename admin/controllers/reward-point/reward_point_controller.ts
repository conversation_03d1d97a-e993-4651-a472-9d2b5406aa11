import ZnRewardActivity from '#models/zn_reward_activity';
import { HttpContext } from '@adonisjs/core/http';

export default class AdminRewardPointController {
  public async getActivities({ request, response }: HttpContext) {
    try {
      const {
        page = 1,
        limit = 10,
      } = request.all()

      const query = ZnRewardActivity.query()
        .preload('user')
        .orderBy('createdAt', 'desc')

      const result = await query.paginate(page, limit)

      return response.ok(result)

    } catch (error) {
      console.log(error);
      return response.internalServerError("Something went wrong!")
    }
  }

}