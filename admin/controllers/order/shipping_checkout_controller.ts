import ZnAdmin from "#models/zn_admin";
import ZnProductVariant from "#models/zn_product_variant";
import ZnShippingCheckout, { EShippingCheckoutStatus } from "#models/zn_shipping_checkout";
import ZnShippingCheckoutItem, { EShippingCheckoutItemScanType } from "#models/zn_shipping_checkout_item";
import { HttpContext } from "@adonisjs/core/http";
import { DateTime } from "luxon";
import { ShippingCheckoutService } from "../../services/order/shipping_checkout_service.js";
import { passBundleShippingCheckoutValidator, scanItemShippingCheckoutValidator, updateShippingCheckoutValidator } from "../../validators/order/shipping_checkout_validator.js";
import { SHIPPING_CHECKOUT_TEXT } from "./shipping_checkout_constants.js";
import mail from "@adonisjs/mail/services/main";
import InventoryCheckoutReportNotification from "../../../app/mails/report/inventory_checkout_report_notification.js";

export default class AdmiShippingCheckoutController {
  private shippingCheckoutService = new ShippingCheckoutService()
  /**
   * @showFulfilOrder
   * @tag Admin Shipping Checkout
   * @summary Show order from Fulfil
   * @paramQuery fulfilNumber - Fulfil number - @type(string) @required
   * @responseBody 200 - {"meta":{"checkoutId":""},"data":[{"id":"","count":0,"quantity":1,"barcode":"","sku":"","variantId":"","variant":"<ZnProductVariant>"}]} - Ok
   */
  /**
   * Show individual record
   */
  async showFulfilOrder({ auth, request, response }: HttpContext) {
    try {
      const { fulfilNumber } = request.qs()


      if (!fulfilNumber) {
        return response.badRequest(SHIPPING_CHECKOUT_TEXT.SHIPPING_CHECKOUT_NUMBER_MISSING)
      }

      const cleanFulfilNumber = fulfilNumber.trim().toUpperCase()

      const data = await this.shippingCheckoutService.getOrderLinesByFulfilNumber(cleanFulfilNumber)

      if (!data?.[0]) {
        return response.notFound(SHIPPING_CHECKOUT_TEXT.SHIPPING_CHECKOUT_ORDER_NOT_FOUND)
      }

      const hasFinished = await ZnShippingCheckout.query()
        .where({
          fulfilNumber: cleanFulfilNumber,
          status: EShippingCheckoutStatus.FINISH
        })
        .first()

      const admin = auth.getUserOrFail() as ZnAdmin
      const checkout = await ZnShippingCheckout.create({
        fulfilNumber: cleanFulfilNumber,
        adminId: admin.id,
        status: EShippingCheckoutStatus.SHOW,
      })

      const variantSkus: string[] = []

      for (const datum of data) {
        if (datum.sku) {
          variantSkus.push(datum.sku)
        }

        // for (const dati of datum.subProducts) {
        //   if (dati.sku) {
        //     variantSkus.push(dati.sku)
        //   }
        // }
      }

      const variants = await ZnProductVariant.query()
        .whereIn('sku', variantSkus)
        .preload('image')
        .preload('product')
        .preload('optionValues', (optionValueQuery) => {
          optionValueQuery.preload('option')
        })

      const noBarcodeItems: any[] = []
      const orderLines = data.map(datum => {
        const variant = variants.find(vari => vari.sku == datum.sku)?.serialize()

        // const barcode = datum.codes.find((code: any) => code.type == 'upc')?.code

        const item = {
          barcode: datum.barcode,
          sku: datum.sku,
          variantId: variant?.id,
          scanType: EShippingCheckoutItemScanType.ERROR,
          reason: "No Barcode",
          adminId: admin.id
        }

        if (!datum.barcode) { noBarcodeItems.push(item) }

        // const subProducts = datum.subProducts.map((dati: any) => {
        //   const subVariant = variants.find(vari => vari.sku == dati.sku)?.serialize()

        //   const subItem = {
        //     barcode: dati.barcode,
        //     sku: dati.sku,
        //     variantId: subVariant?.id,
        //     scanType: EShippingCheckoutItemScanType.ERROR,
        //     reason: "No Barcode",
        //     adminId: admin.id
        //   }

        //   if (!dati.barcode) { noBarcodeItems.push(subItem) }

        //   return {
        //     ...subItem,
        //     id: dati.id,
        //     count: 0,
        //     quantity: dati.quantity,
        //     productName: dati.productName,
        //     variantName: dati.variantName,
        //     variantId: subVariant?.id,
        //     variant: subVariant,
        //     reason: null,
        //     scanType: null,
        //   }
        // })

        return {
          ...item,
          id: datum.id,
          count: 0,
          quantity: datum.quantity,
          productName: datum.productName,
          variantName: datum.variantName,
          variantId: variant?.id,
          variant,
          // subItems: subProducts,
          reason: null,
          scanType: null,
          bundle: datum.bundle,
        }
      })

      for (const item of noBarcodeItems) {
        await ZnShippingCheckoutItem.create({
          checkoutId: checkout.id,
          ...item
        })
      }

      return response.ok({
        meta: {
          fulfilNumber,
          checkoutId: checkout.id,
          hasFinished: !!hasFinished,
        },
        data: orderLines
      })

    } catch (error) {
      console.log(error);
      return response.internalServerError({
        message: SHIPPING_CHECKOUT_TEXT.SHIPPING_CHECKOUT_ERROR,
        error
      })
    }
  }

  /**
   * @scanItem
   * @tag Admin Shipping Checkout
   * @summary Scan item
   * @description Scan item descriptively
   * @paramPath id - Id of order - @type(string) @required
   * @requestBody {"sku":"","barcode":""}
   * @responseBody 200 - <ZnShippingCheckoutItem> - Ok
   */
  /**
   * Scan Item
   */
  async scanItem({ auth, params, request, response }: HttpContext) {
    try {
      const checkoutId = params.id

      const checkout = await ZnShippingCheckout.find(checkoutId)
      if (!checkout) { return response.notFound(SHIPPING_CHECKOUT_TEXT.SHIPPING_CHECKOUT_NOT_FOUND) }

      const data = request.body()
      const payload = await scanItemShippingCheckoutValidator.validate(data)

      const items: ZnShippingCheckoutItem[] = []

      const scanType = payload.scanType || EShippingCheckoutItemScanType.SCAN
      const reason = payload.reason

      let admin = auth.getUserOrFail() as ZnAdmin
      if (payload.adminId) {
        const scannedAdmin = await ZnAdmin.find(payload.adminId)
        if (!scannedAdmin) {
          return response.badRequest(SHIPPING_CHECKOUT_TEXT.CHECKIN_INVALID)
        }
        admin = scannedAdmin
      }
      const adminId = admin?.id

      if (scanType == EShippingCheckoutItemScanType.PASS) {
        const existingErrorItem = await ZnShippingCheckoutItem.query()
          .where({
            checkoutId,
            sku: payload.sku,
            scanType: EShippingCheckoutItemScanType.ERROR,
            reason: payload.reason,
          })
          .first()

        if (!existingErrorItem) {
          await ZnShippingCheckoutItem.create({
            checkoutId,
            sku: payload.sku,
            barcode: payload.barcode,
            variantId: payload.variantId,
            scanType: EShippingCheckoutItemScanType.ERROR,
            reason,
            adminId
          })
        }
      } else if (scanType == EShippingCheckoutItemScanType.ERROR) {
        const item = await this.shippingCheckoutService.getProductBySkuOrBarcode(payload.sku || payload.barcode)

        const existingErrorItem = await ZnShippingCheckoutItem.query()
          .where({
            checkoutId,
            barcode: item?.barcode || payload.barcode || "",
            scanType: EShippingCheckoutItemScanType.ERROR,
            reason: payload.reason,
          })
          .first()

        if (!existingErrorItem) {
          const variant = await ZnProductVariant.findBy({ sku: item?.sku || payload.sku })

          await ZnShippingCheckoutItem.create({
            checkoutId,
            sku: item?.sku || payload.sku || null,
            barcode: item?.barcode || payload.barcode || null,
            variantId: variant?.id,
            scanType,
            reason,
            adminId
          })
        }

      }

      const count = payload.count || 0
      let loop = 1
      while (loop <= count) {
        const item = await ZnShippingCheckoutItem.create({
          checkoutId,
          sku: payload.sku,
          barcode: payload.barcode,
          variantId: payload.variantId,
          scanType,
          reason,
          adminId
        })
        items.push(item)

        loop++
      }

      checkout.status = EShippingCheckoutStatus.START

      await checkout.save()

      return response.created(items)

    } catch (error) {
      console.log(error);
      return response.badRequest({
        message: SHIPPING_CHECKOUT_TEXT.SHIPPING_CHECKOUT_ERROR,
        error
      })
    }
  }

  /**
   * @passBundle
   * @tag Admin Shipping Checkout
   * @summary Pass item
   * @description Pass item descriptively
   * @paramPath id - Id of order - @type(string) @required
   * @requestBody {"sku":"","barcode":""}
   * @responseBody 200 - <ZnShippingCheckoutItem> - Ok
   */
  /**
   * Scan Item
   */
  async passBundle({ auth, params, request, response }: HttpContext) {
    try {
      const checkoutId = params.id

      const checkout = await ZnShippingCheckout.find(checkoutId)
      if (!checkout) { return response.notFound(SHIPPING_CHECKOUT_TEXT.SHIPPING_CHECKOUT_NOT_FOUND) }

      const data = request.body()
      const payload = await passBundleShippingCheckoutValidator.validate(data)

      const scanType = EShippingCheckoutItemScanType.PASS

      let admin = auth.getUserOrFail() as ZnAdmin
      if (payload.adminId) {
        const scannedAdmin = await ZnAdmin.find(payload.adminId)
        if (!scannedAdmin) {
          return response.badRequest(SHIPPING_CHECKOUT_TEXT.CHECKIN_INVALID)
        }
        admin = scannedAdmin
      }
      const adminId = admin?.id

      const fulfilBundle = await this.shippingCheckoutService.getProductBySkuOrBarcode(payload.bundleSku)
      const dbBundle = await ZnProductVariant.findBy({ sku: payload.bundleSku })
      const errorBundleItem = {
        checkoutId,
        sku: payload.bundleSku,
        scanType: EShippingCheckoutItemScanType.ERROR,
        reason: "Bundle Not Open",
      }
      await ZnShippingCheckoutItem.updateOrCreate(
        errorBundleItem,
        {
          ...errorBundleItem,
          barcode: fulfilBundle?.barcode,
          variantId: dbBundle?.id,
          adminId,
        }
      )

      for (const item of payload.bundleItems) {

        await ZnShippingCheckoutItem.create({
          checkoutId,
          sku: item.sku,
          barcode: item.barcode,
          variantId: item.variantId,
          scanType: EShippingCheckoutItemScanType.ERROR,
          reason: "Part of Bundle",
          adminId
        })

        const count = item.count || 0
        let loop = 1
        while (loop <= count) {
          await ZnShippingCheckoutItem.create({
            checkoutId,
            sku: item.sku,
            barcode: item.barcode,
            variantId: item.variantId,
            scanType,
            reason: "Part of Bundle",
            adminId
          })

          loop++
        }
      }


      checkout.status = EShippingCheckoutStatus.START

      await checkout.save()

      return response.created('ok')

    } catch (error) {
      console.log(error);
      return response.badRequest({
        message: SHIPPING_CHECKOUT_TEXT.SHIPPING_CHECKOUT_ERROR,
        error
      })
    }
  }

  /**
   * @updateFulfilOrder
   * @tag Admin Shipping Checkout
   * @summary Update Shipping Checkout
   * @description Update Shipping Checkout descriptively
   * @paramPath id - Id of Shipping Checkout - @type(string) @required
   * @requestBody {"status":"cancel|finish"}
   * @responseBody 200 - <ZnShippingCheckout> - Ok
   */
  /**
   * Finish Shipping Checkout
   */
  async updateFulfilOrder({ params, request, response }: HttpContext) {
    try {
      const checkoutId = params.id

      const checkout = await ZnShippingCheckout.query()
        .where({ id: checkoutId })
        .first()

      if (!checkout) {
        return response.notFound(SHIPPING_CHECKOUT_TEXT.SHIPPING_CHECKOUT_NOT_FOUND)
      }

      const data = request.body()
      const payload = await updateShippingCheckoutValidator.validate(data)

      checkout.merge({
        endedAt: DateTime.now(),
        status: payload.status
      })
      await checkout.save()

    } catch (error) {
      console.log(error);
      return response.internalServerError({
        message: SHIPPING_CHECKOUT_TEXT.SHIPPING_CHECKOUT_ERROR,
        error
      })
    }
  }

  /**
   * @report
   * @tag Admin Shipping Checkout
   * @summary Report Shipping Checkout
   * @description Update Shipping Checkout descriptively
   * @requestBody {}
   * @responseBody 200 - {} - Ok
   */
  /**
   * Finish Shipping Checkout
   */
  async report({ auth, request, response }: HttpContext) {
    try {
      const { fulfilNumber, code } = request.body()

      const admin = auth.getUserOrFail() as ZnAdmin
      await mail.sendLater(new InventoryCheckoutReportNotification(
        fulfilNumber, code, admin
      )).catch((error) => {
        console.log(error);
      })

      return response.ok("OK")

    } catch (error) {
      console.log(error);
      return response.internalServerError({
        message: SHIPPING_CHECKOUT_TEXT.SHIPPING_CHECKOUT_ERROR,
        error
      })
    }
  }
}
