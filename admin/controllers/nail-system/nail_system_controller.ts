import { HttpContext } from '@adonisjs/core/http'
import ZnNailSystems from '#models/zn_nail_system'
import { NailSystemsService } from '#services/firebase/nail_system_service'
import { appCreateNailSystemsValidator } from '#validators/app/nail-systems/nail_systems'
import { ACTION, RESOURCE } from '../../../app/constants/authorization.js'

export default class NailSystemsController {
  private nailSystemsService = new NailSystemsService()

  /**
   * @index
   * @tag Admin Nail-Systems
   * @summary Read all nail systems
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @responseBody 200 - <ZnNailSystems[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null).paginated() - Read all nail systems descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display a list of resource
   */
  async index({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.NAIL_SYSTEM)

    try {
      const { page = 1, limit = 10, search } = request.qs()

      const query = ZnNailSystems.query().whereNull('deletedAt')

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder.whereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
        })
      }

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @select
   * @tag Admin Nail-Systems
   * @summary Read nail system selection
   * @paramQuery search - Search term - @type(string)
   * @responseBody 200 - <ZnNailSystems[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read nail system selection descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Show individual record
   */
  public async select({ request, response }: HttpContext) {
    const { page = 1, search, filter } = request.qs()

    try {
      const query = ZnNailSystems.query().whereNull('deletedAt')

      if (search) {
        query.whereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
      }

      if (filter) {
        query.whereNotIn('id', filter)
      }

      const result = await query.paginate(page, 5)

      return response.ok(result)
    } catch (error) {
      return response.internalServerError(error)
    }
  }

  /**
   * @show
   * @tag Admin Nail-Systems
   * @summary Read a nail system
   * @paramPath id - ID of Nail-Systems - @type(string) @required
   * @responseBody 200 - <ZnNailSystems>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read a nail system descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Nail system not found"} - Not Found
   */
  /**
   * Show individual record
   */
  public async show({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.NAIL_SYSTEM)

    try {
      const nailSystem = await this.nailSystemIfExists(params, response)
      if (!nailSystem) {
        return
      }

      return response.ok(nailSystem)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @create
   * @tag Admin Nail-Systems
   * @summary Return data to create a nail system
   */
  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {}

  /**
   * @store
   * @tag Admin Nail-Systems
   * @summary Create a nail system
   * @requestBody <ZnNailSystems>
   * @responseBody 201 - <ZnNailSystems>.append("id":"","createdAt":"","updatedAt":"")
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The name field must be defined","rule":"required","field":"name"}]}
   * @responseBody 401 - Unauthorized access
   */
  /**
   * Handle form submission for the create action
   */
  public async store({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.NAIL_SYSTEM)

    const data = request.all()

    const payload = await appCreateNailSystemsValidator.validate(data)

    try {
      const created = await ZnNailSystems.create({
        name: payload.name,
      })

      this.nailSystemsService.write(created)

      return response.created(created)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @edit
   * @tag Admin Nail-Systems
   * @summary Return data to update a nail system
   */
  /**
   * Edit individual record
   */
  async edit({}: HttpContext) {}

  /**
   * @update
   * @tag Admin Nail-Systems
   * @summary Update a nail system
   * @description Update a nail system descriptively
   * @paramPath id - ID of Nail-Systems - @type(string) @required
   * @requestBody <ZnNailSystems>
   * @responseBody 200 - <ZnNailSystems>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The name field must be defined","rule":"required","field":"name"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Nail system not found"} - Not Found
   */
  /**
   * Handle form submission for the edit action
   */
  public async update({ bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.NAIL_SYSTEM)

    const nailSystem = await this.nailSystemIfExists(params, response)
    if (!nailSystem) {
      return
    }

    const data = request.all()

    const payload = await appCreateNailSystemsValidator.validate(data)

    try {
      nailSystem.name = payload.name

      const updated = await nailSystem.save()

      this.nailSystemsService.write(updated)

      return response.ok(updated)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }
  /**
   * @destroy
   * @tag Admin Nail-Systems
   * @summary Soft-delete a nail system
   * @description Soft-delete a nail system descriptively
   * @paramPath id - ID of Nail-Systems - @type(string) @required
   * @responseBody 200 - {"message":"Nail system soft-deleted successfully"}
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Nail system not found"} - Not Found
   */
  /**
   * Delete record
   */
  public async destroy({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.NAIL_SYSTEM)

    try {
      const nailSystem = await this.nailSystemIfExists(params, response)
      if (!nailSystem) {
        return
      }

      await nailSystem?.softDelete()

      this.nailSystemsService.delete(nailSystem)

      return response.ok({ message: 'Nail system soft-deleted successfully' })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  private async nailSystemIfExists(
    params: HttpContext['params'],
    response: HttpContext['response']
  ) {
    const nailSystemId = params.id

    const nailSystem = await ZnNailSystems.query().where('id', nailSystemId).first()

    if (!nailSystem) {
      response.notFound({ message: 'Nail system not found' })
    }

    return nailSystem
  }
}
