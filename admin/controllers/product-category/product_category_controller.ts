import ZnProductCategory from '#models/zn_product_category'
import type { HttpContext } from '@adonisjs/core/http'
import { ACTION, RESOURCE } from '../../../app/constants/authorization.js'
import {
  createProductCategoryValidator,
  updateProductCategoryValidator,
} from '../../validators/product-category/product_category_validator.js'

export default class AdminProductCategoryController {
  /**
   * @index
   * @tag Admin Product Categories
   * @summary Read all product categories
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @paramQuery search - Search term - @type(string)
   * @responseBody 200 - <ZnProductCategory[]>.with(image,collections,subCategories,subCategories.image).paginated() - Read all product categories descriptively
   */
  async index({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.PRODUCT)

    const { page = 1, limit = 10, search } = request.qs()

    const query = ZnProductCategory.query()
      .preload('image')
      .preload('subCategories', (query) => {
        query.preload('image')
      })
      .preload('collections')

    if (search) {
      query.where((queryBuilder) => {
        queryBuilder.whereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
      })
    }

    const categories = await query.orderBy('name', 'asc').paginate(page, limit)
    return response.ok(categories)
  }

  /**
   * @store
   * @tag Admin Product Categories
   * @summary Create a product category
   * @description Create a product category descriptively
   * @requestBody <ZnProductCategory>.append("collections":[{"id":"","orderBy":0}])
   * @responseBody 200 - <ZnProductCategory> - Ok
   */
  async store({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.PRODUCT)

    const payload = await request.validateUsing(createProductCategoryValidator)

    const category = await ZnProductCategory.create({
      name: payload.name,
      shopifyId: payload.shopifyId,
      level: payload.level as any,
      isRoot: payload.isRoot ?? false,
      isLeaf: payload.isLeaf ?? false,
      parentId: payload.parentId,
      ancestorIds: payload.ancestorIds || '[]',
      childrenIds: payload.childrenIds || '[]',
      fullName: payload.fullName,
      isArchived: payload.isArchived ?? false,
      imageId: payload.imageId,
    })

    // attach collections with order by
    if (payload.collections && payload.collections.length > 0) {
      const collectionRecords: any = {}
      for (const collection of payload.collections) {
        collectionRecords[collection.id] = { orderBy: collection.pivotOrderBy }
      }
      await category.related('collections').attach(collectionRecords)
      // or attach collectionIds only
    } else if (payload.collectionIds && payload.collectionIds.length > 0) {
      await category.related('collections').attach(payload.collectionIds)
    }

    return response.created(category)
  }

  /**
   * @show
   * @tag Admin Product Categories
   * @summary Read a product category
   * @paramPath id - ID of Product Category - @type(string) @required
   * @responseBody 200 - <ZnProductCategory>.with(image,collections,subCategories,subCategories.image) - Read a product category descriptively
   */
  async show({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.PRODUCT)

    const category = await ZnProductCategory.query()
      .where('id', params.id)
      .preload('image')
      .preload('collections', (query) => {
        query.orderBy('orderBy', 'asc')
      })
      .preload('subCategories', (query) => {
        query.preload('image')
      })
      .first()

    if (!category) {
      return response.notFound('Category not found')
    }

    return response.ok(category)
  }

  /**
   * @update
   * @tag Admin Product Categories
   * @summary Update a product category
   * @description Update a product category descriptively
   * @paramPath id - ID of Product Category - @type(string) @required
   * @requestBody <ZnProductCategory>.append("collections":[{"id":"","orderBy":0}])
   * @responseBody 200 - <ZnProductCategory>.with(image,collections,subCategories,subCategories.image) - Ok
   */
  async update({ bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.PRODUCT)

    try {
      const category = await ZnProductCategory.find(params.id)
      if (!category) {
        return response.notFound('Category not found')
      }

      const payload = await request.validateUsing(updateProductCategoryValidator)

      category.merge({
        name: payload.name,
        shopifyId: payload.shopifyId,
        level: payload.level as any,
        isRoot: payload.isRoot,
        isLeaf: payload.isLeaf,
        parentId: payload.parentId,
        ancestorIds: payload.ancestorIds ? JSON.stringify(payload.ancestorIds) : category.ancestorIds,
        childrenIds: payload.childrenIds ? JSON.stringify(payload.childrenIds) : category.childrenIds,
        imageId: payload.imageId || null,
      })

      await category.save()

      // attach collections with order by
      if (payload.collections && payload.collections.length > 0) {
        const collectionRecords: any = {}
        for (const collection of payload.collections) {
          collectionRecords[collection.id] = { orderBy: collection.pivotOrderBy }
        }
        await category.related('collections').sync(collectionRecords)
        // or attach collectionIds only
      } else if (payload.collectionIds && payload.collectionIds.length > 0) {
        await category.related('collections').attach(payload.collectionIds)
      }

      await category.load('image')
      await category.load('collections')
      await category.load('subCategories', (query) => query.preload('image'))

      return response.ok(category)

    } catch (error) {
      console.log(error);
      return response.internalServerError(error)
    }
  }

  /**
   * @destroy
   * @tag Admin Product Categories
   * @summary Delete a product category
   * @description Delete a product category descriptively
   * @paramPath id - ID of Product Category - @type(string) @required
   * @responseBody 200 - {"message":"Category deleted successfully"} - Ok
   */
  async destroy({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.PRODUCT)

    const category = await ZnProductCategory.find(params.id)
    if (!category) {
      return response.notFound('Category not found')
    }

    await category.related('collections').detach()
    await category.delete()

    return response.ok({ message: 'Category deleted successfully' })
  }

  /**
   * @attachCollections
   * @tag Admin Product Categories
   * @summary Attack collections to a product category
   * @description Attack collections to a product category descriptively
   * @paramPath id - ID of Product Category - @type(string) @required
   * @requestBody {"collectionIds":[""],"collections":[{"id":"","orderBy":0}]}
   * @responseBody 200 - <ZnProductCategory>.with(collections) - Ok
   */
  async attachCollections({ bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.PRODUCT)

    try {
      const category = await ZnProductCategory.find(params.id)
      if (!category) {
        return response.notFound('Category not found')
      }

      const {
        collectionIds,
        collections
      } = request.body()

      // attach collections with order by
      if (collections && collections.length > 0) {
        const collectionRecords: any = {}
        for (const collection of collections) {
          collectionRecords[collection.id] = { orderBy: collection.pivotOrderBy }
          await category.related('collections').sync(collectionRecords)
        }
        // or attach collectionIds only
      } else if (collectionIds && collectionIds.length > 0) {
        await category.related('collections').sync(collectionIds)
      }

      await category.load('collections')

      return response.ok(category)
    } catch (error) {
      console.log(error);
      return response.internalServerError(error)
    }
  }

  /**
   * @select
   * @tag Admin Product Categories
   * @summary Read product categories selection
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery search - Search Term - @type(string)
   * @paramQuery filter - Filter Ids - @type(array)
   * @paramQuery excludeIds - Exclude Ids - @type(array)
   * @responseBody 200 - <ZnProductCategory[]>.paginated() - Read product categories selection descriptively
   */
  public async select({ request, response }: HttpContext) {
    const { page = 1, search, filter, excludeIds = [] } = request.qs()

    try {
      const query = ZnProductCategory.query().preload('image')

      if (search) {
        query.whereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
      }

      if (filter) {
        query.whereNotIn('id', filter)
      }

      if (excludeIds.length > 0) {
        query.whereNotIn('id', excludeIds)
      }

      const result = await query.where('isArchived', false).paginate(page, 5)

      return response.ok(result)
    } catch (error) {
      return response.internalServerError(error)
    }
  }
}
