import { SHIPPING_CHECKOUT_TEXT } from '#adminControllers/order/shipping_checkout_constants'
import ZnAdmin from '#models/zn_admin'
import { HttpContext } from '@adonisjs/core/http'

export default class AdminAuthController {
  /**
   * @loginAdmin
   * @tag Admin Auth
   * @summary Admin login
   * @requestBody {"username":"<EMAIL>","password":"swordfish"}
   * @responseBody 200 - {"type":"bearer","token":"","user":"<ZnAdmin>"} - Admin login descriptively
   * @responseBody 400 - Invalid credentials - Bad Request
   */
  async loginAdmin({ request, response, auth }: HttpContext) {
    const { username, password } = request.all()
    const user = await ZnAdmin.verifyCredentials(username, password)

    if (!user) {
      return response.abort('Invalid credentials')
    }

    const data = await (auth.use('jwt_admin') as any).generate(user)

    return {
      ...data,
      user,
    }
  }

  /**
   * @passbyCheckin
   * @tag Admin Auth
   * @summary Passby Checkin
   * @requestBody {"code":""}
   * @responseBody 200 - {"type":"bearer","token":"","user":"<ZnAdmin>"} - Passby Checkin descriptively
   * @responseBody 400 - Invalid credentials - Bad Request
   */
  async passbyCheckin({ auth, request, response, }: HttpContext) {
    const { code } = request.all()
    const admin = await ZnAdmin.find(code)
    // const admin = await ZnAdmin.query().first()

    if (!admin) {      
      return response.abort(SHIPPING_CHECKOUT_TEXT.CHECKIN_INVALID)
    }

    const data = await (auth.use('jwt_admin') as any).generate(admin)

    return {
      ...data,
      user: admin,
    }
  }
}
