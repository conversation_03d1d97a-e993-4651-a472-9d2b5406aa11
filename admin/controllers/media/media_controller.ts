import ZnMedia from '#models/zn_media'
import { uploadMediasValidator } from '../../validators/media/media_validator.js'
import { HttpContext } from '@adonisjs/core/http'
import { AmazonS3StorageService } from '../../../services/aws/s3/aws-s3.service.js'

export default class AdminMediaController {
  private amazonS3StorageService: AmazonS3StorageService

  constructor() {
    this.amazonS3StorageService = new AmazonS3StorageService()
  }

  /**
   * @upload
   * @tag Admin Media
   */
  async upload({ request, response }: HttpContext) {
    try {
      const files = request.files('medias')

      const data = { medias: files }
      await uploadMediasValidator.validate(data)

      const uploadPromises: any[] = []

      request.multipart.onFile('medias', {}, async (part) => {
        const chunks: any[] = []

        part.on('data', (chunk) => {
          chunks.push(chunk)
        })

        part.on('end', async () => {
          const buffer = Buffer.concat(chunks)

          const ext = part.filename.match(/(\.[a-zA-Z0-9]+)$/)
          const transformedFile = {
            fileName: part.filename as string,
            buffer,
            mimeType: part.headers['content-type'],
            extname: ext ? ext[0] : '',
          }

          uploadPromises.push(
            this.amazonS3StorageService.uploadImages([transformedFile]).then((uploadedToS3) => {
              return uploadedToS3.uploadedFiles.map((file) => ({
                fileKey: file.fileKey,
                url: file.fileKey,
                fileType: file.fileType,
              }))
            })
          )
        })

        part.on('error', (error) => {
          console.error(`Failed to process file: ${error.message}`)
        })
      })

      await request.multipart.process()

      const results = await Promise.all(uploadPromises)

      const uploadedFiles = results.flat()

      const mediaDtos = uploadedFiles.map((file) => {
        return {
          fileKey: file.fileKey,
          url: file.fileKey,
          type: file.fileType,
        }
      })

      const createdMedias = await ZnMedia.createMany(mediaDtos)

      const mappedResult = createdMedias.map((created) => {
        return {
          ...created.serialize(),
          url: `${this.amazonS3StorageService.fullUrl}${created.fileKey}`,
        }
      })

      return response.created(mappedResult)
    } catch (error) {
      if (error.messages) {
        return response.badRequest({
          message: 'Validation failed',
          errors: error.messages,
        })
      }

      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }
}
