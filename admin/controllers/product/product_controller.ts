import ZnProduct, { EProductApproval } from '#models/zn_product'
import ZnProductTag from '#models/zn_product_tag'
import ZnProductType from '#models/zn_product_type'
import ZnVendor from '#models/zn_vendor'
import { ShopifyService } from '#services/shopify/shopify_service'
import StoreShopifyProduct from '#services/sync/store_shopify_product'
import { HttpContext } from '@adonisjs/core/http'
import { ACTION, RESOURCE } from '../../../app/constants/authorization.js'
import { createProductValidator } from '../../validators/product/product_validator.js'
import { FastBundleService } from '#services/shopify/fast_bundle_service'
import { SyncBundleProductService } from '#services/sync/sync_bundle_product_service'
import { ProductService } from '#services/shop/product_service'

export default class AdminProductController {
  /**
   * @index
   * @tag Admin Product
   * @summary Read all products
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @responseBody 200 - <ZnProduct[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null).paginated() - Read all produtcs descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display a list of resource
   */
  async index({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.PRODUCT)

    try {
      const {
        page = 1,
        limit = 10,
        search,
        status,
        vendorId,
        categoryId,
        typeId,
        tagIds,
        sortBy,
        pendingApproval,
      } = request.qs()

      const query = ZnProduct.query()
        .where('isGift', false)
        .has('variant')
        .preload('image')
        .preload('vendor')
        .preload('category')
        .preload('productType')
        .preload('variants', (variantQuery) => {
          variantQuery.preload('image')
        })

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder.whereRaw('LOWER(title) LIKE LOWER(?)', [`%${search}%`])
            .orWhereHas('variants', (variantQuery) => {
              variantQuery.where('sku', search)
            })
        })
      }

      if (status) {
        query.where({ status })
      }

      if (vendorId) {
        query.where({ vendorId })
      }

      if (categoryId) {
        query.where({ categoryId: categoryId })
      }

      if (typeId) {
        query.where({ productTypeId: typeId })
      }

      if (tagIds && tagIds.length > 0) {
        query.whereHas('tags', (tagQuery) => {
          tagQuery.whereIn('id', tagIds)
        })
      }

      if (pendingApproval !== undefined) {
        query
          .whereNotNull('pendingApproval')
          .whereNot({ pendingApproval: EProductApproval.APPROVE })

        if (pendingApproval) {
          query.where({ pendingApproval })
        }
      }

      if (sortBy) {
        switch (sortBy) {
          case 'a2z':
            query.orderBy('title', 'asc')
            break
          case 'z2a':
            query.orderBy('title', 'desc')
            break
          case 'oldest':
            query.orderBy('createdAt', 'asc')
            break
          case 'newest':
            query.orderBy('createdAt', 'desc')
            break
          default:
        }
      }


      query.orderBy('updatedAt', 'desc')

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @select
   * @tag Admin Product
   * @summary Read product selection
   * @paramQuery search - Search term - @type(string)
   * @responseBody 200 - <ZnProduct[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read product selection descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Show individual record
   */
  public async select({ request, response }: HttpContext) {
    const {
      page = 1,
      search,
      filter
    } = request.qs()

    try {
      const query = ZnProduct.query()
        .whereNull('deletedAt')
        .where('status', 'active')
        .preload('image')

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder
            .whereRaw('LOWER(title) LIKE LOWER(?)', [`%${search}%`])
            .orWhereHas('variants', (variantQuery) => {
              variantQuery.where('sku', search)
            })
        })
      }

      if (filter) {
        query.whereNotIn('id', filter)
      }

      const result = await query.paginate(page, 5)

      return response.ok(result)
    } catch (error) {
      return response.internalServerError(error)
    }
  }

  /**
   * @show
   * @tag Admin Product
   * @summary Read a product
   * @paramPath id - ID of Product - @type(string) @required
   * @responseBody 200 - <ZnProduct>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read a product descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Product not found"} - Not Found
   */
  /**
   * Show individual record
   */
  public async show({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.PRODUCT)

    try {
      const productId = params.id

      const product = await ZnProduct.query()
        .preload('images')
        .preload('vendor')
        .preload('category')
        .preload('productType')
        .preload('tags')
        .preload('options', (query) => {
          query
            .preload('variantOptionValues')
            .preload('productOptionValues')
            .orderBy('position', 'asc')
        })
        .preload('variants', (query) => {
          query
            .preload('image')
            .preload('optionValues', (optionValueQuery) => {
              optionValueQuery.preload('option')
            })
            .orderBy('position', 'asc')
        })
        .where('id', productId)
        .first()

      if (!product) {
        return response.notFound({ message: 'Product not found' })
      }

      return response.ok(product)

    } catch (error) {
      console.log(error);
      return response.badRequest(error)
    }
  }

  /**
   * @store
   * @tag Admin Product
   * @summary Create a product
   * @requestBody <ZnProduct>
   * @responseBody 201 - <ZnProduct>.append("id":"","createdAt":"","updatedAt":"")
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The title field must be defined","rule":"required","field":"title"}]}
   * @responseBody 401 - Unauthorized access
   */
  /**
   * Handle form submission for the create action
   */
  public async store({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.PRODUCT)

    const data = request.all()

    const payload = await createProductValidator.validate(data)

    try {
      let productTypeId
      if (payload.productTypeName) {
        const productType = await ZnProductType.fetchOrCreateMany('name', [
          { name: payload.productTypeName },
        ])
        productTypeId = productType[0]?.id
      }

      let vendorId
      if (payload.vendorName) {
        const vendor = await ZnVendor.fetchOrCreateMany('companyName', [
          { companyName: payload.vendorName },
        ])
        vendorId = vendor[0]?.id
      }

      const created = await ZnProduct.create({
        title: payload.title,
        description: payload.description,
        status: payload.status,
        productTypeId: productTypeId,
        vendorId: vendorId,
      })

      if (payload.collectionIds) {
        created.related('collections').sync(payload.collectionIds)
      }

      if (payload.tagNames) {
        const tags = await ZnProductTag.fetchOrCreateMany(
          'name',
          payload.tagNames.map((tag) => ({ name: tag }))
        )
        created.related('tags').sync(tags.map((tag) => tag.id))
      }

      return response.created(created)

    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @update
   * @tag Admin Product
   * @summary Update a product
   * @description Update a product descriptively
   * @paramPath id - ID of Product - @type(string) @required
   * @requestBody <ZnProduct>
   * @responseBody 200 - <ZnProduct>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The title field must be defined","rule":"required","field":"title"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Product not found"} - Not Found
   */
  /**
   * Handle form submission for the edit action
   */
  public async update({ bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.PRODUCT)

    const productId = params.id

    const product = await ZnProduct.query().where('id', productId).first()

    if (!product) {
      return response.notFound({ message: 'Product not found' })
    }

    const data = request.all()

    const payload = await createProductValidator.validate(data)

    try {
      product.title = payload.title || product.title
      product.description = payload.description || product.description
      product.status = payload.status || product.status

      if (payload.productTypeName) {
        const productType = await ZnProductType.fetchOrCreateMany('name', [
          { name: payload.productTypeName },
        ])
        product.productTypeId = productType[0]?.id
      }

      if (payload.vendorName) {
        const vendor = await ZnVendor.fetchOrCreateMany('companyName', [
          { companyName: payload.vendorName },
        ])
        product.vendorId = vendor[0]?.id
      }

      if (payload.collectionIds) {
        product.related('collections').sync(payload.collectionIds)
      }

      if (payload.tagNames) {
        const tags = await ZnProductTag.fetchOrCreateMany(
          'name',
          payload.tagNames.map((tag) => ({ name: tag }))
        )
        product.related('tags').sync(tags.map((tag) => tag.id))
      }

      const updated = await product.save()

      return response.ok(updated)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @syncShopify
   * @tag Admin Product
   * @summary Sync a product with Shopify
   * @description Sync a product with Shopify descriptively
   * @paramPath id - ID of Product - @type(string) @required
   * @requestBody {}
   * @responseBody 200 - Synced - OK
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Product not found"} - Not Found
   */
  /**
   * Handle form submission for the edit action
   */
  public async syncShopify({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.PRODUCT)

    try {
      const productId = params.id

      const dbProduct = await ZnProduct.query()
        .where('id', productId)
        .first()

      if (!dbProduct) {
        return response.notFound({ message: 'Product not found' })
      }

      const shopifyProductId = dbProduct.shopifyProductId
      if (shopifyProductId) {
        const shopifyIdParts = shopifyProductId.split('/')
        const shopifyId = shopifyIdParts[shopifyIdParts.length - 1]

        const shopifyService = new ShopifyService()
        const { product } = await shopifyService.fetchShopifyProduct(shopifyId)
        if (product) {
          const storeShopifyProduct = new StoreShopifyProduct()
          await storeShopifyProduct.handle([product])
        }

        const bundleService = new FastBundleService()
        const bundle = await bundleService.getBundleByBundleProductId(shopifyId)
        if (bundle) {
          const syncBundleProductService = new SyncBundleProductService()
          await syncBundleProductService.syncBundleProductByBundle([bundle])
        }
      }

      return response.ok("Synced")

    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @destroy
   * @tag Admin Product
   * @summary Soft-delete a product
   * @description Soft-delete a product descriptively
   * @paramPath id - ID of Product - @type(string) @required
   * @responseBody 200 - {"message":"Product soft-deleted successfully"}
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Product not found"} - Not Found
   */
  /**
   * Delete record
   */
  public async destroy({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.PRODUCT)

    try {
      const productId = params.id

      const product = await ZnProduct.query().where('id', productId).first()

      if (!product) {
        return response.notFound({ message: 'Product not found' })
      }

      await product?.softDelete()

      return response.ok({ message: 'Product soft-deleted successfully' })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @productApproval
   * @tag Admin Product
   * @summary Approve/Reject a product
   * @description Approve or reject a product descriptively
   * @paramPath id - ID of Product - @type(string) @required
   * @responseBody 200 - {"message":"Product approved"}
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Product not found"} - Not Found
   */
  /**
   * Approve/Reject product
   */
  public async productApproval({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.PRODUCT)

    try {
      const { approval, productIds } = request.body()

      const products = await ZnProduct.query()
        .whereIn('id', productIds)

      if (products.length == 0) {
        return response.notFound({ message: 'Products not found' })
      }

      for (const product of products) {
        if (product.pendingApproval != EProductApproval.UPDATE) { continue }

        if (approval == 'approve') {
          const productService = new ProductService()

          const payload = await createProductValidator.validate(product.pendingChanges)

          await productService.updateShopifyProduct(product.shopifyProductId, payload)

          await product.merge({ pendingChanges: null, pendingApproval: EProductApproval.APPROVE }).save()
        } else if (approval == 'reject') {
          await product.merge({ pendingApproval: EProductApproval.REJECT }).save()
        }
      }

      return response.ok({ message: 'Products approved successfully' })
    } catch (error) {
      return response.badRequest(error)
    }
  }
}
