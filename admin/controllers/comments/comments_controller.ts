import { RESOURCE_TYPE } from '#constants/like_comment_resource'
import ZnAdmin from '#models/zn_admin'
import ZnPostComment from '#models/zn_post_comment'
import ZnPostCommentLike from '#models/zn_post_comment_like'
import { HttpContext } from '@adonisjs/core/http'
import { createAdminCommentValidator } from '../../validators/comments/comments_validator.js'
import SendNotificationPostCommentJob, {
  CommentJobType,
} from '#jobs/send_notification_post_comment_job'
import queue from '@rlanz/bull-queue/services/main'

export default class AdminCommentsController {
  /**
   * @index
   * @tag Admin Comment
   * @summary List comment of resource
   * @paramQuery resourceId - ID of Ressource - @type(stinrg)
   * @paramQuery postId - ID of Post - @type(stinrg)
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @requestBody <ZnPostComment[]>.paginated()
   */
  async index({ request, response }: HttpContext) {
    try {
      const { page = 1, limit = 10, resourceId, postId } = request.qs()
      // Support old app version, remove later
      let queryColumn: string
      let queryValue: string

      if (postId) {
        queryColumn = 'postId'
        queryValue = postId
      } else if (resourceId) {
        queryColumn = 'resourceId'
        queryValue = resourceId
      } else {
        return response.badRequest({ message: 'Resource ID is required' })
      }

      const queryComments = ZnPostComment.query()
        .where(queryColumn, queryValue)
        .whereNull('parentCommentId')
        .withCount('children')
        .withCount('reactUsers')
        .preload('user', (query) => {
          query.preload('avatarMedia')
        })
        .preload('admin', (query) => {
          query.preload('avatar')
        })
        .orderBy('createdAt', 'desc')

      const result = await queryComments.paginate(page, limit)

      const totalComments = await ZnPostComment.query()
        .where(queryColumn, queryValue)
        .count('* AS count')
        .first()

      return response.ok({
        ...result.serialize(),
        totalComments: totalComments?.$extras.count,
      })
    } catch (error) {
      console.log(error)

      return response.badRequest(error)
    }
  }

  /**
   * @show
   * @tag Admin Comment
   * @summary Show a comment
   * @paramPath id - ID of Comment - @type(string) @required
   * @responseBody 200 - <ZnPostComment>.with(post)  - Show a comment descriptively
   * @responseBody 404 - {"message":"Comment not found"} - Not Found
   */
  async show({ response, params }: HttpContext) {
    const comment = await ZnPostComment.query().where({ id: params.id }).preload('post').first()

    if (!comment) {
      return response.notFound({ message: 'Comment not found' })
    }

    return response.ok(comment)
  }

  /**
   * @children
   * @tag Admin Comment
   * @summary Show a comment's children
   * @paramPath id - ID of Comment - @type(string) @required
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @responseBody 200 - <ZnPostComment>.with(post)  - Show a comment descriptively
   * @responseBody 404 - {"message":"Comment not found"} - Not Found
   */
  async children({ request, response, params }: HttpContext) {
    try {
      const { page = 1, limit = 10, filterIds = [] } = request.qs()
      const parentCommentId = params.id

      const queryComments = ZnPostComment.query()
        .where({ parentCommentId })
        .whereNotIn('id', filterIds)
        .withCount('reactUsers')
        .preload('user', (query) => {
          query.preload('avatarMedia')
        })
        .preload('admin', (query) => {
          query.preload('avatar')
        })
        .orderBy('createdAt', 'asc')

      const result = await queryComments.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @store
   * @tag Admin Comment
   * @summary Create a comment
   * @requestBody <ZnPostComment>.only(parentCommentId, content, postId, resourceId, resourceType)
   * @responseBody 200 - <ZnPostComment> - Create a comment descriptively
   * @responseBody 400 - {"message":"Resource ID and type are required"} - Bad Request
   * @responseBody 500 - {"message":"Something went wrong"} - Bad Request
   */
  async store({ auth, request, response }: HttpContext) {
    const data = request.all()
    const payload = await createAdminCommentValidator.validate(data)

    const admin = auth.getUserOrFail() as ZnAdmin
    try {
      const baseDto = {
        parentCommentId: payload.parentCommentId,
        content: payload.content,
        userId: admin?.id,
        userType: ZnAdmin.name,
        totalLike: 0,
        resourceId: '',
        resourceType: '',
      }

      let commentDto
      if (payload.postId && payload.resourceType === RESOURCE_TYPE.POST) {
        // For post type, include postId
        commentDto = Object.assign({}, baseDto, {
          postId: payload.postId,
          resourceId: payload.postId,
          resourceType: RESOURCE_TYPE.POST,
        })
      } else if (payload.resourceId && payload.resourceType) {
        // For other types, exclude postId
        commentDto = Object.assign({}, baseDto, {
          resourceId: payload.resourceId,
          resourceType: payload.resourceType,
        })
      } else {
        return response.badRequest({ message: 'Resource ID and type are required' })
      }

      const created = await ZnPostComment.create(commentDto)

      // @ts-ignore
      const comment = await ZnPostComment.query({ mode: 'write' })
        .where({ id: created.id })
        .withCount('children')
        .withCount('reactUsers')
        .preload('user', (query) => {
          query.preload('avatarMedia')
        })
        .preload('admin', (query) => {
          query.preload('avatar')
        })
        .first()

      return response.created(comment)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @destroy
   * @tag Admin Comment
   * @summary Soft-delete a comment
   * @paramPath id - ID of Comment - Soft-delete a comment descriptively
   * @responseBody 200 - {"message":"Comment soft-deleted successfully"} - OK
   * @responseBody 404 - {"message":"Comment not found"} - Not Found
   */
  async destroy({ params, response }: HttpContext) {
    const comment = await ZnPostComment.find(params.id)

    if (!comment) {
      return response.notFound({ message: 'Comment not found' })
    }

    try {
      await comment.softDelete()

      const children = await ZnPostComment.findManyBy({ parentCommentId: comment.id })
      for (const child of children) {
        await child.softDelete()
      }

      return response.ok({ message: 'Deleted successfully' })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @react
   * @tag Admin Comment
   * @responseBody 200 - Like successfully
   * @responseBody 404 - Comment not found
   */
  async react({ auth, response, params }: HttpContext) {
    const comment = await ZnPostComment.find(params.id)
    const admin = auth.getUserOrFail() as ZnAdmin

    if (!comment) {
      return response.notFound({ message: 'Comment not found' })
    }

    const like = await ZnPostCommentLike.query()
      .where({
        userId: admin.id,
        postCommentId: params.id,
      })
      .first()

    if (like) {
      await like.delete()

      return response.ok('Unlike successfully')
    } else {
      await ZnPostCommentLike.create({
        userId: admin.id,
        postCommentId: params.id,
      })
      await queue.dispatch(SendNotificationPostCommentJob, {
        comment,
        type: CommentJobType.Like,
        user: admin as any,
      })

      return response.ok('Like successfully')
    }
  }
}
