import { HttpContext } from '@adonisjs/core/http'

import { appCreateContactInformationValidator } from '#validators/app/contact-information/contact-information'
import { ContactInformationService } from '#services/firebase/contact_information_service'
import { CONTACT_INFORMATION_NAME_SINGLE } from '../../../app/constants/contact_information.js'

import ZnContactInformation from '#models/zn_contact_information'
import { ACTION, RESOURCE } from '../../../app/constants/authorization.js'

export default class AppContactInformationController {
  private contactInformationService = new ContactInformationService()

  /**
   * @index
   * @tag Admin Contact-Information
   * @summary Read all contact information
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @responseBody 200 - <ZnContactInformation[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null).paginated() - Read all contact information descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display a list of resource
   */
  async index({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.CONTACT_INFORMATION)

    try {
      const { page = 1, limit = 10, search } = request.qs()

      const query = ZnContactInformation.query().whereNull('deletedAt')

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder
            .where('name', 'like', `%${search}%`)
            .orWhere('title', 'like', `%${search}%`)
            .orWhere('value', 'like', `%${search}%`)
        })
      }

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @create
   * @tag Admin Contact-Information
   * @summary Return data to create
   */
  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {}

  /**
   * @store
   * @tag Admin Contact-Information
   * @summary Create a contact information
   * @requestBody <ZnContactInformation>
   * @responseBody 201 - <ZnContactInformation>.append("id":"","createdAt":"","updatedAt":"") - Create a contact information descriptively
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The name field must be defined","rule":"required","field":"name"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 409 - Duplicated - Conflict
   */
  /**
   * Handle form submission for the create action
   */
  public async store({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.CONTACT_INFORMATION)

    const data = request.all()

    const payload = await appCreateContactInformationValidator.validate(data)

    try {
      if (Object.values(CONTACT_INFORMATION_NAME_SINGLE).includes(payload.name as any)) {
        const contactInformationSingle = await ZnContactInformation.query()
          .whereNull('deletedAt')
          .where('name', payload.name)
          .first()

        if (contactInformationSingle) {
          return response.conflict('Duplicated')
        }
      }

      const created = await ZnContactInformation.create({
        name: payload.name,
        title: payload.title,
        value: payload.value,
      })

      this.contactInformationService.write(created)

      return response.created(created)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @show
   * @tag Admin Contact-Information
   * @summary Read a contact information
   * @paramPath id - ID of Contact-Information - @type(string) @required
   * @responseBody 200 - <ZnContactInformation>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read a contact information descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Contact information not found"} - Not Found
   */
  /**
   * Show individual record
   */
  public async show({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.CONTACT_INFORMATION)

    try {
      const contactInformation = await this.contactInformationIfExists(params, response)
      if (!contactInformation) {
        return
      }

      return response.ok(contactInformation)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @edit
   * @tag Admin Contact-Information
   * @summary Return data to update
   */
  /**
   * Edit individual record
   */
  async edit({}: HttpContext) {}

  /**
   * @update
   * @tag Admin Contact-Information
   * @summary Update a contact information
   * @description Update a contact information descriptively
   * @paramPath id - ID of Contact-Information - @type(string) @required
   * @requestBody <ZnContactInformation>
   * @responseBody 200 - <ZnContactInformation>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The name field must be defined","rule":"required","field":"name"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Contact information not found"} - Not Found
   */
  /**
   * Handle form submission for the edit action
   */
  public async update({ bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.CONTACT_INFORMATION)

    const contactInformation = await this.contactInformationIfExists(params, response)
    if (!contactInformation) {
      return
    }

    const data = request.all()

    const payload = await appCreateContactInformationValidator.validate(data)

    try {
      contactInformation.name = payload.name
      contactInformation.title = payload.title
      contactInformation.value = payload.value

      const updated = await contactInformation.save()

      this.contactInformationService.write(updated)

      return response.ok(updated)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @destroy
   * @tag Admin Contact-Information
   * @summary Soft-delete a contact information
   * @description Soft-delete a contact information descriptively
   * @paramPath id - ID of Contact-Information - @type(string) @required
   * @responseBody 200 - {"message":"Contact information soft-deleted successfully"}
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Contact information not found"} - Not Found
   */
  /**
   * Delete record
   */
  public async destroy({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.CONTACT_INFORMATION)

    try {
      const contactInformation = await this.contactInformationIfExists(params, response)
      if (!contactInformation) {
        return
      }

      await contactInformation?.softDelete()

      this.contactInformationService.delete(contactInformation)

      return response.ok({ message: 'Contact information soft-deleted successfully' })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  private async contactInformationIfExists(
    params: HttpContext['params'],
    response: HttpContext['response']
  ) {
    const contactInformationId = params.id

    const contactInformation = await ZnContactInformation.query()
      .where('id', contactInformationId)
      .first()

    if (!contactInformation) {
      response.notFound({ message: 'Contact information not found' })
    }

    return contactInformation
  }
}
