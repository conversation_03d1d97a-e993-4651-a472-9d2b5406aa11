import { inject } from '@adonisjs/core'
import type { HttpContext } from '@adonisjs/core/http'
import EventService from '#services/events/event_service'
import {
  createEventValidator,
  updateEventValidator,
  findRegistrationByContactValidator,
  checkInParticipantValidator,
  createEventRegistrationValidator,
} from '../../validators/event/event.js'
import ZnEvent, { EEventStatus } from '#models/zn_event'
import ZnEventRegistration, { ERegistrationSource } from '#models/zn_event_registration'
import { DateTime } from 'luxon'
import queue from '@rlanz/bull-queue/services/main'
import EventRegistrationJob from '#jobs/events/event_registration_job'
import { ACTION, RESOURCE } from '#constants/authorization'
import ZnEventTime from '#models/zn_event_times'

@inject()
export default class AdminEventController {
  constructor(protected eventService: EventService) { }

  /**
   * @index
   * @tag Admin Event
   * @summary List all events with pagination and filtering
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @paramQuery search - Search term for title or location - @type(string)
   * @paramQuery status - Filter by event status - @type(string)
   * @responseBody 200 - {"success":true,"data":<ZnEvent[]>.paginated()} - List of events with pagination
   * @responseBody 500 - {"success":false,"message":"Failed to fetch events"} - Internal Server Error
   */
  public async index({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.EVENT)

    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 10)
      const search = request.input('search', '')
      const status = request.input('status', '')

      let query = ZnEvent.query().whereNull('deletedAt').orderBy('createdAt', 'desc')

      if (search) {
        query = query.where((builder) => {
          builder.whereILike('title', `%${search}%`).orWhereILike('location', `%${search}%`)
        })
      }

      if (status && Object.values(EEventStatus).includes(status as EEventStatus)) {
        query = query.where('status', status)
      }

      const events = await query
        .preload('timeRanges', (query) => {
          query.orderBy('startTime');
        })
        .preload('thumbnails', (query) => {
          query.pivotColumns(['renderPosition'])
        })
        .preload('createdByAdmin')
        .paginate(page, limit)

      const eventsWithCounts = await Promise.all(
        events.map(async (event) => {
          const registrationsCount = await ZnEventRegistration.query()
            .where('eventId', event.id)
            .whereNull('deletedAt')
            .count('* as total')

          const serializedEvent = event.serialize()
          return {
            ...serializedEvent,
            registrationsCount: Number(registrationsCount[0].$extras.total || 0),
          }
        })
      )

      const result = events.serialize()
      result.data = eventsWithCounts

      return response.ok(result)
    } catch (error) {
      console.error('Error fetching events:', error)
      return response.internalServerError({
        success: false,
        message: 'Failed to fetch events',
      })
    }
  }

  /**
   * @select
   * @tag Admin Event
   * @summary List event selections
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @paramQuery search - Search term for title or location - @type(string)
   * @responseBody 200 - {"success":true,"data":<ZnEvent[]>.paginated()} - List event selections descriptively
   * @responseBody 500 - {"success":false,"message":"Failed to fetch events"} - Internal Server Error
   */
  public async select({ request, response }: HttpContext) {
    try {
      const page = request.input('page', 1)
      const limit = request.input('limit', 10)
      const search = request.input('search', '')

      let query = ZnEvent.query()
        .preload('thumbnails', (query) => {
          query.pivotColumns(['renderPosition'])
        })
        .whereNull('deletedAt')
        .whereIn('status', [EEventStatus.ACTIVE, EEventStatus.SCHEDULED])
        .orderBy('createdAt', 'desc')

      if (search) {
        query = query.where((builder) => {
          builder.whereILike('title', `%${search}%`).orWhereILike('location', `%${search}%`)
        })
      }

      const events = await query.paginate(page, limit)

      return response.ok(events)
    } catch (error) {
      console.error('Error fetching events:', error)
      return response.internalServerError({
        success: false,
        message: 'Failed to fetch events',
      })
    }
  }

  /**
   * @store
   * @tag Admin Event
   * @summary Create a new event
   * @requestBody {"title":"Event Title","description":"Event Description","location":"Event Location","startTime":"2023-12-25T10:00:00Z","endTime":"2023-12-25T18:00:00Z","capacity":100,"status":"scheduled","slug":"event-title","shareUrl":"https://domain.com/event/event-title/register"}
   * @responseBody 201 - {"success":true,"data":<ZnEvent>,"message":"Event created successfully"} - Event created successfully
   * @responseBody 400 - {"success":false,"message":"Validation failed"} - Bad Request
   */
  public async store({ bouncer, request, response, auth }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.EVENT)

    try {
      const payload = await createEventValidator.validate(request.all())

      const adminId = (auth.user as any)?.id || null

      const event = await this.eventService.createEvent(
        {
          ...payload,
          createdByAdminId: adminId,
          slug: payload.slug,
          // shareUrl: payload.shareUrl,
        },
        payload.timeRanges,
        payload.thumbnails ?? []
      )

      return response.created(event)
    } catch (error) {
      console.error('Error creating event:', error)
      return response.badRequest({
        success: false,
        message: error.message || 'Failed to create event',
      })
    }
  }

  /**
   * @show
   * @tag Admin Event
   * @summary Get a specific event with its registrations
   * @paramPath id - ID of Event - @type(string) @required
   * @paramQuery tab - Tab to display (general or registrations) - @type(string)
   * @paramQuery page - Page Number for registrations (default 1) - @type(number)
   * @paramQuery limit - Page Limit for registrations (default 10) - @type(number)
   * @paramQuery search - Search term for registrations - @type(string)
   * @responseBody 200 - {"success":true,"data":{"event":<ZnEvent>,"registrations":<ZnEventRegistration[]>}} - Event details with registrations
   * @responseBody 404 - {"success":false,"message":"Event not found"} - Not Found
   */
  public async show({ bouncer, params, response, request }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.EVENT)

    try {
      const event = await ZnEvent.query()
        .where('id', params.id)
        .whereNull('deletedAt')
        .preload('timeRanges', (query) => {
          query.orderBy('startTime');
        })
        .preload('thumbnails', (query) => {
          query.pivotColumns(['renderPosition'])
        })
        .preload('createdByAdmin')
        .firstOrFail()

      const tab = request.input('tab', 'general')
      let registrations: any = { meta: {}, data: [], counts: { total: 0, checkedIn: 0 } }

      if (tab === 'registrations') {
        const page = request.input('page', 1)
        const limit = request.input('limit', 10)
        const search = request.input('search', '')

        const registrationsQuery = ZnEventRegistration.query()
          .where('eventId', params.id)
          .preload('languages')
          .orderBy('createdAt', 'desc');

        let baseQuery = ZnEventRegistration.query()
          .where('eventId', params.id)
          .whereNull('deletedAt')

        if (search) {
          const searchCondition = (builder: any) => {
            builder
              .whereILike('name', `%${search}%`)
              .orWhereILike('email', `%${search}%`)
              .orWhereILike('phone', `%${search}%`)
          }
          registrationsQuery.where(searchCondition)
          baseQuery = baseQuery.where(searchCondition)
        }

        const [countResults] = await Promise.all([
          Promise.all([
            baseQuery.clone().count('* as total').first(),
            baseQuery.clone().where('checkedIn', '=', 1).count('* as checkedIn').first(),
          ]),
        ])

        registrations = (await registrationsQuery.paginate(page, limit)).serialize();
        registrations.counts = {
          total: Number(countResults?.[0]?.$extras.total || 0),
          checkedIn: Number(countResults?.[1]?.$attributes.checkedIn || 0),
        }
      }

      return response.ok({
        success: true,
        data: {
          event,
          registrations:
            tab === 'registrations'
              ? registrations
              : { meta: {}, data: [], counts: { total: 0, checkedIn: 0 } },
        },
      })
    } catch (error) {
      console.error('Error fetching event details:', error)
      return response.notFound({
        success: false,
        message: 'Event not found',
      })
    }
  }

  /**
   * @update
   * @tag Admin Event
   * @summary Update an existing event
   * @paramPath id - ID of Event - @type(string) @required
   * @requestBody {"title":"Updated Title","description":"Updated Description","location":"Updated Location","startTime":"2023-12-25T10:00:00Z","endTime":"2023-12-25T18:00:00Z","capacity":150,"status":"active","slug":"updated-title","shareUrl":"https://domain.com/event/updated-title/register"}
   * @responseBody 200 - {"success":true,"data":<ZnEvent>,"message":"Event updated successfully"} - Event updated successfully
   * @responseBody 400 - {"success":false,"message":"Validation failed"} - Bad Request
   * @responseBody 404 - {"success":false,"message":"Event not found"} - Not Found
   */
  public async update({ bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.EVENT)

    try {
      const event = await ZnEvent.findOrFail(params.id)
      const payload = await updateEventValidator.validate({
        ...request.all(),
        id: event.id,
      })

      const updates: any = { ...payload }

      // Only update slug and shareUrl if they are provided
      if (payload.slug) {
        updates.slug = payload.slug
      }
      // if (payload.shareUrl) {
      //   updates.shareUrl = payload.shareUrl
      // }

      Object.assign(event, updates)

      await event.save()

      if (payload.timeRanges) {
        await event.load('timeRanges');
        const updatingTimeRangeIds = payload.timeRanges.filter(tr => tr.id && tr.id.length > 0).map(tr => tr.id);
        const deletedTimeRangeIds = event.timeRanges.filter(tr => !updatingTimeRangeIds.includes(tr.id)).map(tr => tr.id);
        const deletedTimeRanges = await ZnEventTime.findMany(deletedTimeRangeIds);
        for (const tr of deletedTimeRanges) {
          await tr.softDelete();
        }

        await ZnEventTime.updateOrCreateMany('id', payload.timeRanges.map((tr) => {
          return {
            id: (tr.id && tr.id.length > 0) ? tr.id : '',
            startTime: DateTime.fromJSDate(new Date(tr.startTime)),
            endTime: DateTime.fromJSDate(new Date(tr.endTime)),
            eventId: event.id
          }
        }));
      }

      if (payload.thumbnails) {
        const syncData = payload.thumbnails.reduce((accData, thumbnail) => ({
          ...accData,
          [thumbnail.id]: { renderPosition: (thumbnail.renderPosition && thumbnail.renderPosition.length > 0) ? thumbnail.renderPosition : 'register-page' }
        }), {});
        await event.related('thumbnails').sync(syncData);
      }

      await event.load('timeRanges', (query) => {
        query.orderBy('startTime', 'asc')
      });
      await event.load('thumbnails');

      return response.ok({
        success: true,
        data: event,
        message: 'Event updated successfully',
      })
    } catch (error) {
      console.error('Error updating event:', error)
      return response.badRequest({
        success: false,
        message: error.message || 'Failed to update event',
      })
    }
  }

  /**
   * @checkIn
   * @tag Admin Event
   * @summary Check in a participant
   * @requestBody {"registrationId":"uuid-of-registration","adminId":"uuid-of-admin"}
   * @responseBody 200 - {"success":true,"data":<ZnEventRegistration>,"message":"Participant checked in successfully"} - Check-in successful
   * @responseBody 400 - {"success":false,"message":"Failed to check in participant"} - Bad Request
   * @responseBody 404 - {"success":false,"message":"Registration not found"} - Not Found
   */
  public async checkIn({ bouncer, request, response, auth }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CHECK_IN, RESOURCE.EVENT)

    try {
      const payload = await request.validateUsing(checkInParticipantValidator)
      const adminId = (auth.user as any)?.id || null

      const registration = await this.eventService.checkInParticipant(
        payload.registrationId,
        adminId || payload.adminId
      )

      return response.ok({
        success: true,
        data: registration,
        message: 'Participant checked in successfully',
      })
    } catch (error) {
      console.error('Error checking in participant:', error)
      return response.badRequest({
        success: false,
        message: error.message || 'Failed to check in participant',
      })
    }
  }

  /**
   * @findRegistration
   * @tag Admin Event
   * @summary Find a registration by email or phone
   * @requestBody {"eventId":"uuid-of-event","contact":"email-or-phone"}
   * @responseBody 200 - {"success":true,"data":<ZnEventRegistration>} - Registration found
   * @responseBody 400 - {"success":false,"message":"Failed to find registration"} - Bad Request
   * @responseBody 404 - {"success":false,"message":"Registration not found"} - Not Found
   */
  public async findRegistration({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.VIEW_REGISTRATION, RESOURCE.EVENT)

    try {
      const payload = await request.validateUsing(findRegistrationByContactValidator)

      const registration = await this.eventService.findRegistrationByContact(
        payload.eventId,
        payload.contact
      )

      if (!registration) {
        return response.notFound({
          success: false,
          message: 'Registration not found',
        })
      }

      return response.ok({
        success: true,
        data: registration,
      })
    } catch (error) {
      console.error('Error finding registration:', error)
      return response.badRequest({
        success: false,
        message: error.message || 'Failed to find registration',
      })
    }
  }

  /**
   * @registerAndCheckIn
   * @tag Admin Event
   * @summary Register and check in a participant immediately
   * @requestBody {"eventId":"uuid-of-event","name":"Participant Name","email":"<EMAIL>","phone":"+84123456789","source":"admin"}
   * @responseBody 200 - {"success":true,"data":<ZnEventRegistration>,"message":"Participant registered and checked in successfully"} - Registration and check-in successful
   * @responseBody 400 - {"success":false,"message":"Failed to register and check in"} - Bad Request
   */
  public async registerAndCheckIn({ bouncer, request, response, auth }: HttpContext) {
    await bouncer.authorize('allow', ACTION.ADD_REGISTRATION, RESOURCE.EVENT)
    await bouncer.authorize('allow', ACTION.CHECK_IN, RESOURCE.EVENT)

    try {
      const payload = await request.validateUsing(createEventRegistrationValidator)
      if (!payload.email && !payload.phone) {
        return response.badRequest({
          success: false,
          message: 'Validation failed',
          errors: [
            {
              field: 'emailOrPhone',
              rule: 'emailOrPhone',
              message: 'Either email or phone number is required for registration',
            },
          ],
        })
      }

      let eventId = payload.eventId || request.input('eventId')

      if (!eventId) {
        const defaultEvent = await ZnEvent.query()
          .whereNull('deletedAt')
          .where('status', '!=', EEventStatus.CANCELLED)
          .orderBy('startTime', 'desc')
          .first()

        if (!defaultEvent) {
          return response.badRequest({
            success: false,
            message: 'No active event found',
          })
        }

        eventId = defaultEvent.id
      }

      const event = await ZnEvent.query().where('id', eventId).whereNull('deletedAt').first()

      if (!event) {
        return response.badRequest({
          success: false,
          message: 'Event not found',
        })
      }

      const existingRegistration = await ZnEventRegistration.query()
        .where('eventId', eventId)
        .whereNull('deletedAt')
        .where((query) => {
          if (payload.email) {
            query.where('email', payload.email)
          }
          if (payload.phone) {
            query.orWhere('phone', payload.phone)
          }
        })
        .first()

      if (existingRegistration) {
        if (!existingRegistration.checkedIn) {
          const adminId = (auth.user as any)?.id || null
          await this.eventService.checkInParticipant(existingRegistration.id, adminId)

          if (existingRegistration.email) {
            await this.eventService.sendRegistrationEmail(existingRegistration.id)
          }

          return response.ok({
            success: true,
            data: existingRegistration,
            message: 'Existing participant checked in successfully',
          })
        }

        return response.badRequest({
          success: false,
          message: `A registration already exists and is checked in with this ${existingRegistration.email === payload.email ? 'email' : 'phone number'}`,
          data: existingRegistration,
        })
      }

      const adminId = (auth.user as any)?.id || null

      const registration = await this.eventService.registerEvent(
        eventId,
        {
          name: payload.name,
          email: payload.email || '',
          phone: payload.phone || '',
          languages: payload.languages,
          occupation: payload.occupation,
          source: ERegistrationSource.ADMIN,
          userId: payload.userId,
        },
        true,
        adminId
      )

      if (registration.email) {
        await this.eventService.sendRegistrationEmail(registration.id)
      }

      return response.ok({
        success: true,
        data: registration,
        message: 'Participant registered and checked in successfully',
      })
    } catch (error) {
      console.error('Error in register and check in:', error)
      return response.badRequest({
        success: false,
        message: error.message || 'Failed to register and check in',
      })
    }
  }

  /**
   * @registerMobile
   * @tag Admin Event
   * @summary Register for an event from mobile app with optional check-in
   * @requestBody {"eventId":"uuid","name":"string","email":"string","phone":"string","shouldCheckIn":boolean}
   * @responseBody 200 - {"success":true,"data":{"jobId":"string"}} - Registration job queued successfully
   * @responseBody 400 - {"success":false,"message":"Validation failed"} - Bad Request
   */
  public async registerMobile({ request, response, auth }: HttpContext) {
    try {
      const payload = await createEventRegistrationValidator.validate(request.all())

      const userId = (auth.user as any)?.id || null

      const job = await queue.dispatch(EventRegistrationJob, {
        eventId: payload.eventId,
        userId,
        name: payload.name,
        email: payload.email,
        phone: payload.phone,
        shouldCheckIn: payload.shouldCheckIn || false,
        checkInTime: DateTime.now().toISO(),
      })

      return response.ok({
        success: true,
        data: {
          jobId: job.id,
        },
      })
    } catch (error) {
      console.error('Error registering for event:', error)
      return response.badRequest({
        success: false,
        message: error.message || 'Failed to register for event',
      })
    }
  }

  /**
   * @destroy
   * @tag Admin Event
   * @summary Soft-delete an event
   * @paramPath id - ID of Event - @type(string) @required
   * @responseBody 200 - {"success":true,"message":"Event deleted successfully"} - Event soft-deleted successfully
   * @responseBody 400 - {"success":false,"message":"Failed to delete event"} - Bad Request
   * @responseBody 404 - {"success":false,"message":"Event not found"} - Not Found
   */
  public async destroy({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.EVENT)

    try {
      const event = await ZnEvent.findOrFail(params.id)
      const now = DateTime.now()

      await ZnEventRegistration.query()
        .where('eventId', event.id)
        .whereNull('deletedAt')
        .update({ deletedAt: now.toJSDate() })

      event.deletedAt = now
      await event.save()

      return response.ok({
        success: true,
        message: 'Event and related registrations deleted successfully',
      })
    } catch (error) {
      console.error('Error deleting event:', error)
      return response.badRequest({
        success: false,
        message: error.message || 'Failed to delete event',
      })
    }
  }

  /**
   * @register
   * @tag Admin Event
   * @summary Register a participant from admin panel
   * @requestBody {"eventId":"uuid-of-event","name":"Participant Name","email":"<EMAIL>","phone":"+84123456789"}
   * @responseBody 200 - {"success":true,"data":<ZnEventRegistration>,"message":"Participant registered successfully"} - Registration successful
   * @responseBody 400 - {"success":false,"message":"Failed to register"} - Bad Request
   */
  public async register({ bouncer, request, response, auth }: HttpContext) {
    await bouncer.authorize('allow', ACTION.ADD_REGISTRATION, RESOURCE.EVENT)

    try {
      const payload = await request.validateUsing(createEventRegistrationValidator)
      if (!payload.email && !payload.phone) {
        return response.badRequest({
          success: false,
          message: 'Validation failed',
          errors: [
            {
              field: 'emailOrPhone',
              rule: 'emailOrPhone',
              message: 'Either email or phone number is required for registration',
            },
          ],
        })
      }
      const adminId = (auth.user as any)?.id || null

      let eventId = payload.eventId || request.input('eventId')

      if (!eventId) {
        const defaultEvent = await ZnEvent.query()
          .whereNull('deletedAt')
          .where('status', '!=', EEventStatus.CANCELLED)
          .orderBy('startTime', 'desc')
          .first()

        if (!defaultEvent) {
          return response.badRequest({
            success: false,
            message: 'No active event found',
          })
        }

        eventId = defaultEvent.id
      }

      const event = await ZnEvent.query().where('id', eventId).whereNull('deletedAt').first()

      if (!event) {
        return response.badRequest({
          success: false,
          message: 'Event not found',
        })
      }

      const existingRegistration = await ZnEventRegistration.query()
        .where('eventId', eventId)
        .whereNull('deletedAt')
        .where((query) => {
          if (payload.email) {
            query.where('email', payload.email)
          }
          if (payload.phone) {
            query.orWhere('phone', payload.phone)
          }
        })
        .first()

      if (existingRegistration) {
        return response.badRequest({
          success: false,
          message: `A registration already exists with this ${existingRegistration.email === payload.email ? 'email' : 'phone number'}`,
          data: existingRegistration,
        })
      }

      const registration = await this.eventService.registerEvent(
        eventId,
        {
          name: payload.name,
          email: payload.email || '',
          phone: payload.phone || '',
          languages: payload.languages,
          occupation: payload.occupation,
          source: ERegistrationSource.ADMIN,
          userId: payload.userId,
        },
        false,
        adminId
      )

      if (registration.email) {
        await this.eventService.sendRegistrationEmail(registration.id)
      }

      return response.ok({
        success: true,
        data: registration,
        message: 'Participant registered successfully',
      })
    } catch (error) {
      console.error('Error in registration:', error)
      return response.badRequest({
        success: false,
        message: error.message || 'Failed to register participant',
      })
    }
  }

  /**
   * @deleteRegistration
   * @tag Admin Event
   * @summary Delete an event registration
   * @paramParams id - Registration ID - @type(number)
   * @responseBody 200 - {"success":true,"message":"Event registration deleted successfully"} - Success response
   * @responseBody 404 - {"success":false,"message":"Event registration not found"} - Not Found
   * @responseBody 500 - {"success":false,"message":"Failed to delete event registration"} - Internal Server Error
   */
  public async deleteRegistration({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.DELETE_REGISTRATION, RESOURCE.EVENT)

    try {
      const registrationId = params.id

      const registration = await ZnEventRegistration.find(registrationId)
      if (!registration) {
        return response.status(404).json({
          success: false,
          message: 'Event registration not found',
        })
      }

      await registration.delete()

      return response.json({
        success: true,
        message: 'Event registration deleted successfully',
      })
    } catch (error) {
      return response.status(500).json({
        success: false,
        message: 'Failed to delete event registration',
        error: error.message,
      })
    }
  }

  /**
   * @regenerateQRCode
   * @tag Admin Event
   * @summary Regenerate QR code for an event
   * @paramPath id - ID of Event - @type(string) @required
   * @responseBody 200 - {"success":true,"data":<ZnEvent>,"message":"QR code regenerated successfully"} - QR code regenerated successfully
   * @responseBody 400 - {"success":false,"message":"Failed to regenerate QR code"} - Bad Request
   * @responseBody 404 - {"success":false,"message":"Event not found"} - Not Found
   */
  public async regenerateQRCode({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.EVENT)

    try {
      const event = await ZnEvent.findOrFail(params.id)

      // // Generate new QR code with timestamp to ensure uniqueness
      // const timestamp = new Date().getTime()
      // const qrCode = `placeholder-for-qr-code-${event.id}-${timestamp}`
      const qrCode = `placeholder-for-qr-code-${event.id}`

      event.qrCode = qrCode
      await event.save()

      return response.ok({
        success: true,
        data: event,
        message: 'QR code regenerated successfully',
      })
    } catch (error) {
      console.error('Error regenerating QR code:', error)
      if (error.code === 'E_ROW_NOT_FOUND') {
        return response.notFound({
          success: false,
          message: 'Event not found',
        })
      }
      return response.badRequest({
        success: false,
        message: error.message || 'Failed to regenerate QR code',
      })
    }
  }

  /**
   * @suggestSlugAndUrl
   * @tag Admin Event
   * @summary Get suggested slug and shareUrl for an event
   * @paramQuery title - Event title to generate slug from - @type(string) @required
   * @responseBody 200 - {"success":true,"data":{"slug":"string","shareUrl":"string"}} - Suggested slug and URL
   * @responseBody 400 - {"success":false,"message":"Title is required"} - Bad Request
   */
  public async suggestSlugAndUrl({ request, response }: HttpContext) {
    try {
      const title = request.input('title')

      if (!title) {
        return response.badRequest({
          success: false,
          message: 'Title is required',
        })
      }

      const result = await this.eventService.generateSuggestedSlugAndUrl(title)

      return response.ok({
        success: true,
        data: result,
      })
    } catch (error) {
      console.error('Error generating suggested slug and URL:', error)
      return response.badRequest({
        success: false,
        message: error.message || 'Failed to generate suggested slug and URL',
      })
    }
  }

  /**
   * @updateStatus
   * @tag Admin Event
   * @summary Update event status
   * @paramPath id - ID of Event - @type(string) @required
   * @requestBody {"status":"scheduled|active|completed|cancelled"}
   * @responseBody 200 - {"success":true,"data":<ZnEvent>,"message":"Status updated successfully"}
   * @responseBody 400 - {"success":false,"message":"Invalid status or event not found"}
   */
  public async updateStatus({ bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.EVENT)
    try {
      const event = await ZnEvent.findOrFail(params.id)
      const status = request.input('status')
      if (!status || !Object.values(EEventStatus).includes(status)) {
        return response.badRequest({
          success: false,
          message: 'Invalid status value',
        })
      }
      event.status = status
      await event.save()
      return response.ok({
        success: true,
        data: event,
        message: 'Status updated successfully',
      })
    } catch (error) {
      console.error('Error updating event status:', error)
      return response.badRequest({
        success: false,
        message: error.message || 'Failed to update event status',
      })
    }
  }
}
