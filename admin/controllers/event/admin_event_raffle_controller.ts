import { ACTION, RESOURCE } from "#constants/authorization";
import { NOTIFICATION_TYPE } from "#constants/notification";
import RaffleWinnerNotification from "#mails/event/raffle_winner_notification";
import Notification from "#models/notification";
import ZnEventRaffle from "#models/zn_event_raffle";
import ZnEventRegistration from "#models/zn_event_registration";
import { SMSService } from "#services/aws/sms_service";
import EventRegistrationService from "#services/events/event-registration-services";
import { NotificationService } from "#services/notification_service";
import { HttpContext } from "@adonisjs/core/http";
import mail from "@adonisjs/mail/services/main";
import EventService from "#services/events/event_service";
import ZnProductVariant from "#models/zn_product_variant";

export default class AdminEventLuckyWheelController {
  private eventRegistrationService;
  private eventService;

  constructor() {
    this.eventRegistrationService = new EventRegistrationService();
    this.eventService = new EventService();
  }

  async list({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.EVENT);
      const eventId = params.id;

      return await ZnEventRegistration.query()
        .where('eventId', eventId)
        .whereHas('raffle', (query) => {
          query.whereNotNull('prize');
        })
        .preload('raffle');

    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async prizes({ bouncer, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.EVENT);

      const prizes = await this.eventService.getPrizes()

      return response.ok(prizes);

    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async assignLuckyNumbers({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.EVENT);
      const eventId = params.id;

      const registrations = await this.eventRegistrationService.getRegistrations({
        eventId,
        checkedIn: true
      });

      if (registrations.length > 0) {
        for (let i = 0; i < registrations.length; i++) {
          await ZnEventRaffle.updateOrCreate({
            registrationId: registrations[i].id
          }, {
            luckyNumber: i + 1
          })
        }

        return response.ok('Lucky numbers are assigned');

      } else {
        return response.ok({
          success: false,
          message: 'No one checked-in yet'
        });
      }

    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async store({ bouncer, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.EVENT);
      const { registrationId, prize } = request.body();

      const raffle = await ZnEventRaffle.findByOrFail('registrationId', registrationId);
      if (raffle.prize && raffle.prize.length > 0) {
        return response.badRequest('This registration was already rewarded');
      }
      raffle.prize = prize;

      const prizes = await this.eventService.getPrizes()
      let variants: ZnProductVariant[]
      if (prize === 'Grand Prize') {
        // @ts-ignore
        variants = prizes?.grandPrize;
      } else if (prize === 'Special Prize') {
        // @ts-ignore
        variants = prizes?.specialPrize;
      } else {
        return response.badRequest('The prize param is required.');
      }

      await raffle.save();
      await raffle.related('rewards').sync(variants.map(item => item.id));
      await raffle.load('rewards', (query) => {
        query.preload('image');
      });

      const registration = await ZnEventRegistration.query()
        .where('id', registrationId)
        .preload('user')
        .firstOrFail();

      await this.sendEmail(registration, raffle);
      await this.sendSMS(registration, raffle);
      await this.sendPushNotification(registration, raffle);

      return response.ok({
        success: true,
        raffle
      })

    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async withdrawReward({ bouncer, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.EVENT);
      const { registrationId } = request.body();

      const raffle = await ZnEventRaffle.findByOrFail('registrationId', registrationId);
      if (raffle.prize && raffle.prize.length > 0) {
        raffle.prize = null;
        await raffle.save();
        await raffle.related('rewards').sync([]);

        const registration = await ZnEventRegistration.query()
          .where('id', registrationId)
          .preload('raffle')
          .firstOrFail();

        return response.ok({
          success: true,
          registration
        })
      } else {
        const registration = await ZnEventRegistration.query()
          .where('id', registrationId)
          .preload('raffle')
          .firstOrFail();

        return response.notModified({
          success: false,
          messaging: 'This participant is not rewarded yet',
          registration
        });
      }

    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  private async sendEmail(registration: ZnEventRegistration, raffle: ZnEventRaffle) {
    if (registration.email) {
      await mail
        .send(new RaffleWinnerNotification(registration, raffle))
        .then(() => {
          console.log(`Raffle winner sent to email: ${registration.email}`)
        })
        .catch((error) => {
          console.log('Error when sending raffle winner mail', error)
        })
    }
  }

  private async sendSMS(registration: ZnEventRegistration, raffle: ZnEventRaffle) {
    if (registration.phone) {
      const message = `Congratulations! You've won the Zurno Raffle ${raffle.prize}! To claim, email <EMAIL> with subject '[PREMIERE ORLANDO] ZURNO RAFFLE WINNER INFORMATION' and include name, address & phone number. Thank you for participating. Best regards, The Zurno Team.`;

      const smsService = new SMSService();
      await smsService.send(registration.phone, message);
    }
  }

  private async sendPushNotification(registration: ZnEventRegistration, raffle: ZnEventRaffle) {
    if (registration.user) {
      const notification = await Notification.create({
        type: NOTIFICATION_TYPE.EVENT,
        userId: registration.user.id,
        resourceId: raffle.id,
        title: `You've Won the ${raffle.prize}!`,
        description: `Congratulations! You've won the Zurno Raffle ${raffle.prize}! Check your email for details on how to claim your reward!`,
      })

      if (notification) {
        const notificationService = new NotificationService()
        await notificationService.send([registration.user], [notification])
      }
    }
  }
}
