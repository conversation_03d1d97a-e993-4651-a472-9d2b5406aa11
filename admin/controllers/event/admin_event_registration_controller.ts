import { ACTION, RESOURCE } from "#constants/authorization";
import ZnEventRegistration from "#models/zn_event_registration";
import EventRegistrationService from "#services/events/event-registration-services";
import { HttpContext } from "@adonisjs/core/http";
import ExcelJS from 'exceljs';
import { uploadReports } from "../../../services/media/index.js";

export default class AdminEventRegistrationController {
  private eventRegistrationService;

  constructor() {
    this.eventRegistrationService = new EventRegistrationService();
  }

  async index({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.EVENT);
      const eventId = params.id;
      const { checkedIn, hasLuckyNumber, rewarded, page, limit } = request.qs();

      const registrations = await this.eventRegistrationService.getRegistrations({
        eventId,
        checkedIn,
        hasLuckyNumber,
        rewarded,
        page,
        limit
      });

      return response.ok(registrations);
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async exportRegistrations({ params, response }: HttpContext) {
    const { id } = params

    const registrations = await ZnEventRegistration.query()
      .where({ eventId: id })
      .preload('event')
      .preload('languages')
      .preload('user')
      .orderBy('createdAt', 'desc')

    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('Registrations')

    worksheet.addRow([
      'Name',
      'Email',
      'Phone',
      'Registered From',
      'Occupation',
      'Languages',
      'Registered Time',
      'Status',
    ])

    for (const record of registrations) {
      worksheet.addRow([
        record.name,
        record.email,
        record.phone,
        record.source,
        record.occupation,
        record.languages.map(lan => lan.name).join(','),
        record.createdAt?.toFormat('MMM dd, yyyy hh:mm a'),
        record.checkedIn ? "Checked-in" : "Not Checked-in"
      ])
    }

    const buffer = (await workbook.xlsx.writeBuffer()) as unknown as Buffer

    const reports = await uploadReports([{ buffer: buffer }])

    return response.ok({
      filename: `Registrations.xlsx`,
      url: reports[0].url,
    })
  }

}