import ZnCity from "#models/zn_city";
import ZnCountry from "#models/zn_country";
import ZnState from "#models/zn_state";
import { HttpContext } from "@adonisjs/core/http";

export default class AdminLocationController {
    /**
     * @selectCountries
     * @tag Admin Location
     * @summary Read Country selection
     * @paramQuery page - Page Number (default 1) - @type(number)
     * @paramQuery limit - Page Limit (default 10) - @type(number)
     * @paramQuery search - Search Term - @type(string)
     * @responseBody 200 - <ZnCountry[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null).paginated() - Read Country selection descriptively
     * @responseBody 401 - Unauthorized access - Unauthorized
     */
    async selectCountries({ request, response }: HttpContext) {
        const {
            page = 1,
            limit = 10,
            search,
        } = request.all()

        const query = ZnCountry.query()
            .orderBy('name', 'asc')

        if (search) {
            query.whereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
        }

        const result = await query.paginate(page, limit)

        return response.ok(result)
    }

    /**
     * @selectStates
     * @tag Admin Location
     * @summary Read State selection
     * @paramQuery page - Page Number (default 1) - @type(number)
     * @paramQuery limit - Page Limit (default 10) - @type(number)
     * @paramQuery search - Search Term - @type(string)
     * @paramQuery countryIds - ID of Country - @type(array)
     * @responseBody 200 - <ZnState[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null).paginated() - Read State selection descriptively
     * @responseBody 401 - Unauthorized access - Unauthorized
     */
    async selectStates({ request, response }: HttpContext) {
        const {
            page = 1,
            limit = 10,
            search,
            countryIds,
        } = request.all()

        const query = ZnState.query()
            .orderBy('name', 'asc')

        if (search) {
            query.whereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
        }

        if (countryIds) {
            let ids = [countryIds]
            if (Array.isArray(countryIds)) {
                ids = countryIds
            }

            query.whereIn('countryId', ids)
        }

        const result = await query.paginate(page, limit)

        return response.ok(result)
    }

    /**
     * @selectCities
     * @tag Admin Location
     * @summary Read City selection
     * @paramQuery page - Page Number (default 1) - @type(number)
     * @paramQuery limit - Page Limit (default 10) - @type(number)
     * @paramQuery search - Search Term - @type(string)
     * @paramQuery stateIds - IDs of State - @type(array)
     * @responseBody 200 - <ZnCity[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null).paginated() - Read City selection descriptively
     * @responseBody 401 - Unauthorized access - Unauthorized
     */
    async selectCities({ request, response }: HttpContext) {
        const {
            page = 1,
            limit = 10,
            search,
            stateIds,
        } = request.all()

        const query = ZnCity.query()
            .orderBy('name', 'asc')

        if (search) {
            query.whereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
        }

        if (stateIds) {
            let ids = [stateIds]
            if (Array.isArray(stateIds)) {
                ids = stateIds
            }

            query.whereIn('stateId', ids)
        }

        const result = await query.paginate(page, limit)

        return response.ok(result)

    }
}
