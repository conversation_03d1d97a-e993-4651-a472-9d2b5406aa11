import ZnInventoryMovement from '#models/zn_inventory_movement'
import type { HttpContext } from '@adonisjs/core/http'

export default class InventoryMovementsController {
  /**
   * @index
   * @tag Admin Inventory Movements
   * @summary Read all inventory movements
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 20) - @type(number)
   * @paramQuery search - Search terrm - @type(string)
   * @responseBody 200 - <ZnInventoryMovement[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null).paginated() - Read all inventory movements descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display a list of resource
   */
  async index({ request, response }: HttpContext) {
    const { page = 1, limit = 20, search } = request.qs()

    try {
      const query = ZnInventoryMovement.query()
        .preload('variant')
        .preload('createAdmin')
        .preload('writeAdmin')
        .preload('fromLocation', (fromQuery) => {
          fromQuery.preload('warehouse')
        })
        .preload('toLocation', (fromQuery) => {
          fromQuery.preload('warehouse')
        })

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder.whereHas('variant', (variantQuery) => {
            variantQuery.where('sku', search)
          })
        })
      }

      query.orderBy('createdAt', 'desc')

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      console.log(error)
      return response.internalServerError(error)
    }
  }
}
