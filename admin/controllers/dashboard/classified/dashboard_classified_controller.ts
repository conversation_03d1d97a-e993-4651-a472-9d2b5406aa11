import ZnPost from '#models/zn_post'
import ZnStore from '#models/zn_store'
import { HttpContext } from '@adonisjs/core/http'
import db from '@adonisjs/lucid/services/db'
import { startOfDay, endOfDay, subDays, format, eachDayOfInterval } from 'date-fns'
import { AmazonS3StorageService } from '../../../../services/aws/s3/aws-s3.service.js'
import ZnUser from '#models/zn_user'
import ZnPostCategory from '#models/zn_post_category'
import ZnNailSystems from '#models/zn_nail_system'
import { CacheResponse } from '../../../../app/decorators/cache_response.decorator.js'

export default class AdminDashboardClassifiedController {
  @CacheResponse()
  async getClassifiedTotalPostsAndStores({ response }: HttpContext) {
    let result = {
      totalPost: 0,
      totalStore: 0,
      totalAccounts: 0,
      postChangePercent: 0,
      storeChangePercent: 0,
      accountChangePercent: 0,
    }

    try {
      const today = new Date()
      const yesterday = subDays(today, 1)

      const todayStart = startOfDay(today)
      const todayEnd = endOfDay(today)
      const yesterdayStart = startOfDay(yesterday)
      const yesterdayEnd = endOfDay(yesterday)

      const [
        totalPosts,
        totalStores,
        totalAccounts,
        todayPosts,
        todayStores,
        todayAccounts,
        yesterdayPosts,
        yesterdayStores,
        yesterdayAccounts,
      ] = await Promise.all([
        ZnPost.query().count('* as total').first(),
        ZnStore.query().count('* as total').first(),
        ZnUser.query().count('* as total').first(),
        ZnPost.query()
          .whereBetween('createdAt', [todayStart, todayEnd])
          .count('* as total')
          .first(),
        ZnStore.query()
          .whereBetween('createdAt', [todayStart, todayEnd])
          .count('* as total')
          .first(),
        ZnUser.query()
          .whereBetween('createdAt', [todayStart, todayEnd])
          .count('* as total')
          .first(),
        ZnPost.query()
          .whereBetween('createdAt', [yesterdayStart, yesterdayEnd])
          .count('* as total')
          .first(),
        ZnStore.query()
          .whereBetween('createdAt', [yesterdayStart, yesterdayEnd])
          .count('* as total')
          .first(),
        ZnUser.query()
          .whereBetween('createdAt', [yesterdayStart, yesterdayEnd])
          .count('* as total')
          .first(),
      ])

      const totalPostCount = totalPosts?.$extras['total'] || 0
      const totalStoreCount = totalStores?.$extras['total'] || 0
      const totalAccountCount = totalAccounts?.$extras['total'] || 0
      const todayPostCount = todayPosts?.$extras['total'] || 0
      const todayStoreCount = todayStores?.$extras['total'] || 0
      const todayAccountCount = todayAccounts?.$extras['total'] || 0
      const yesterdayPostCount = yesterdayPosts?.$extras['total'] || 0
      const yesterdayStoreCount = yesterdayStores?.$extras['total'] || 0
      const yesterdayAccountCount = yesterdayAccounts?.$extras['total'] || 0

      result.totalPost = totalPostCount
      result.totalStore = totalStoreCount
      result.totalAccounts = totalAccountCount

      result.postChangePercent = yesterdayPostCount
        ? Math.round(((todayPostCount - yesterdayPostCount) / yesterdayPostCount) * 100)
        : 0

      result.storeChangePercent = yesterdayStoreCount
        ? Math.round(((todayStoreCount - yesterdayStoreCount) / yesterdayStoreCount) * 100)
        : 0

      result.accountChangePercent = yesterdayAccountCount
        ? Math.round(((todayAccountCount - yesterdayAccountCount) / yesterdayAccountCount) * 100)
        : 0

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  @CacheResponse()
  async getMonthlyPostCounts({ request, response }: HttpContext) {
    try {
      const { startDate, endDate } = request.qs()

      let startOfDateRange: Date | null = null
      let endOfDateRange: Date | null = null

      if (startDate) {
        startOfDateRange = new Date(startDate)
        if (isNaN(startOfDateRange.getTime())) {
          return response.badRequest({
            message: 'Invalid date format for startDate',
          })
        }
      }

      if (endDate) {
        endOfDateRange = new Date(endDate)
        if (isNaN(endOfDateRange.getTime())) {
          return response.badRequest({
            message: 'Invalid date format for endDate',
          })
        }
      }

      if (startOfDateRange && endOfDateRange) {
        const diffInDays =
          (endOfDateRange.getTime() - startOfDateRange.getTime()) / (1000 * 60 * 60 * 24)

        if (diffInDays <= 7) {
          const query = `
            SELECT 
              DATE(createdAt) AS label,
              COUNT(*) AS total
            FROM zn_posts
            WHERE deletedAt IS NULL
            AND createdAt >= ?
            AND createdAt <= ?
            GROUP BY DATE(createdAt)
            ORDER BY DATE(createdAt)
          `

          const dailyPostCounts = await db.rawQuery(query, [startOfDateRange, endOfDateRange])

          const allDates = eachDayOfInterval({
            start: startOfDateRange,
            end: endOfDateRange,
          })

          const result = allDates.map((date) => {
            const formattedDate = format(date, 'yyyy-MM-dd')
            const found = dailyPostCounts[0].find(
              (row: any) => format(new Date(row.label), 'yyyy-MM-dd') === formattedDate
            )

            return {
              label: format(date, 'dd MMM'),
              total: found ? found.total : 0,
            }
          })

          return response.ok(result)
        }
      }

      const query = `
        SELECT 
          MONTH(createdAt) AS label,
          COUNT(*) AS total
        FROM zn_posts
        WHERE deletedAt IS NULL
        ${startOfDateRange ? 'AND createdAt >= ?' : ''}
        ${endOfDateRange ? 'AND createdAt <= ?' : ''}
        GROUP BY MONTH(createdAt)
        ORDER BY MONTH(createdAt)
      `

      const queryParams: (Date | null)[] = []
      if (startOfDateRange) queryParams.push(startOfDateRange)
      if (endOfDateRange) queryParams.push(endOfDateRange)

      const monthlyPostCounts = await db.rawQuery(query, queryParams)

      const monthNames = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
      ]

      const result = Array.from({ length: 12 }, (_, index) => ({
        label: monthNames[index],
        total: 0,
      }))

      monthlyPostCounts[0].forEach((row: any) => {
        result[row.label - 1].total = row.total
      })

      return response.ok(result)
    } catch (error) {
      return response.badRequest({
        message: 'An error occurred while fetching post counts',
        error: error.message,
      })
    }
  }

  @CacheResponse()
  async getTopUsersByPosts({ request, response }: HttpContext) {
    try {
      const { startDate, endDate } = request.qs()

      const limit = 5

      let dateCondition = ''
      const queryParams: any[] = [limit]
      if (startDate) {
        const parsedStartDate = new Date(startDate)
        if (isNaN(parsedStartDate.getTime())) {
          return response.badRequest({ message: 'Invalid startDate format' })
        }
        dateCondition += ' AND posts.createdAt >= ?'
        queryParams.unshift(parsedStartDate)
      }

      if (endDate) {
        const parsedEndDate = new Date(endDate)
        if (isNaN(parsedEndDate.getTime())) {
          return response.badRequest({ message: 'Invalid endDate format' })
        }
        dateCondition += ' AND posts.createdAt <= ?'
        queryParams.unshift(parsedEndDate)
      }

      const topUsers = await db.rawQuery(
        `
        SELECT 
          users.id AS userId,
          CONCAT(users.firstName, ' ', users.lastName) AS fullName,
          users.avatar AS avatar,
          COUNT(posts.id) AS postCount
        FROM zn_users AS users
        INNER JOIN zn_posts AS posts ON users.id = posts.userId
        WHERE posts.deletedAt IS NULL
        ${dateCondition}
        GROUP BY users.id
        ORDER BY postCount DESC
        LIMIT ?
        `,
        queryParams
      )

      const awsService = new AmazonS3StorageService()
      const result = topUsers[0].map((user: ZnUser) => ({
        ...user,
        avatar: user.avatar
          ? /(http(s?)):\/\//i.test(user.avatar)
            ? user.avatar
            : awsService.getFullUrl() + user.avatar
          : null,
      }))

      return response.ok(result)
    } catch (error) {
      return response.badRequest({
        message: 'An error occurred while fetching top users by posts',
        error: error.message,
      })
    }
  }

  @CacheResponse()
  async getRecentPosts({ request, response }: HttpContext) {
    try {
      const limit = parseInt(request.qs().limit) || 5

      const recentPosts = await ZnPost.query()
        .preload('thumbnail', (thumbnailQuery) => {
          thumbnailQuery.select(['id', 'fileKey'])
        })
        .preload('user', (query) => {
          query.select(['id', 'firstName', 'lastName'])
        })
        .preload('createdByAdmin', (query) => {
          query.select(['id', 'name', 'username'])
        })
        .preload('city', (cityQuery) => {
          cityQuery.select(['id', 'name', 'stateId']).preload('state', (stateQuery) => {
            stateQuery.select(['id', 'name', 'countryId']).preload('country', (countryQuery) => {
              countryQuery.select(['id', 'name'])
            })
          })
        })
        .preload('categories', (query) => {
          query.select(['id', 'name'])
        })
        .select([
          'id',
          'title',
          'price',
          'userId',
          'cityId',
          'createdAt',
          'thumbnailId',
          'createdByAdminId',
        ])
        .whereNull('deletedAt')
        .orderBy('createdAt', 'desc')
        .limit(limit)

      for (const post of recentPosts) {
        if (typeof post.createdByAdminId === 'undefined') {
          post.createdByAdminId = null
        }
      }

      const result = recentPosts.map((post) => {
        const user = post.user
          ? `${post.user.firstName} ${post.user.lastName}`
          : post.createdByAdmin
            ? 'Zurno'
            : null

        const city = post.city?.name || null
        const state = post.city?.state?.name || null
        const country = post.city?.state?.country?.name || null
        const location =
          city || state || country
            ? `${city || ''}, ${state || ''}, ${country || ''}`.replace(/,\s*$/, '')
            : null

        const category =
          post.categories.length > 0 ? post.categories.map((cat) => cat.name).join('/') : null

        const thumbnail = post.thumbnail && post.thumbnail.url ? post.thumbnail.url : null

        return {
          id: post.id,
          account: user,
          category: category,
          title: post.title || null,
          price: post.price || 0,
          location: location,
          thumbnail: thumbnail,
        }
      })

      return response.ok(result)
    } catch (error) {
      return response.status(500).send({
        message: 'An error occurred while fetching recent posts',
        error: error.message,
      })
    }
  }

  @CacheResponse()
  async getPostCountByCategory({ request, response }: HttpContext) {
    try {
      const { startDate, endDate } = request.qs()

      const query = ZnPostCategory.query()
        .select('id', 'name')
        .withCount('posts', (postsQuery) => {
          if (startDate) {
            postsQuery.where('createdAt', '>=', new Date(startDate))
          }
          if (endDate) {
            postsQuery.where('createdAt', '<=', new Date(endDate))
          }
        })
        .orderBy('posts_count', 'desc')

      const categories = await query

      const topCategories = categories.slice(0, 5).map((category) => ({
        id: category.id,
        name: category.name,
        totalPosts: category.$extras.posts_count || 0,
      }))

      const otherCount = categories.slice(5).reduce((sum, category) => {
        return sum + (category.$extras.posts_count || 0)
      }, 0)

      if (otherCount > 0) {
        topCategories.push({
          id: 'other',
          name: 'Other',
          totalPosts: otherCount,
        })
      }

      return response.ok(topCategories)
    } catch (error) {
      console.error('Error fetching post counts by category:', error)
      return response.status(500).send({
        message: 'An error occurred while fetching post counts by category',
        error: error.message,
      })
    }
  }

  @CacheResponse()
  async getStoreCountByNailSystem({ request, response }: HttpContext) {
    try {
      const { startDate, endDate } = request.qs()

      const query = ZnNailSystems.query()
        .select('id', 'name')
        .withCount('stores', (storesQuery) => {
          if (startDate) {
            storesQuery.where('createdAt', '>=', new Date(startDate))
          }
          if (endDate) {
            storesQuery.where('createdAt', '<=', new Date(endDate))
          }
        })
        .orderBy('stores_count', 'desc')

      const nailSystems = await query

      const topNailSystems = nailSystems.slice(0, 5).map((system) => ({
        id: system.id,
        name: system.name,
        totalStores: system.$extras.stores_count || 0,
      }))

      const otherCount = nailSystems.slice(5).reduce((sum, system) => {
        return sum + (system.$extras.stores_count || 0)
      }, 0)

      if (otherCount > 0) {
        topNailSystems.push({
          id: 'other',
          name: 'Other',
          totalStores: otherCount,
        })
      }

      return response.ok(topNailSystems)
    } catch (error) {
      return response.status(500).send({
        message: 'An error occurred while fetching store counts by nail system',
        error: error.message,
      })
    }
  }
}
