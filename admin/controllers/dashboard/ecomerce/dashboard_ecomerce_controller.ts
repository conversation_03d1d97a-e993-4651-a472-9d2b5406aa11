import ZnOrder from '#models/zn_order'
import ZnOrderDetail from '#models/zn_order_detail'
import ZnProduct from '#models/zn_product'
import ZnUser from '#models/zn_user'
import { HttpContext } from '@adonisjs/core/http'
import db from '@adonisjs/lucid/services/db'
import { eachDayOfInterval, endOfDay, format, startOfDay, subDays } from 'date-fns'
import { CacheResponse } from '../../../../app/decorators/cache_response.decorator.js'
import { TRACKING_ACTION } from '#constants/tracking'

export default class AdminDashboardEcomerceController {
  @CacheResponse()
  async getDashboardEcomerceTotals({ response }: HttpContext) {
    try {
      const today = new Date()
      const yesterday = subDays(today, 1)

      const todayStart = startOfDay(today)
      const todayEnd = endOfDay(today)
      const yesterdayStart = startOfDay(yesterday)
      const yesterdayEnd = endOfDay(yesterday)

      const [
        totalProducts,
        todayOrders,
        todaySales,
        todayProducts,
        yesterdayProducts,
        yesterdayOrders,
        yesterdaySales,
      ] = await Promise.all([
        ZnProduct.query().whereNull('deletedAt').count('* as total').first(),
        ZnOrder.query()
          .whereNull('deletedAt')
          .whereBetween('createdAt', [todayStart, todayEnd])
          .count('* as total')
          .first(),
        ZnOrder.query()
          .whereNull('deletedAt')
          .whereBetween('createdAt', [todayStart, todayEnd])
          .sum('totalPrice as total')
          .first(),
        ZnProduct.query()
          .whereNull('deletedAt')
          .whereBetween('createdAt', [todayStart, todayEnd])
          .count('* as total')
          .first(),
        ZnProduct.query()
          .whereNull('deletedAt')
          .whereBetween('createdAt', [yesterdayStart, yesterdayEnd])
          .count('* as total')
          .first(),
        ZnOrder.query()
          .whereNull('deletedAt')
          .whereBetween('createdAt', [yesterdayStart, yesterdayEnd])
          .count('* as total')
          .first(),
        ZnOrder.query()
          .whereNull('deletedAt')
          .whereBetween('createdAt', [yesterdayStart, yesterdayEnd])
          .sum('totalPrice as total')
          .first(),
      ])

      const result = {
        totalProduct: Number(totalProducts?.$extras.total || 0),
        totalOrder: Number(todayOrders?.$extras.total || 0),
        totalSale: Number(todaySales?.$extras.total || 0),
        productChangePercent: 0,
        orderChangePercent: 0,
        saleChangePercent: 0,
      }

      const todayProductCount = Number(todayProducts?.$extras.total || 0)
      const yesterdayProductCount = Number(yesterdayProducts?.$extras.total || 0)
      const todayOrderCount = Number(todayOrders?.$extras.total || 0)
      const yesterdayOrderCount = Number(yesterdayOrders?.$extras.total || 0)
      const todaySalesAmount = Number(todaySales?.$extras.total || 0)
      const yesterdaySalesAmount = Number(yesterdaySales?.$extras.total || 0)

      if (yesterdayProductCount > 0) {
        result.productChangePercent = Math.round(
          ((todayProductCount - yesterdayProductCount) / yesterdayProductCount) * 100
        )
      }

      if (yesterdayOrderCount > 0) {
        result.orderChangePercent = Math.round(
          ((todayOrderCount - yesterdayOrderCount) / yesterdayOrderCount) * 100
        )
      }

      if (yesterdaySalesAmount > 0) {
        result.saleChangePercent = Math.round(
          ((todaySalesAmount - yesterdaySalesAmount) / yesterdaySalesAmount) * 100
        )
      }

      return response.ok(result)
    } catch (error) {
      console.error('Error in getDashboardEcomerceTotals:', error)
      return response.internalServerError({
        message: 'An error occurred while fetching dashboard totals',
        error: error.message,
      })
    }
  }

  @CacheResponse()
  async getMonthlySaleCounts({ request, response }: HttpContext) {
    try {
      const { startDate, endDate } = request.qs()

      let startOfDateRange: Date | null = null
      let endOfDateRange: Date | null = null

      if (startDate) {
        startOfDateRange = new Date(startDate)
        if (isNaN(startOfDateRange.getTime())) {
          return response.badRequest({
            message: 'Invalid date format for startDate',
          })
        }
      }

      if (endDate) {
        endOfDateRange = new Date(endDate)
        if (isNaN(endOfDateRange.getTime())) {
          return response.badRequest({
            message: 'Invalid date format for endDate',
          })
        }
      }

      if (startOfDateRange && endOfDateRange) {
        const diffInDays =
          (endOfDateRange.getTime() - startOfDateRange.getTime()) / (1000 * 60 * 60 * 24)

        if (diffInDays <= 7) {
          const query = `
            SELECT 
              DATE(createdAt) AS label,
              SUM(totalPrice) AS total
            FROM zn_orders
            WHERE deletedAt IS NULL
            AND createdAt >= ?
            AND createdAt <= ?
            GROUP BY DATE(createdAt)
            ORDER BY DATE(createdAt)
          `

          const dailySales = await db.rawQuery(query, [startOfDateRange, endOfDateRange])

          const allDates = eachDayOfInterval({
            start: startOfDateRange,
            end: endOfDateRange,
          })

          const result = allDates.map((date) => {
            const formattedDate = format(date, 'yyyy-MM-dd')
            const found = dailySales[0].find(
              (row: any) => format(new Date(row.label), 'yyyy-MM-dd') === formattedDate
            )

            return {
              label: format(date, 'dd MMM'),
              total: found ? found.total : 0,
            }
          })

          return response.ok(result)
        }
      }

      const query = `
        SELECT 
          MONTH(createdAt) AS label,
          SUM(totalPrice) AS total
        FROM zn_orders
        WHERE deletedAt IS NULL
        ${startOfDateRange ? 'AND createdAt >= ?' : ''}
        ${endOfDateRange ? 'AND createdAt <= ?' : ''}
        GROUP BY MONTH(createdAt)
        ORDER BY MONTH(createdAt)
      `

      const queryParams: (Date | null)[] = []
      if (startOfDateRange) queryParams.push(startOfDateRange)
      if (endOfDateRange) queryParams.push(endOfDateRange)

      const monthlySales = await db.rawQuery(query, queryParams)

      const monthNames = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
      ]

      const result = Array.from({ length: 12 }, (_, index) => ({
        label: monthNames[index],
        total: 0,
      }))

      if (monthlySales?.[0]?.length > 0) {
        monthlySales[0].forEach((row: any) => {
          result[row.label - 1].total = row.total
        })
      }

      return response.ok(result)
    } catch (error) {
      console.error('Error fetching monthly sales:', error)
      return response.status(500).send({
        message: 'An error occurred while fetching monthly sales',
        error: error.message,
      })
    }
  }

  @CacheResponse()
  async getRecentOrders({ response }: HttpContext) {
    try {
      const limit = 10

      const recentOrders = await ZnOrder.query()
        .select(['id', 'name', 'userId', 'totalPrice', 'fulfillmentStatus', 'createdAt'])
        .whereNull('deletedAt')
        .orderBy('createdAt', 'desc')
        .limit(limit)

      const orderIds = recentOrders.map((order) => order.id)
      const userIds = recentOrders.map((order) => order.userId).filter(Boolean)

      const users = await ZnUser.query()
        .select(['id', 'firstName', 'lastName'])
        .whereIn('id', userIds as any)

      const orderDetails = await ZnOrderDetail.query()
        .select(['orderId', 'quantity'])
        .whereIn('orderId', orderIds as any)

      const userMap = new Map(
        users.map((user) => [user.id, `${user.firstName || ''} ${user.lastName || ''}`.trim()])
      )

      const orderDetailsMap = new Map()
      orderDetails.forEach((detail) => {
        if (!orderDetailsMap.has(detail.orderId)) {
          orderDetailsMap.set(detail.orderId, 0)
        }
        orderDetailsMap.set(
          detail.orderId,
          orderDetailsMap.get(detail.orderId) + (detail.quantity || 0)
        )
      })

      const result = recentOrders.map((order) => {
        const customer = order.userId ? userMap.get(order.userId) || 'Unknown' : 'Unknown'

        const totalPrice = order.totalPrice || 0
        const totalItems = orderDetailsMap.get(order.id) || 0

        return {
          orderId: order.id,
          customer: customer,
          totalItems: totalItems,
          totalPrice: `$${totalPrice.toFixed(2)}`,
          status: order.fulfillmentStatus,
          createdAt: order.createdAt,
        }
      })

      return response.ok(result)
    } catch (error) {
      return response.status(500).send({
        message: 'An error occurred while fetching recent orders',
        error: error.message,
      })
    }
  }

  @CacheResponse()
  async getOrderCountByFulfillmentStatus({ request, response }: HttpContext) {
    try {
      const { startDate, endDate } = request.qs()

      const query = ZnOrder.query()
        .select('fulfillmentStatus')
        .count('* as total')
        .groupBy('fulfillmentStatus')

      if (startDate) {
        query.where('createdAt', '>=', new Date(startDate))
      }
      if (endDate) {
        query.where('createdAt', '<=', new Date(endDate))
      }

      const orderCounts = await query

      const result = {
        shipped: 0,
        partial: 0,
        unshipped: 0,
        unfulfilled: 0,
        any: 0,
      }

      orderCounts.forEach((row) => {
        const status = row.fulfillmentStatus ? row.fulfillmentStatus.toLowerCase() : 'unshipped'
        const count = row.$extras.total || 0

        if (status === 'fulfilled') {
          result.shipped = count
        } else if (status === 'partial') {
          result.partial = count
        } else if (status === 'unfulfilled') {
          result.unfulfilled = count
        } else if (status === 'unshipped') {
          result.unshipped = count
        }
      })

      result.any = Object.values(result).reduce((sum, value) => sum + value, 0)

      return response.ok(result)
    } catch (error) {
      console.error('Error fetching order counts by fulfillment status:', error)
      return response.status(500).send({
        message: 'An error occurred while fetching order counts by fulfillment status',
        error: error.message,
      })
    }
  }

  @CacheResponse()
  async getTopCustomers({ request, response }: HttpContext) {
    try {
      const { startDate, endDate } = request.qs()

      const startDateTime = startDate ? new Date(startDate) : null
      const endDateTime = endDate ? new Date(endDate) : null

      if (startDateTime && endDateTime && endDateTime < startDateTime) {
        return response.badRequest({ message: 'End date must be after start date' })
      }

      const query = ZnOrder.query()
        .select('userId')
        .count('* as totalOrders')
        .sum('totalPrice as totalSpent')
        .groupBy('userId')
        .orderBy('totalSpent', 'desc')
        .limit(5)

      if (startDateTime) {
        query.where('createdAt', '>=', startDateTime)
      }
      if (endDateTime) {
        query.where('createdAt', '<=', endDateTime)
      }

      const topCustomers = await query

      const userIds = topCustomers.map((customer) => customer.userId)

      const users = await ZnUser.query()
        .select(['id', 'firstName', 'lastName', 'avatar'])
        .whereIn('id', userIds as string[])

      const userMap = new Map(users.map((user) => [user.id, user]))

      const result = topCustomers.map((customer) => {
        const user = userMap.get(customer.userId as string)
        return {
          customerName: user ? `${user.firstName} ${user.lastName}` : 'Unknown',
          avatar: user ? user.avatarUrl : null,
          totalOrders: Number(customer.$extras.totalOrders) || 0,
          totalSpent: `$${(Number(customer.$extras.totalSpent) || 0).toLocaleString('en-US', { maximumFractionDigits: 2 })}`,
        }
      })

      return response.ok(result)
    } catch (error) {
      console.error('Error fetching top customers:', error)
      return response.status(500).send({
        message: 'An error occurred while fetching top customers',
        error: error.message,
      })
    }
  }

  @CacheResponse()
  async getOrderCountByCountry({ request, response }: HttpContext) {
    try {
      const { startDate, endDate } = request.qs()

      const startDateTime = startDate ? new Date(startDate) : undefined
      const endDateTime = endDate ? new Date(endDate) : undefined

      if (startDateTime && endDateTime && endDateTime < startDateTime) {
        return response.badRequest({ message: 'End date must be after start date' })
      }

      const query = db
        .from('zn_orders AS orders')
        .join('zn_addresses as shipping_address', 'orders.shippingId', 'shipping_address.id')
        .select('shipping_address.country')
        .count('* as totalOrders')
        .groupBy('shipping_address.country')
        .orderBy('totalOrders', 'desc')
        .limit(5)

      query.whereNull('orders.deletedAt')

      if (startDateTime) {
        query.where('orders.createdAt', '>=', startDateTime)
      }
      if (endDateTime) {
        query.where('orders.createdAt', '<=', endDateTime)
      }

      const ordersByCountry = await query

      const totalOrders = ordersByCountry.reduce((sum, row) => sum + Number(row.totalOrders), 0)

      const result = ordersByCountry.map((row) => {
        const country = row.country || 'Unknown'
        const totalOrdersForCountry = Number(row.totalOrders) || 0
        const percentage =
          totalOrders > 0 ? ((totalOrdersForCountry / totalOrders) * 100).toFixed(2) : '0.00'

        return {
          country,
          totalOrders: totalOrdersForCountry,
          percentage: `${percentage}%`,
        }
      })

      return response.ok(result)
    } catch (error) {
      console.error('Error fetching order count by country:', error)

      return response.status(500).send({
        message: 'An error occurred while fetching order count by country',
        error: error.message,
      })
    }
  }

  @CacheResponse()
  async getTopSellingProducts({ request, response }: HttpContext) {
    try {
      const { startDate, endDate, page = 1, limit = 10 } = request.qs()

      const pageNumber = parseInt(page)
      const limitNumber = parseInt(limit)
      const offset = (pageNumber - 1) * limitNumber

      const startDateTime = startDate ? new Date(startDate) : undefined
      const endDateTime = endDate ? new Date(endDate) : undefined

      const totalCountQuery = db
        .from('zn_order_details')
        .join('zn_product_variants as variants', 'zn_order_details.variantId', 'variants.id')
        .join('zn_products as products', 'variants.productId', 'products.id')
        .whereNull('zn_order_details.deletedAt')
        .whereNull('variants.deletedAt')
        .whereNull('products.deletedAt')

      if (startDateTime) totalCountQuery.where('zn_order_details.createdAt', '>=', startDateTime)
      if (endDateTime) totalCountQuery.where('zn_order_details.createdAt', '<=', endDateTime)

      const totalResult = await totalCountQuery.countDistinct('products.id as total').first()
      const total = Number(totalResult?.total || 0)

      const query = db
        .from('zn_order_details')
        .join('zn_product_variants as variants', 'zn_order_details.variantId', 'variants.id')
        .join('zn_products as products', 'variants.productId', 'products.id')
        .leftJoin('zn_product_types as types', 'products.productTypeId', 'types.id')
        .select(
          'products.id as productId',
          'products.title as productName',
          'types.name as category'
        )
        .countDistinct('zn_order_details.orderId as totalOrders')
        .select(db.raw('SUM(zn_order_details.price * zn_order_details.quantity) as totalSales'))
        .whereNull('zn_order_details.deletedAt')
        .whereNull('variants.deletedAt')
        .whereNull('products.deletedAt')
        .groupBy('products.id', 'products.title', 'types.name')
        .orderBy('totalSales', 'desc')
        .offset(offset)
        .limit(limitNumber)

      if (startDateTime) query.where('zn_order_details.createdAt', '>=', startDateTime)
      if (endDateTime) query.where('zn_order_details.createdAt', '<=', endDateTime)

      const rows = await query

      const data = rows.map((row) => ({
        id: row.productId,
        product: row.productName,
        category: row.category || 'Unknown',
        totalOrders: Number(row.totalOrders) || 0,
        totalSales: Number(row.totalSales) || 0,
      }))

      const lastPage = Math.ceil(total / limitNumber)

      return response.ok({
        meta: {
          total,
          perPage: limitNumber,
          currentPage: pageNumber,
          lastPage,
          firstPage: 1,
          firstPageUrl: `?page=1`,
          lastPageUrl: `?page=${lastPage}`,
          nextPageUrl: pageNumber < lastPage ? `?page=${pageNumber + 1}` : null,
          previousPageUrl: pageNumber > 1 ? `?page=${pageNumber - 1}` : null,
        },
        data,
      })
    } catch (error) {
      console.error('Error fetching top selling products:', error)
      return response.status(500).send({
        message: 'An error occurred while fetching top selling products',
        error: error.message,
      })
    }
  }

  @CacheResponse()
  async getTopPurchasedProducts({ request, response }: HttpContext) {
    try {
      const { startDate, endDate } = request.qs()
      const limit = 5

      const query = db
        .from('zn_trackings')
        .joinRaw(`INNER JOIN zn_products ON zn_products.shopifyProductId = zn_trackings.resourceId`)
        .leftJoin('zn_product_types', 'zn_products.productTypeId', 'zn_product_types.id')
        .select(
          'zn_products.id as productId',
          'zn_products.title as productName',
          'zn_product_types.name as category',
          db.raw(`(
            SELECT src 
            FROM zn_product_images 
            WHERE productId = zn_products.id 
            AND deletedAt IS NULL 
            ORDER BY position ASC 
            LIMIT 1
          ) as imageUrl`)
        )
        .where('zn_trackings.action', TRACKING_ACTION.ADD_TO_CART)
        .where('zn_trackings.resource', 'ShopifyProduct')
        .whereNull('zn_products.deletedAt')
        .groupBy('zn_products.id', 'zn_products.title', 'zn_product_types.name')
        .count('zn_trackings.id as purchaseCount')
        .orderBy('purchaseCount', 'desc')
        .limit(limit)

      if (startDate) {
        query.where('zn_trackings.createdAt', '>=', new Date(startDate))
      }
      if (endDate) {
        query.where('zn_trackings.createdAt', '<=', new Date(endDate))
      }

      const purchasedProducts = await query

      const result = purchasedProducts.map((row) => ({
        id: row.productId,
        product: row.productName,
        category: row.category || 'Unknown',
        image: row.imageUrl,
        purchaseCount: Number(row.purchaseCount) || 0,
      }))

      return response.ok(result)
    } catch (error) {
      console.error('Error fetching top purchased products:', error)
      return response.internalServerError({
        message: 'An error occurred while fetching top purchased products',
        error: error.message,
      })
    }
  }
}
