import { HttpContext } from '@adonisjs/core/http'
import { CacheResponse } from '../../../../app/decorators/cache_response.decorator.js'
import db from '@adonisjs/lucid/services/db'
import { TRACKING_ACTION } from '../../../../app/constants/tracking.js'
import ZnTracking from '../../../../app/models/zn_tracking.js'
import ZnProduct from '../../../../app/models/zn_product.js'

export default class AdminDashboardAnalyticController {
  @CacheResponse()
  async getTopSellingProducts({ request, response }: HttpContext) {
    try {
      const { startDate, endDate, page = 1, limit = 10 } = request.qs()

      const pageNumber = parseInt(page)
      const limitNumber = parseInt(limit)
      const offset = (pageNumber - 1) * limitNumber

      const startDateTime = startDate ? new Date(startDate) : undefined
      const endDateTime = endDate ? new Date(endDate) : undefined

      const totalCountQuery = db
        .from('zn_order_details')
        .join('zn_product_variants as variants', 'zn_order_details.variantId', 'variants.id')
        .join('zn_products as products', 'variants.productId', 'products.id')
        .whereNull('zn_order_details.deletedAt')
        .whereNull('variants.deletedAt')
        .whereNull('products.deletedAt')

      if (startDateTime) totalCountQuery.where('zn_order_details.createdAt', '>=', startDateTime)
      if (endDateTime) totalCountQuery.where('zn_order_details.createdAt', '<=', endDateTime)

      const totalResult = await totalCountQuery.countDistinct('products.id as total').first()
      const total = Number(totalResult?.total || 0)

      const query = db
        .from('zn_order_details')
        .join('zn_product_variants as variants', 'zn_order_details.variantId', 'variants.id')
        .join('zn_products as products', 'variants.productId', 'products.id')
        .leftJoin('zn_product_types as types', 'products.productTypeId', 'types.id')
        .select(
          'products.id as productId',
          'products.title as productName',
          'types.name as category'
        )
        .countDistinct('zn_order_details.orderId as totalOrders')
        .select(db.raw('SUM(zn_order_details.price * zn_order_details.quantity) as totalSales'))
        .whereNull('zn_order_details.deletedAt')
        .whereNull('variants.deletedAt')
        .whereNull('products.deletedAt')
        .groupBy('products.id', 'products.title', 'types.name')
        .orderBy('totalSales', 'desc')
        .offset(offset)
        .limit(limitNumber)

      if (startDateTime) query.where('zn_order_details.createdAt', '>=', startDateTime)
      if (endDateTime) query.where('zn_order_details.createdAt', '<=', endDateTime)

      const rows = await query

      const data = rows.map((row) => ({
        id: row.productId,
        product: row.productName,
        category: row.category || 'Unknown',
        totalOrders: Number(row.totalOrders) || 0,
        totalSales: Number(row.totalSales) || 0,
      }))

      const lastPage = Math.ceil(total / limitNumber)

      return response.ok({
        meta: {
          total,
          perPage: limitNumber,
          currentPage: pageNumber,
          lastPage,
          firstPage: 1,
          firstPageUrl: `?page=1`,
          lastPageUrl: `?page=${lastPage}`,
          nextPageUrl: pageNumber < lastPage ? `?page=${pageNumber + 1}` : null,
          previousPageUrl: pageNumber > 1 ? `?page=${pageNumber - 1}` : null,
        },
        data,
      })
    } catch (error) {
      console.error('Error fetching top selling products:', error)
      return response.status(500).send({
        message: 'An error occurred while fetching top selling products',
        error: error.message,
      })
    }
  }

  @CacheResponse()
  async getMostViewedProducts({ request, response }: HttpContext) {
    try {
      const { startDate, endDate, page = 1, limit = 10 } = request.qs()
      const pageNumber = parseInt(page)
      const limitNumber = parseInt(limit)
      const offset = (pageNumber - 1) * limitNumber

      const startDateTime = startDate ? new Date(startDate) : undefined
      const endDateTime = endDate ? new Date(endDate) : undefined

      const totalCountQuery = db
        .from('zn_resource_interacts')
        .join('zn_products as products', 'zn_resource_interacts.resourceId', 'products.id')
        .where('zn_resource_interacts.resource', 'ShopifyProduct')

      if (startDateTime)
        totalCountQuery.where('zn_resource_interacts.createdAt', '>=', startDateTime)
      if (endDateTime) totalCountQuery.where('zn_resource_interacts.createdAt', '<=', endDateTime)

      const totalResult = await totalCountQuery.countDistinct('products.id as total').first()
      const total = Number(totalResult?.total || 0)

      const query = db
        .from('zn_resource_interacts')
        .join('zn_products as products', 'zn_resource_interacts.resourceId', 'products.id')
        .leftJoin('zn_product_types as types', 'products.productTypeId', 'types.id')
        .select(
          'products.id as productId',
          'products.title as productName',
          'types.name as category',
          'zn_resource_interacts.viewCount as totalViews'
        )
        .where('zn_resource_interacts.resource', 'ShopifyProduct')
        .orderBy('zn_resource_interacts.viewCount', 'desc')
        .offset(offset)
        .limit(limitNumber)

      if (startDateTime) query.where('zn_resource_interacts.createdAt', '>=', startDateTime)
      if (endDateTime) query.where('zn_resource_interacts.createdAt', '<=', endDateTime)

      const rows = await query

      const data = rows.map((row) => ({
        id: row.productId,
        product: row.productName,
        category: row.category || 'Unknown',
        totalViews: Number(row.totalViews) || 0,
      }))

      const lastPage = Math.ceil(total / limitNumber)

      return response.ok({
        meta: {
          total,
          perPage: limitNumber,
          currentPage: pageNumber,
          lastPage,
          firstPage: 1,
          firstPageUrl: `?page=1`,
          lastPageUrl: `?page=${lastPage}`,
          nextPageUrl: pageNumber < lastPage ? `?page=${pageNumber + 1}` : null,
          previousPageUrl: pageNumber > 1 ? `?page=${pageNumber - 1}` : null,
        },
        data,
      })
    } catch (error) {
      console.error('Error fetching most viewed products:', error)
      return response.status(500).send({
        message: 'An error occurred while fetching most viewed products',
        error: error.message,
      })
    }
  }

  @CacheResponse()
  async getTopFavoritedProducts({ request, response }: HttpContext) {
    try {
      const { startDate, endDate, page = 1, limit = 10 } = request.qs()
      const pageNumber = parseInt(page)
      const limitNumber = parseInt(limit)
      const offset = (pageNumber - 1) * limitNumber

      const startDateTime = startDate ? new Date(startDate) : undefined
      const endDateTime = endDate ? new Date(endDate) : undefined

      const totalCountQuery = db
        .from('zn_resource_interacts')
        .join('zn_products as products', 'zn_resource_interacts.resourceId', 'products.id')
        .where('zn_resource_interacts.resource', 'ShopifyProduct')

      if (startDateTime)
        totalCountQuery.where('zn_resource_interacts.createdAt', '>=', startDateTime)
      if (endDateTime) totalCountQuery.where('zn_resource_interacts.createdAt', '<=', endDateTime)

      const totalResult = await totalCountQuery.countDistinct('products.id as total').first()
      const total = Number(totalResult?.total || 0)

      const query = db
        .from('zn_resource_interacts')
        .join('zn_products as products', 'zn_resource_interacts.resourceId', 'products.id')
        .leftJoin('zn_product_types as types', 'products.productTypeId', 'types.id')
        .select(
          'products.id as productId',
          'products.title as productName',
          'types.name as category',
          'zn_resource_interacts.likeCount as totalFavorites'
        )
        .where('zn_resource_interacts.resource', 'ShopifyProduct')
        .orderBy('zn_resource_interacts.likeCount', 'desc')
        .offset(offset)
        .limit(limitNumber)

      if (startDateTime) query.where('zn_resource_interacts.createdAt', '>=', startDateTime)
      if (endDateTime) query.where('zn_resource_interacts.createdAt', '<=', endDateTime)

      const rows = await query

      const data = rows.map((row) => ({
        id: row.productId,
        product: row.productName,
        category: row.category || 'Unknown',
        totalFavorites: Number(row.totalFavorites) || 0,
      }))

      const lastPage = Math.ceil(total / limitNumber)

      return response.ok({
        meta: {
          total,
          perPage: limitNumber,
          currentPage: pageNumber,
          lastPage,
          firstPage: 1,
          firstPageUrl: `?page=1`,
          lastPageUrl: `?page=${lastPage}`,
          nextPageUrl: pageNumber < lastPage ? `?page=${pageNumber + 1}` : null,
          previousPageUrl: pageNumber > 1 ? `?page=${pageNumber - 1}` : null,
        },
        data,
      })
    } catch (error) {
      console.error('Error fetching top favorited products:', error)
      return response.status(500).send({
        message: 'An error occurred while fetching top favorited products',
        error: error.message,
      })
    }
  }

  @CacheResponse()
  async getHighestRatedProducts({ request, response }: HttpContext) {
    try {
      const { startDate, endDate, page = 1, limit = 5 } = request.qs()
      const pageNumber = parseInt(page)
      const limitNumber = parseInt(limit)
      const offset = (pageNumber - 1) * limitNumber
      const startDateTime = startDate ? new Date(startDate) : undefined
      const endDateTime = endDate ? new Date(endDate) : undefined
      if (startDateTime && endDateTime && endDateTime < startDateTime) {
        return response.badRequest({ message: 'End date must be after start date' })
      }
      const trackingQuery = ZnTracking.query()
        .where('action', TRACKING_ACTION.RATING_PRODUCT)
        .whereIn('resource', ['ZnProduct', 'zn_products'])
        .whereNotNull('ratingReviewStar')
      if (startDateTime) trackingQuery.where('createdAt', '>=', startDateTime)
      if (endDateTime) trackingQuery.where('createdAt', '<=', endDateTime)
      const allRatings = await trackingQuery
      const ratingMap = new Map()
      for (const t of allRatings) {
        const key = t.resourceId
        if (!ratingMap.has(key)) ratingMap.set(key, { total: 0, count: 0, stars: [] })
        const obj = ratingMap.get(key)
        obj.total += t.ratingReviewStar
        obj.count += 1
        obj.stars.push(t.ratingReviewStar)
      }
      const resourceIds = Array.from(ratingMap.keys())
      const shopifyIds = resourceIds
        .filter((id) => /^\d+$/.test(id))
        .map((id) => `gid://shopify/Product/${id}`)
      const uuidIds = resourceIds.filter((id) => !/^\d+$/.test(id))
      let products: ZnProduct[] = []
      if (uuidIds.length && shopifyIds.length) {
        products = await ZnProduct.query()
          .whereIn('id', uuidIds)
          .orWhereIn('shopifyProductId', shopifyIds)
          .preload('category')
          .preload('productType')
      } else if (uuidIds.length) {
        products = await ZnProduct.query()
          .whereIn('id', uuidIds)
          .preload('category')
          .preload('productType')
      } else if (shopifyIds.length) {
        products = await ZnProduct.query()
          .whereIn('shopifyProductId', shopifyIds)
          .preload('category')
          .preload('productType')
      }
      const data = products.map((product) => {
        let rating = ratingMap.get(product.id)
        if (!rating && product.shopifyProductId) {
          const shopifyId = product.shopifyProductId.replace('gid://shopify/Product/', '')
          rating = ratingMap.get(shopifyId)
        }
        return {
          id: product.id,
          product: product.title,
          category: product.category
            ? product.category.name
            : product.productType
              ? product.productType.name
              : 'Unknown',
          rating: rating ? (rating.total / rating.count).toFixed(1) : null,
          ratingCount: rating ? rating.count : 0,
        }
      })
      const sorted = data.sort((a, b) => (Number(b.rating) || 0) - (Number(a.rating) || 0))
      const total = sorted.length
      const lastPage = Math.ceil(total / limitNumber)
      const paged = sorted.slice(offset, offset + limitNumber)
      return response.ok({
        meta: {
          total,
          perPage: limitNumber,
          currentPage: pageNumber,
          lastPage,
          firstPage: 1,
          firstPageUrl: `?page=1`,
          lastPageUrl: `?page=${lastPage}`,
          nextPageUrl: pageNumber < lastPage ? `?page=${pageNumber + 1}` : null,
          previousPageUrl: pageNumber > 1 ? `?page=${pageNumber - 1}` : null,
        },
        data: paged,
      })
    } catch (error) {
      console.error('Error fetching highest rated products:', error)
      return response.status(500).send({
        message: 'An error occurred while fetching highest rated products',
        error: error.message,
      })
    }
  }

  @CacheResponse()
  async getMostViewedPosts({ request, response }: HttpContext) {
    try {
      const { startDate, endDate, page = 1, limit = 10 } = request.qs()
      const pageNumber = parseInt(page)
      const limitNumber = parseInt(limit)
      const offset = (pageNumber - 1) * limitNumber

      const startDateTime = startDate ? new Date(startDate) : undefined
      const endDateTime = endDate ? new Date(endDate) : undefined

      const totalCountQuery = db
        .from('zn_resource_interacts')
        .join('zn_posts as posts', 'zn_resource_interacts.resourceId', 'posts.id')
        .where('zn_resource_interacts.resource', 'ZnPost')

      if (startDateTime)
        totalCountQuery.where('zn_resource_interacts.createdAt', '>=', startDateTime)
      if (endDateTime) totalCountQuery.where('zn_resource_interacts.createdAt', '<=', endDateTime)

      const totalResult = await totalCountQuery.countDistinct('posts.id as total').first()
      const total = Number(totalResult?.total || 0)

      const query = db
        .from('zn_resource_interacts')
        .join('zn_posts as posts', 'zn_resource_interacts.resourceId', 'posts.id')
        .join('zn_posts_post_categories as post_categories', 'posts.id', 'post_categories.postId')
        .join('zn_post_categories as categories', 'post_categories.postCategoryId', 'categories.id')
        .select(
          'posts.id as postId',
          'posts.title as postTitle',
          'posts.source as postSource',
          'categories.name as categoryName',
          'zn_resource_interacts.viewCount as totalViews',
          'zn_resource_interacts.clickCallOnCount as totalCalls'
        )
        .where('zn_resource_interacts.resource', 'ZnPost')
        .orderBy('zn_resource_interacts.viewCount', 'desc')
        .offset(offset)
        .limit(limitNumber)

      if (startDateTime) query.where('zn_resource_interacts.createdAt', '>=', startDateTime)
      if (endDateTime) query.where('zn_resource_interacts.createdAt', '<=', endDateTime)

      const rows = await query

      const data = rows.map((row) => ({
        id: row.postId,
        title: row.postTitle,
        source: row.postSource,
        category: row.categoryName || 'Unknown',
        totalViews: Number(row.totalViews) || 0,
        totalCalls: Number(row.totalCalls) || 0,
      }))

      const lastPage = Math.ceil(total / limitNumber)

      return response.ok({
        meta: {
          total,
          perPage: limitNumber,
          currentPage: pageNumber,
          lastPage,
          firstPage: 1,
          firstPageUrl: `?page=1`,
          lastPageUrl: `?page=${lastPage}`,
          nextPageUrl: pageNumber < lastPage ? `?page=${pageNumber + 1}` : null,
          previousPageUrl: pageNumber > 1 ? `?page=${pageNumber - 1}` : null,
        },
        data,
      })
    } catch (error) {
      console.error('Error fetching most viewed posts:', error)
      return response.status(500).send({
        message: 'An error occurred while fetching most viewed posts',
        error: error.message,
      })
    }
  }

  @CacheResponse()
  async getHighestRatedStores({ request, response }: HttpContext) {
    try {
      const { startDate, endDate, page = 1, limit = 10 } = request.qs()
      const pageNumber = parseInt(page)
      const limitNumber = parseInt(limit)
      const offset = (pageNumber - 1) * limitNumber

      const startDateTime = startDate ? new Date(startDate) : undefined
      const endDateTime = endDate ? new Date(endDate) : undefined

      const totalCountQuery = db
        .from('zn_resource_interacts')
        .join('zn_stores as stores', 'zn_resource_interacts.resourceId', 'stores.id')
        .where('zn_resource_interacts.resource', 'ZnStore')

      if (startDateTime)
        totalCountQuery.where('zn_resource_interacts.createdAt', '>=', startDateTime)
      if (endDateTime) totalCountQuery.where('zn_resource_interacts.createdAt', '<=', endDateTime)

      const totalResult = await totalCountQuery.countDistinct('stores.id as total').first()
      const total = Number(totalResult?.total || 0)

      const query = db
        .from('zn_resource_interacts')
        .join('zn_stores as stores', 'zn_resource_interacts.resourceId', 'stores.id')
        .join('zn_stores_nail_systems as nail_systems', 'stores.id', 'nail_systems.storeId')
        .join('zn_nail_systems as systems', 'nail_systems.nailSystemId', 'systems.id')
        .select(
          'stores.id as storeId',
          'stores.name as storeName',
          db.raw(
            'GROUP_CONCAT(DISTINCT systems.name ORDER BY systems.name SEPARATOR ", ") as nailSystemNames'
          ),
          'zn_resource_interacts.likeCount as totalLikes',
          'zn_resource_interacts.viewCount as totalViews'
        )
        .where('zn_resource_interacts.resource', 'ZnStore')
        .groupBy(
          'stores.id',
          'stores.name',
          'zn_resource_interacts.likeCount',
          'zn_resource_interacts.viewCount'
        )
        .orderBy('zn_resource_interacts.likeCount', 'desc')
        .offset(offset)
        .limit(limitNumber)

      if (startDateTime) query.where('zn_resource_interacts.createdAt', '>=', startDateTime)
      if (endDateTime) query.where('zn_resource_interacts.createdAt', '<=', endDateTime)

      const rows = await query

      const data = rows.map((row) => ({
        id: row.storeId,
        name: row.storeName,
        nailSystem: row.nailSystemNames,
        rating: Number(((Number(row.totalLikes) / (Number(row.totalViews) || 1)) * 5).toFixed(1)),
      }))

      const lastPage = Math.ceil(total / limitNumber)

      return response.ok({
        meta: {
          total,
          perPage: limitNumber,
          currentPage: pageNumber,
          lastPage,
          firstPage: 1,
          firstPageUrl: `?page=1`,
          lastPageUrl: `?page=${lastPage}`,
          nextPageUrl: pageNumber < lastPage ? `?page=${pageNumber + 1}` : null,
          previousPageUrl: pageNumber > 1 ? `?page=${pageNumber - 1}` : null,
        },
        data,
      })
    } catch (error) {
      console.error('Error fetching highest rated stores:', error)
      return response.status(500).send({
        message: 'An error occurred while fetching highest rated stores',
        error: error.message,
      })
    }
  }

  @CacheResponse()
  async getTopCommentedPosts({ request, response }: HttpContext) {
    try {
      const { startDate, endDate, page = 1, limit = 10 } = request.qs()
      const pageNumber = parseInt(page)
      const limitNumber = parseInt(limit)
      const offset = (pageNumber - 1) * limitNumber

      const startDateTime = startDate ? new Date(startDate) : undefined
      const endDateTime = endDate ? new Date(endDate) : undefined

      const totalCountQuery = db
        .from('zn_post_comments as comments')
        .join('zn_posts as posts', 'comments.postId', 'posts.id')
        .whereNotNull('comments.id')

      if (startDateTime) totalCountQuery.where('comments.createdAt', '>=', startDateTime)
      if (endDateTime) totalCountQuery.where('comments.createdAt', '<=', endDateTime)

      const totalResult = await totalCountQuery.countDistinct('posts.id as total').first()
      const total = Number(totalResult?.total || 0)

      const query = db
        .from('zn_post_comments as comments')
        .join('zn_posts as posts', 'comments.postId', 'posts.id')
        .join('zn_posts_post_categories as post_categories', 'posts.id', 'post_categories.postId')
        .join('zn_post_categories as categories', 'post_categories.postCategoryId', 'categories.id')
        .select(
          'posts.id as postId',
          'posts.title as postTitle',
          'posts.source as postSource',
          'categories.name as categoryName',
          db.raw('COUNT(comments.id) as totalComments')
        )
        .groupBy('posts.id', 'posts.title', 'posts.source', 'categories.name')
        .orderBy('totalComments', 'desc')
        .offset(offset)
        .limit(limitNumber)

      if (startDateTime) query.where('comments.createdAt', '>=', startDateTime)
      if (endDateTime) query.where('comments.createdAt', '<=', endDateTime)

      const rows = await query

      const data = rows.map((row) => ({
        id: row.postId,
        post: row.postTitle,
        source: row.postSource,
        category: row.categoryName || 'Unknown',
        totalComments: Number(row.totalComments) || 0,
      }))

      const lastPage = Math.ceil(total / limitNumber)

      return response.ok({
        meta: {
          total,
          perPage: limitNumber,
          currentPage: pageNumber,
          lastPage,
          firstPage: 1,
          firstPageUrl: `?page=1`,
          lastPageUrl: `?page=${lastPage}`,
          nextPageUrl: pageNumber < lastPage ? `?page=${pageNumber + 1}` : null,
          previousPageUrl: pageNumber > 1 ? `?page=${pageNumber - 1}` : null,
        },
        data,
      })
    } catch (error) {
      console.error('Error fetching top commented posts:', error)
      return response.status(500).send({
        message: 'An error occurred while fetching top commented posts',
        error: error.message,
      })
    }
  }

  @CacheResponse()
  async getTopLikedPosts({ request, response }: HttpContext) {
    try {
      const { startDate, endDate, page = 1, limit = 10 } = request.qs()
      const pageNumber = parseInt(page)
      const limitNumber = parseInt(limit)
      const offset = (pageNumber - 1) * limitNumber

      const startDateTime = startDate ? new Date(startDate) : undefined
      const endDateTime = endDate ? new Date(endDate) : undefined

      const totalCountQuery = db
        .from('zn_resource_interacts')
        .join('zn_posts as posts', 'zn_resource_interacts.resourceId', 'posts.id')
        .where('zn_resource_interacts.resource', 'ZnPost')

      if (startDateTime)
        totalCountQuery.where('zn_resource_interacts.createdAt', '>=', startDateTime)
      if (endDateTime) totalCountQuery.where('zn_resource_interacts.createdAt', '<=', endDateTime)

      const totalResult = await totalCountQuery.countDistinct('posts.id as total').first()
      const total = Number(totalResult?.total || 0)

      const query = db
        .from('zn_resource_interacts')
        .join('zn_posts as posts', 'zn_resource_interacts.resourceId', 'posts.id')
        .join('zn_posts_post_categories as post_categories', 'posts.id', 'post_categories.postId')
        .join('zn_post_categories as categories', 'post_categories.postCategoryId', 'categories.id')
        .select(
          'posts.id as postId',
          'posts.title as postTitle',
          'posts.source as postSource',
          'categories.name as categoryName',
          'zn_resource_interacts.likeCount as totalLikes'
        )
        .where('zn_resource_interacts.resource', 'ZnPost')
        .orderBy('zn_resource_interacts.likeCount', 'desc')
        .offset(offset)
        .limit(limitNumber)

      if (startDateTime) query.where('zn_resource_interacts.createdAt', '>=', startDateTime)
      if (endDateTime) query.where('zn_resource_interacts.createdAt', '<=', endDateTime)

      const rows = await query

      const data = rows.map((row) => ({
        id: row.postId,
        post: row.postTitle,
        source: row.postSource,
        category: row.categoryName || 'Unknown',
        totalLikes: Number(row.totalLikes) || 0,
      }))

      const lastPage = Math.ceil(total / limitNumber)

      return response.ok({
        meta: {
          total,
          perPage: limitNumber,
          currentPage: pageNumber,
          lastPage,
          firstPage: 1,
          firstPageUrl: `?page=1`,
          lastPageUrl: `?page=${lastPage}`,
          nextPageUrl: pageNumber < lastPage ? `?page=${pageNumber + 1}` : null,
          previousPageUrl: pageNumber > 1 ? `?page=${pageNumber - 1}` : null,
        },
        data,
      })
    } catch (error) {
      console.error('Error fetching top liked posts:', error)
      return response.status(500).send({
        message: 'An error occurred while fetching top liked posts',
        error: error.message,
      })
    }
  }
}
