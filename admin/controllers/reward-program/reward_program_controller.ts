import ZnRewardProgram, { ERewardProgramStatus } from '#models/zn_reward_program';
import { HttpContext } from '@adonisjs/core/http';
import { createRewardProgramValidator } from '../../validators/reward-program/reward_program_validator.js';

export default class AdminRewardProgramController {
  public async index({ request, response }: HttpContext) {
    try {
      const {
        page = 1,
        limit = 5,
        rewardType,
      } = request.all()

      const query = ZnRewardProgram.query()

      if (rewardType) {
        query.where({ rewardType })
      }

      const result = await query.paginate(page, limit)

      return response.ok(result)

    } catch (error) {
      console.log(error);
      return response.internalServerError("Something went wrong!")
    }
  }

  public async show({ params, response }: HttpContext) {
    try {
      const programId = params.id

      const program = await ZnRewardProgram.find(programId)

      if (!program) {
        return response.notFound("Reward Program Not Found")
      }

      return response.ok(program)

    } catch (error) {
      console.log(error);
      return response.internalServerError("Something went wrong!")
    }
  }

  public async store({ request, response }: HttpContext) {
    const data = request.all()
    const payload = await createRewardProgramValidator.validate(data)

    try {
      if (payload.status == ERewardProgramStatus.ACTIVE) {
        const existingProgram = await ZnRewardProgram.query()
          .where({
            status: ERewardProgramStatus.ACTIVE,
            programType: payload.programType
          })
          .first()

        if (existingProgram) {
          return response.badRequest("A similar program is active!")
        }
      }

      const program = await ZnRewardProgram.create(payload)

      return response.ok(program)

    } catch (error) {
      console.log(error);
      return response.internalServerError("Something went wrong!")
    }
  }

  public async update({ params, request, response }: HttpContext) {
    const data = request.all()
    const payload = await createRewardProgramValidator.validate(data)

    try {
      const programId = params.id
      const program = await ZnRewardProgram.find(programId)

      if (!program) {
        return response.notFound("Reward Program Not Found")
      }

      if (payload.status == ERewardProgramStatus.ACTIVE) {
        const existingProgram = await ZnRewardProgram.query()
          .where({
            status: ERewardProgramStatus.ACTIVE,
            programType: payload.programType
          })
          .whereNot({ id: programId })
          .first()

        if (existingProgram) {
          return response.badRequest("A similar program is active!")
        }
      }

      await program.merge(payload).save()

      return response.ok(program)

    } catch (error) {
      console.log(error);
      return response.internalServerError("Something went wrong!")
    }
  }

  public async destroy({ params, response }: HttpContext) {
    try {
      const programId = params.id
      const program = await ZnRewardProgram.find(programId)

      if (!program) {
        return response.notFound("Reward Program Not Found")
      }

      await program.softDelete()

      return response.ok(program)

    } catch (error) {
      console.log(error);
      return response.internalServerError("Something went wrong!")
    }
  }
}