import ZnDrawerMenus from '#models/zn_drawer_menus'
import { DrawerMenusService } from '#services/firebase/drawer_menu_service'
import { appCreateDrawerMenusValidator } from '#validators/app/drawer-menus/drawer-menus'
import { HttpContext } from '@adonisjs/core/http'
import { ACTION, RESOURCE } from '../../../app/constants/authorization.js'

export default class AdminDrawerMenusController {
  private drawerMenusService = new DrawerMenusService()

  /**
   * @index
   * @tag Admin Drawer-Menus
   * @summary Read all tabs
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @responseBody 200 - <ZnDrawerMenus[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null).paginated() - Read all drawer menus descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display a list of resource
   */
  async index({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.DRAWER_MENU)

    try {
      const { page = 1, limit = 10, search } = request.qs()

      const query = ZnDrawerMenus.query().whereNull('deletedAt')

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder.where('title', 'like', `%${search}%`).orWhere('url', 'like', `%${search}%`)
        })
      }

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @create
   * @tag Admin Drawer-Menus
   * @summary Return data to create
   */
  /**
   * Display form to create a new record
   */
  async create({}: HttpContext) {}

  /**
   * @store
   * @tag Admin Drawer-Menus
   * @summary Create a drawer menu
   * @requestBody <ZnDrawerMenus>
   * @responseBody 201 - <ZnDrawerMenus>.append("id":"","createdAt":"","updatedAt":"") - Create a drawer menu descriptively
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The title field must be defined","rule":"required","field":"title"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Handle form submission for the create action
   */
  public async store({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.DRAWER_MENU)

    const data = request.all()

    const payload = await appCreateDrawerMenusValidator.validate(data)

    try {
      const created = await ZnDrawerMenus.create({
        title: payload.title,
        url: payload.url,
      })

      this.drawerMenusService.write(created)

      return response.created(created)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @show
   * @tag Admin Drawer-Menus
   * @summary Read a drawer menu
   * @paramPath id - ID of Tab - @type(string) @required
   * @responseBody 200 - <ZnDrawerMenus>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read a drawer menu descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Drawer menu not found"} - Not Found
   */
  /**
   * Show individual record
   */
  public async show({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.DRAWER_MENU)

    try {
      const drawerMenu = await this.drawerMenuIfExists(params, response)
      if (!drawerMenu) {
        return
      }

      return response.ok(drawerMenu)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @edit
   * @tag Admin Drawer-Menus
   * @summary Return data to update
   */
  /**
   * Edit individual record
   */
  async edit({}: HttpContext) {}

  /**
   * @update
   * @tag Admin Drawer-Menus
   * @summary Update a drawer menu
   * @description Update a drawer menu descriptively
   * @paramPath id - ID of Drawer-Menus - @type(string) @required
   * @requestBody <ZnDrawerMenus>
   * @responseBody 200 - <ZnDrawerMenus>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The title field must be defined","rule":"required","field":"title"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Drawer menu not found"} - Not Found
   */
  /**
   * Handle form submission for the edit action
   */
  public async update({ bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.DRAWER_MENU)

    const drawerMenu = await this.drawerMenuIfExists(params, response)
    if (!drawerMenu) {
      return
    }

    const data = request.all()

    const payload = await appCreateDrawerMenusValidator.validate(data)

    try {
      drawerMenu.title = payload.title
      drawerMenu.url = payload.url

      const updated = await drawerMenu.save()

      this.drawerMenusService.write(updated)

      return response.ok(updated)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @destroy
   * @tag Admin Drawer-Menus
   * @summary Soft-delete a drawer menu
   * @description Soft-delete a drawer menu descriptively
   * @paramPath id - ID of Drawer-Menus - @type(string) @required
   * @responseBody 200 - {"message":"Drawer menu soft-deleted successfully"}
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Drawer menu not found"} - Not Found
   */
  /**
   * Delete record
   */
  public async destroy({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.DRAWER_MENU)

    try {
      const drawerMenu = await this.drawerMenuIfExists(params, response)
      if (!drawerMenu) {
        return
      }

      await drawerMenu.softDelete()

      this.drawerMenusService.delete(drawerMenu)

      return response.ok({ message: 'Drawer menu soft-deleted successfully' })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  private async drawerMenuIfExists(
    params: HttpContext['params'],
    response: HttpContext['response']
  ) {
    const drawerMenuId = params.id

    const drawerMenu = await ZnDrawerMenus.query().where('id', drawerMenuId).first()

    if (!drawerMenu) {
      response.notFound({ message: 'Drawer menu not found' })
    }

    return drawerMenu
  }
}
