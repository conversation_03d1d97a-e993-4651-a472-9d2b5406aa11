import ZnAdmin from '#models/zn_admin'
import { HttpContext } from '@adonisjs/core/http'
import hash from '@adonisjs/core/services/hash'
import { updateMePasswordValidator, updateMeValidator } from '../../validators/me/me_validator.js'

export default class AdminMeController {
  /**
   * @show
   * @tag Admin Me
   * @summary Read an admin
   * @responseBody 200 - <ZnAdmin>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null).with(avatar) - Read an admin descriptively
   * @responseBody 404 - {"message":"Admin not found"} - Not Found
   */
  /**
   * Show individual record
   */
  async show({ auth, response }: HttpContext) {
    // @ts-ignore
    const adminId = auth.getUserOrFail().id

    const admin = await ZnAdmin.query().preload('avatar').where('id', adminId).first()

    if (!admin) {
      return response.notFound({ message: 'Admin not found' })
    }

    // const permissions = await AuthorizationService.getPermissionsFromAdmin(admin)

    return response.ok(admin)
  }

  /**
   * @update
   * @tag Admin Me
   * @summary Update admin info
   * @description Update admin info descriptively
   * @requestBody <ZnAdmin>.exclude(username, isActive)
   * @responseBody 200 - <ZnAdmin>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The name field must be defined","rule":"required","field":"name"}]} - Bad Request
   * @responseBody 404 - {"message":"Admin not found"} - Not Found
   */
  /**
   * Handle form submission for the edit action
   */
  async update({ auth, request, response }: HttpContext) {
    // @ts-ignore
    const adminId = auth.getUserOrFail().id

    const admin = await ZnAdmin.query().where('id', adminId).first()

    if (!admin) {
      return response.notFound({ message: 'Admin not found' })
    }

    const data = request.all()

    const payload = await updateMeValidator.validate(data)

    try {
      admin.name = payload.name || admin.name
      admin.phone = payload.phone || admin.phone

      admin.avatarId = payload.avatarId || admin.avatarId

      const updated = await admin.save()

      return response.ok(updated)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @updatePassword
   * @tag Admin Me
   * @summary Update admin password
   * @description Update admin password descriptively
   * @requestBody {"currentPassword":"password","newPassword":"adminPass"}
   * @responseBody 200 - {"message":"Password updated successfully"} - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The password field must be defined","rule":"required","field":"password"}]} - Bad Request
   * @responseBody 404 - {"message":"Admin not found"} - Not Found
}
   */
  public async updatePassword({ auth, request, response }: HttpContext) {
    const data = request.all()

    const payload = await updateMePasswordValidator.validate(data)

    // @ts-ignore
    const admin = await ZnAdmin.verifyCredentials(auth.getUserOrFail()?.username,
      payload.currentPassword
    )

    if (!admin) {
      return response.notFound({ message: 'Wrong Password' })
    }

    try {
      const hashedpassword = await hash.make(payload.newPassword)

      admin.password = hashedpassword

      await admin.save()

      return response.ok({ message: 'Password updated successfully' })
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }
}
