import { ACTION, RESOURCE } from '#constants/authorization'
import { NOTIFICATION_TYPE } from '#constants/notification'
import { STREAM_EVENT } from '#constants/stream_events'
import { TRACKING_ACTION } from '#constants/tracking'
import Notification from '#models/notification'
import ZnAdmin from '#models/zn_admin'
import ZnChatRoom from '#models/zn_chat_room'
import ZnPost, { EPostSource, EPostType } from '#models/zn_post'
import ZnProductVariant from '#models/zn_product_variant'
import ZnStream, { EStreamStatus } from '#models/zn_stream'
import ZnStreamDevice from '#models/zn_stream_device'
import ZnTracking from '#models/zn_tracking'
import ZnVideoTimeline from '#models/zn_video_timeline'
import { IvsService } from '#services/aws/ivs_service'
import { IvschatService } from '#services/aws/ivschat_service'
import { StreamService } from '#services/stream_service'
import { TrackingService } from '#services/tracking_service'
import { HttpContext } from '@adonisjs/core/http'
import { GetStreamCommandOutput } from '@aws-sdk/client-ivs'
import * as firebase from 'firebase-admin'
import { DateTime } from 'luxon'
// import moment from 'moment'
import ZnBundleProduct from '#models/zn_bundle_product'
import { FirebaseService } from '../../../services/firebase/index.js'
import { createStreamPostMetadataValidator, createStreamPostValidator } from '../../validators/post/stream_post_validator.js'
import ZnStreamUserInteract from '#models/zn_stream_user_interact'
import ZnChatMessage from '#models/zn_chat_message'
import ZnUserLikeResource from '#models/zn_user_like_resource'
import { RESOURCE_TYPE } from '#constants/like_comment_resource'
import { StoreService } from "#services/store_service";

export default class AdminStreamPostController {
  private ivsChatService: IvschatService
  private ivsService: IvsService
  private databaseRealtime: firebase.database.Database
  private streamService: StreamService
  private trackingService: TrackingService
  private storeService

  constructor() {
    this.ivsChatService = new IvschatService()
    this.ivsService = new IvsService()
    const firebaseInstance = new FirebaseService()
    this.databaseRealtime = firebaseInstance.getDatabase()
    this.streamService = new StreamService()
    this.trackingService = new TrackingService()
    this.storeService = new StoreService()
  }

  /**
   * @index
   * @tag Admin Stream Post
   * @summary Read all streams
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @paramQuery search - Search Term - @type(string)
   * @responseBody 200 - <ZnStream[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null).paginated() - Read all streams descriptively
   * @responseBody 400 - Error - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display a list of resource
   */
  async index({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.STREAM_POST)

    try {
      const {
        page = 1,
        limit = 10,
        search,
      } = request.qs()

      const query = ZnStream.query()
        .preload('post')

      if (search) {
        query.whereRaw('LOWER(title) LIKE LOWER(?)', [`%${search}%`])
      }

      query.orderBy('createdAt', 'desc')

      const result = await query.paginate(page, limit)

      return response.ok(result)

    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @store
   * @tag Admin Stream Post
   * @summary Create action
   * @requestBody {"title":"","description":"","device":{"deviceId":"","groupId":"","kind":"","label":""}}
   * @responseBody 201 - <ZnStream>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Create action descriptively
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The title field must be defined","rule":"required","field":"title"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 500 - {"message":"Something went wrong","error":"Error"} - Internal Server Error
   */
  /**
   * Handle form submission for the create action
   */
  async store({ auth, bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.STREAM_POST)

    const data = request.all()

    const payload = await createStreamPostValidator.validate(data)

    let post, room, stream
    try {
      const admin = auth.getUserOrFail() as ZnAdmin
      const zurnoStore = await this.storeService.getZurnoStore()

      post = await ZnPost.create({
        title: payload.title,
        description: payload.description,
        isDraft: true,
        source: EPostSource.ZURNO,
        type: EPostType.LIVE,
        createdByAdminId: admin.id,
        storeId: zurnoStore?.id || null
      })

      if (Array.isArray(payload.categoryIds)) {
        await post.related('categories').sync(payload.categoryIds)
      }

      let chatRoom
      try {
        chatRoom = await this.ivsChatService.createRoom({
          name: post.title.split(" ").join("_"),
        })
      } catch (error) {
        chatRoom = await this.ivsChatService.createRoom({
          name: 'placeholder',
        })
      }

      room = await ZnChatRoom.create({
        arn: chatRoom.arn,
        name: post.title,
      })

      const channel = await this.ivsService.getChannel(process.env.AWS_IVS_DEFAULT_CHANNEL as string)

      stream = await ZnStream.create({
        postId: post.id,
        title: post.title,
        channelName: channel.channel?.name,
        channelArn: channel.channel?.arn,
        ingestEndpoint: channel.channel?.ingestEndpoint,
        url: channel.channel?.playbackUrl,
        // streamKeyArn: streamKey.streamKey?.arn,
        // streamKeyValue: streamKey.streamKey?.value,
        roomArn: chatRoom.arn,
        roomId: room.id,
        adminId: admin.id,
        isLive: false,
        status: EStreamStatus.READY,
        streamMethod: payload.streamMethod,
        scheduledAt: payload.scheduledAt
          ? DateTime.fromJSDate(payload.scheduledAt)
          : null,
      })

      if (payload.devices) {
        for (const device of payload.devices) {
          await ZnStreamDevice.create({
            streamId: stream.id,
            label: device.label,
            kind: device.kind,
            deviceId: device.deviceId,
            groupId: device.groupId,
          })
        }
      }

      return response.created(stream)

    } catch (error) {
      await post?.delete()
      await room?.delete()
      await stream?.delete()
      console.log(error);

      return response.internalServerError({
        message: 'Something went wrong',
        error: error,
      })
    }
  }

  /**
   * @show
   * @tag Admin Stream Post
   * @summary Read a Stream
   * @paramPath id - ID of Stream - @type(string) @required
   * @responseBody 200 - <ZnStream>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null,"hasNotified":false) - Read a stream descriptively
   * @responseBody 400 - Error - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Stream not found"} - Not Found
   */
  /**
   * Show individual record
   */
  async show({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.STREAM_POST)

    try {
      const streamId = params.id

      const stream = await ZnStream.query()
        .preload('post', (postQuery) => {
          postQuery.preload('categories')
        })
        .preload('devices')
        .where('id', streamId)
        .first()

      if (!stream) { return response.notFound({ message: "Stream not found" }) }

      const notification = await Notification.findBy({
        type: NOTIFICATION_TYPE.STREAM_START,
        resourceId: stream.postId,
      })

      return response.ok({
        ...stream.serialize(),
        hasNotified: !!notification
      })

    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @update
   * @tag Admin Stream Post
   * @summary Update a Stream
   * @description Update a stream descriptively
   * @paramPath id - ID of stream - @type(string) @required
   * @requestBody <ZnStream>.only()
   * @responseBody 200 - <ZnStream>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The title field must be defined","rule":"required","field":"title"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Strean not found"} - Not Found
   */
  /**
   * Handle form submission for the edit action
   */
  async update({ bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.STREAM_POST)

    const streamId = params.id

    const stream = await ZnStream.query()
      .preload('devices')
      .where('id', streamId)
      .first()

    if (!stream) { return response.notFound({ message: 'Stream not found' }) }

    const data = request.all()

    const payload = await createStreamPostValidator.validate(data)

    try {
      stream.title = payload.title || stream.title

      if (payload.devices) {
        if (payload.devices.find((device) => device.kind == 'audioinput')) {
          for (const device of stream.devices) {
            if (device.kind == 'audioinput') {
              await device.softDelete()
            }
          }

          await stream.related('devices').createMany(payload.devices?.map((device) => ({
            streamId: stream.id,
            ...device
          })))
        }
      }

      stream.scheduledAt = payload.scheduledAt
        ? DateTime.fromJSDate(payload.scheduledAt)
        : null

      const updated = await stream.save()

      const post = await ZnPost.find(stream.postId)

      if (post) {
        post.title = payload.title || post.title
        post.description = payload.description || post.description
        post.isDraft = payload.isDraft !== undefined ? payload.isDraft : post.isDraft

        if (Array.isArray(payload.categoryIds)) {
          await post.related('categories').sync(payload.categoryIds)
        }

        await post.save()
      }

      return response.ok(updated)

    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
        error: error,
      })
    }
  }

  /**
   * @destroy
   * @tag Admin Stream Post
   * @summary Soft-delete a stream
   * @description Soft-delete a stream descriptively
   * @paramPath id - ID of Stream - @type(string) @required
   * @responseBody 200 - {"message":"Stream soft-deleted successfully"} - OK
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Stream not found"} - Not Found
   */
  /**
   * Delete record
   */
  async destroy({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.STREAM_POST)

    const streamId = params.id

    const stream = await ZnStream.query()
      .where({ id: streamId, })
      .first()

    if (!stream) { return response.notFound({ message: 'Stream not found' }) }

    await stream.softDelete()

    return response.ok({ message: 'Stream soft-deleted successfully' })
  }

  /**
   * @generateStreamKey
   * @tag Admin Stream Post
   * @summary Generate Stream Key
   * @paramPath id - ID of Stream - @required
   * @responseBody 200 - <ZnStream> - Description
   */
  async generateStreamKey({ params, response }: HttpContext) {
    const streamId = params.id

    const stream = await ZnStream.find(streamId)

    if (!stream) {
      return response.notFound("Stream not found")
    }

    try {
      await this.ivsService.getStream(stream.channelArn)
      return response.badRequest("A Stream is currently live")
    } catch (error) { }

    const channel = await this.ivsService.getChannel(process.env.AWS_IVS_DEFAULT_CHANNEL as string)

    const oldStreamKey = await this.ivsService.listStreamKeys(channel.channel?.arn || "")
    if (oldStreamKey.streamKeys?.[0]?.arn) {
      await this.ivsService.deleteStreamKey(oldStreamKey.streamKeys[0].arn)
    }
    const streamKey = await this.ivsService.createStreamKey(channel.channel?.arn as string)

    await stream.merge({
      streamKeyArn: streamKey.streamKey?.arn,
      streamKeyValue: streamKey.streamKey?.value,
    }).save()

    return response.ok(stream)
  }

  /**
   * @startBroadcast
   * @tag Admin Stream Post
   * @summary Summary
   * @paramPath id - ID of Stream - @required
   * @requestBody {"notifications":true}
   * @responseBody 200 - StartBroadcast Job Sent. - Description
   */
  async startBroadcast({ params, request, response }: HttpContext) {
    const streamId = params.id

    const stream = await ZnStream.query()
      .where('id', streamId)
      .first()

    if (!stream) { return response.notFound("Stream Not Found") }

    if (stream.status == EStreamStatus.FINISH) {
      return response.badRequest('Stream Already Finished')
    }

    const post = await ZnPost.query()
      .where('id', stream.postId)
      .first()

    if (!post) { return response.notFound("Post Not Found") }

    let ivsStream: GetStreamCommandOutput
    try {
      ivsStream = await this.ivsService.getStream(stream.channelArn)
    } catch {
      return response.badRequest("Stream Not Live")
    }

    const { notifications } = request.all()

    let newStream, newPost
    try {
      const streamStart = ivsStream.stream?.startTime;

      if (streamStart && !stream.startedAt) {
        stream.startedAt = DateTime.fromJSDate(streamStart).toLocal()
      }

      stream.streamId = ivsStream.stream?.streamId || null
      stream.url = ivsStream.stream?.playbackUrl || null
      stream.isLive = true
      stream.status = EStreamStatus.LIVE

      newStream = await stream.save()

      post.isDraft = false
      newPost = await post.save()
    } catch (error) {
      return response.internalServerError({
        message: "Error starting broadcast",
        error
      })
    }

    try {
      await this.databaseRealtime.ref('zurnoPost/' + newPost.id).set({
        ...newPost.serialize(),
        stream: newStream.serialize()
      })
    } catch (error) {
      return response.internalServerError({
        message: "Error uploading to firebase",
        error
      })
    }

    if (notifications) {
      try {
        await this.streamService.notifyStream(streamId)

      } catch (error) {
        return response.internalServerError({
          message: "Error sending notification",
          error
        })
      }
    }

    return response.ok(stream)
  }

  /**
   * @sendNotifications
   * @tag Admin Stream Post
   * @summary Summary
   * @paramPath id - ID of Stream - @type(string) @required
   * @responseBody 200 - <ZnStream> - Description
   */
  async sendNotifications({ params, response }: HttpContext) {
    try {

      const streamId = params.id

      const stream = await ZnStream.find(streamId)

      if (!stream) { return response.notFound("Stream Not Found") }

      if (!stream.isLive) { return response.badRequest("Stream Not Live") }

      const notification = await Notification.findBy({
        type: NOTIFICATION_TYPE.STREAM_START,
        resourceId: stream.postId,
      })

      if (notification) {
        return response.badRequest("Stream Notifications Already Sent")
      }

      await this.streamService.notifyStream(streamId)

      return response.ok("Stream notifications sent")

    } catch (error) {
      console.log(error);
      return response.internalServerError(error)
    }
  }


  /**
   * @endBroadcast
   * @tag Admin Stream Post
   * @summary Summary
   * @paramPath id - ID of Stream - @required
   * @responseBody 200 - <ZnStream> - Description
   */
  async endBroadcast({ params, response }: HttpContext) {
    try {
      const streamId = params.id

      const stream = await ZnStream.find(streamId)

      if (!stream) { return response.notFound("Stream Not Found") }

      if (!stream.isLive) { return response.notFound("Stream Not Live") }

      await this.streamService.stopStream(streamId)

      return response.ok("Stream ended")
    } catch (error) {
      console.log(error);
      return response.internalServerError(error)
    }
  }

  // /**
  //  * @getViewerCount
  //  * @tag Admin Stream Post
  //  * @summary Get viewer count
  //  * @paramPath id - ID of Stream - @required
  //  * @responseBody 200 - {"viewerCount":100} - Get viewer count and put metadata through stream (only during live stream)
  //  */
  // async getViewerCount({ params, response }: HttpContext) {
  //   const streamId = params.id

  //   const stream = await ZnStream.find(streamId)

  //   if (!stream) { return response.notFound("Stream Not Found") }

  //   let ivsStream: GetStreamCommandOutput
  //   try {
  //     ivsStream = await this.ivsService.getStream(stream.channelArn)
  //   } catch {
  //     return response.badRequest("Stream Not Live")
  //   }

  //   const metadata = {
  //     event: STREAM_EVENT.VIEWER_COUNT,
  //     timestamp: moment.now(),
  //     viewerCount: ivsStream.stream?.viewerCount,
  //   }

  //   const metadataString = JSON.stringify(metadata)

  //   await this.ivsService.putMetadata(stream.channelArn, metadataString)

  //   const lastestStreamViewTracking = await ZnTracking.query()
  //     .where({
  //       resource: this.trackingService.getResourceByAction(TRACKING_ACTION.GET_STREAM_VIEW),
  //       resourceId: streamId,
  //       action: TRACKING_ACTION.GET_STREAM_VIEW,
  //     })
  //     .orderBy('createdAt', 'desc')
  //     .first()

  //   const latestCount = {
  //     current: lastestStreamViewTracking?.details?.current || 0,
  //     highest: lastestStreamViewTracking?.details?.highest || 0,
  //     total: lastestStreamViewTracking?.details?.total || 0,
  //   }

  //   const currentCount = metadata.viewerCount || 0
  //   const changeCount = currentCount - latestCount.current
  //   const highestCount = Math.max(latestCount.highest, currentCount)
  //   const totalCount = latestCount.total + Math.max(changeCount, 0)

  //   await this.trackingService.create({
  //     userId: "",
  //     resource: this.trackingService.getResourceByAction(TRACKING_ACTION.GET_STREAM_VIEW),
  //     resourceId: streamId,
  //     action: TRACKING_ACTION.GET_STREAM_VIEW,
  //     details: JSON.stringify({
  //       current: currentCount,
  //       change: changeCount,
  //       highest: highestCount,
  //       total: totalCount,
  //     })
  //   })

  //   return response.ok({ viewerCount: metadata.viewerCount })
  // }

  /**
   * @putMetadata
   * @tag Admin Stream Post
   * @summary Put metadata through stream
   * @paramPath id - ID of Stream - @required
   * @requestBody {"event":"","timestamp":1740491761412,"attributes":{}}
   * @responseBody 200 - Data here - Put metadata through stream (only during live stream)
   */
  async putMetadata({ auth, params, request, response }: HttpContext) {
    const streamId = params.id

    const stream = await ZnStream.find(streamId)

    if (!stream) { return response.notFound("Not Found") }

    if (!stream.isLive) { return response.badRequest("Not Live") }

    const data = request.body() as any

    const payload = await createStreamPostMetadataValidator.validate(data)

    const currentAdmin = auth.getUserOrFail() as ZnAdmin

    const metadata: any = {
      event: payload.event,
      timestamp: payload.timestamp,
      admin: {
        id: currentAdmin.id,
        username: currentAdmin.username,
        name: currentAdmin.name,
      },
    }

    let metadataString = ''

    switch (payload.event) {
      case STREAM_EVENT.VARIANT_START: {
        const variant = await ZnProductVariant.query()
          .where('id', payload.attributes?.variantId!)
          .preload('product', (productQuery) => {
            productQuery
              .preload('reviewsSummary')
              .preload('image')
          })
          .preload('image')
          .first()

        if (!variant) {
          return response.badRequest('Product Not Found')
        }

        metadata.variant = {
          id: variant.id,
          shopifyVariantId: variant.shopifyVariantId,
          title: variant.title,
          price: variant.price,
          sku: variant.sku,
          compareAtPrice: variant.compareAtPrice,
          inventoryQuantity: variant.inventoryQuantity,
          inventoryManagement: variant.inventoryManagement,
          availableForSale: variant.availableForSale,

          image: {
            src: variant.image?.src || variant.product?.image?.src
          },

          product: {
            id: variant.product?.id,
            title: variant.product?.title,
            shopifyProductId: variant.product?.shopifyProductId,
            reviewSummary: {
              totalReviews: variant.product?.reviewSummary?.totalReviews,
              averageRating: variant.product?.reviewSummary?.averageRating,
            }
          },
        }

        const bundle = await ZnBundleProduct.query()
          .where('mainProductId', variant.productId)
          .where('isActive', true)
          // .preload('items', (query) => {
          //   query
          //     .where('isActive', true)
          //     .preload('product')
          //     .preload('variants', (query) => {
          //       query.preload('image')
          //     })
          // })
          // .preload('discount')
          // .preload('mainProduct', (query) => {
          //   query
          //     .preload('images')
          //     .preload('variant')
          //     .preload('reviewsSummary')
          // })
          .first()

        if (bundle) {
          metadata.variant.bundleId = bundle.id
          metadata.variant.inventoryQuantity = 0
          // metadata.bundle = {
          //   id: bundle.id,

          //   discount: {
          //     id: bundle.discount?.id,
          //     type: bundle.discount?.type,
          //     value: bundle.discount?.value,
          //   },

          //   mainProduct: {
          //     id: bundle.mainProduct?.id,
          //     // title: bundle.mainProduct?.title,
          //     // price: bundle.mainProduct.price,
          //     // compareAtPrice: bundle.mainProduct.compareAtPrice,

          //     items: bundle.items.map((item) => ({
          //       variants: item.variants?.map((vari) => ({
          //         id: vari.id,
          //         title: vari.title,
          //         sku: vari.sku,
          //         price: vari.price,
          //         shopifyVariantId: vari.shopifyVariantId,
          //         availableForSale: vari.availableForSale,
          //       }))
          //     })),

          //     images: bundle.mainProduct?.images?.map((img) => ({
          //       id: img.id,
          //       src: img.src,
          //     })),

          //     reviewSummary: {
          //       totalReviews: bundle.mainProduct?.reviewSummary?.totalReviews,
          //       averageRating: bundle.mainProduct?.reviewSummary?.averageRating,
          //     }
          //   }
          // }
        }

        if (payload.attributes?.refresh) {
          metadata.refresh = true
        }

        metadataString = JSON.stringify(metadata)

        if (metadataString.length >= 900) {
          metadata.variant = { id: variant.id }
          metadataString = JSON.stringify(metadata)
        }

        if (payload.attributes?.refresh) { break }

        if (stream.startedAt) {
          const variantStartSecs = DateTime.fromMillis(metadata.timestamp).diff(stream.startedAt).valueOf() / 1000
          const latestTimeline = await ZnVideoTimeline.query()
            .where({ postId: stream.postId, })
            .orderBy('createdAt', 'desc')
            .first()

          // check if new product variant or not
          if (latestTimeline?.variantId != variant.id) {
            // save variant to db if new
            await ZnVideoTimeline.create({
              postId: stream.postId,
              variantId: variant.id,
              start: variantStartSecs,
              end: variantStartSecs + 10,
            })
          }
        }


        break
      }
      case STREAM_EVENT.VARIANT_END: {
        metadata.variant = { id: payload.attributes?.variantId }
        metadataString = JSON.stringify(metadata)

        if (stream.startedAt) {
          const variantEndSecs = DateTime.fromMillis(metadata.timestamp).diff(stream.startedAt).valueOf() / 1000

          const timeline = await ZnVideoTimeline.query()
            .where({
              postId: stream.postId,
              variantId: payload.attributes?.variantId,
            })
            .orderBy('createdAt', 'desc')
            .first()

          // save variant end to db
          if (timeline) {
            timeline.end = variantEndSecs
            await timeline.save()
          }
        }

        break;
      }
      default:
        break
    }

    // send metadata through stream
    const ivsMetadata = await this.ivsService.putMetadata(stream.channelArn, metadataString)

    return response.ok(ivsMetadata)
  }

  async syncStreamInteractions({ params, response }: HttpContext) {
    try {
      const streamId = params.id

      const stream = await ZnStream.query()
        .where({ id: streamId })
        .first()

      if (!stream) {
        return response.notFound('Stream Not Found')
      }

      // count number of joins and leaves
      const trackings = await ZnTracking.query()
        .where({ resourceId: streamId, resource: this.trackingService.getResourceByAction(TRACKING_ACTION.GET_STREAM_VIEW) })

      // let streamInteractions: ZnStreamUserInteract[] = []
      let streamInteractions: any[] = []
      for (const track of trackings) {
        if (!track.userId) { track.userId = "" }

        if (!streamInteractions.find(interact => interact.userId == track.userId)) {
          streamInteractions.push({ streamId, userId: track.userId })
        }

        streamInteractions = streamInteractions.map((interaction) => {
          if (interaction.userId != (track.userId || "")) { return interaction }
          else {
            if (track.action == TRACKING_ACTION.GET_STREAM_VIEW) {
              if (track.details?.change == 1) {
                interaction.joinCount = (interaction.joinCount || 0) + 1
              } else if (track.details?.change == -1) {
                interaction.leaveCount = (interaction.leaveCount || 0) + 1
              }
            }
            return interaction
          }
        })
      }

      // count number of likes
      const likes = await ZnUserLikeResource.query()
        .where({
          resourceType: RESOURCE_TYPE.STREAM,
          resourceId: stream.id,
        })

      for (const like of likes) {
        if (!like.userId) { like.userId = "" }

        if (!streamInteractions.find(interact => interact.userId == like.userId)) {
          streamInteractions.push({ streamId, userId: like.userId })
        }

        streamInteractions = streamInteractions.map((interaction) => {
          if (interaction.userId != (like.userId || "")) { return interaction }
          else {
            interaction.likeCount = (interaction.likeCount || 0) + 1
            return interaction
          }
        })
      }

      // count number of chat/comment
      const chats = await ZnChatMessage.query()
        .where({ roomId: stream.roomId })

      for (const chat of chats) {
        streamInteractions = streamInteractions.map((interaction) => {
          if (interaction.userId != chat.userId) { return interaction }
          else {
            interaction.commentCount = (interaction.commentCount || 0) + 1
            return interaction
          }
        })
      }

      let result: ZnStreamUserInteract[] = []
      const maxLuckyNumberInteract = await ZnStreamUserInteract.query()
        .where({ streamId })
        .max('luckyNumber', 'maxLuckyNumber')
        .first()

      let maxLuckyNumber = maxLuckyNumberInteract?.$extras?.maxLuckyNumber || 0

      for (const interaction of streamInteractions) {
        if (!interaction.userId) { interaction.userId = null }

        let interact = await ZnStreamUserInteract.query()
          .where({ streamId: stream.id, userId: interaction.userId })
          .first()

        let luckyNumber
        if (interaction.userId) {
          if (interaction.likeCount > 0 || interaction.commentCount > 0) {
            luckyNumber = interact?.luckyNumber || ++maxLuckyNumber
          }
        }

        interact = await ZnStreamUserInteract.updateOrCreate(
          { streamId: stream.id, userId: interaction.userId },
          { ...interaction, luckyNumber }
        )

        await interact.load('user')
        if (interact.luckyNumber && interact.luckyNumber > 0) {
          result.push(interact)
        }
      }

      return response.ok(result)

    } catch (error) {
      console.log(error);
      return response.internalServerError("Something went wrong!")
    }
  }

  async getViewersFullList({ params, response }: HttpContext) {
    try {
      const streamId = params.id

      const interactions = await ZnStreamUserInteract.query()
        .preload('user')
        .where({ streamId })
        .whereNotNull('luckyNumber')
        .where('luckyNumber', '>', 0)
        .orderBy('luckyNumber')

      return response.ok(interactions)

    } catch (error) {
      console.log(error);
      return response.internalServerError("Something went wrong!")
    }
  }

  async getViewersPaginatedList({ params, request, response }: HttpContext) {
    try {
      const streamId = params.id
      const { page = 1, limit = 10 } = request.all()

      const interactions = await ZnStreamUserInteract.query()
        .preload('user')
        .where({ streamId })
        .whereNotNull('luckyNumber')
        .where('luckyNumber', '>', 0)
        .orderBy('luckyNumber')
        .paginate(page, limit)

      return response.ok(interactions)

    } catch (error) {
      console.log(error);
      return response.internalServerError({
        message: "Something went wrong!",
        error
      })
    }
  }

  async removeViewerFromRaffleList({ params, request, response }: HttpContext) {
    try {
      const streamId = params.id

      const { interactionId } = request.all()

      const interaction = await ZnStreamUserInteract.query()
        .preload('user')
        .where({ id: interactionId })
        .first()

      if (!interaction) { return response.notFound('Not Found') }

      if (interaction.streamId != streamId) {
        return response.badRequest('Wrong Stream')
      }

      interaction.luckyNumber = -interaction.luckyNumber
      await interaction.save()

      return response.ok(interaction)

    } catch (error) {
      console.log(error);
      return response.internalServerError({
        message: "Something went wrong!",
        error
      })
    }
  }

  async saveStreamRaffleWinner({ params, request, response }: HttpContext) {
    try {
      const streamId = params.id

      const { interactionId } = request.all()

      const interaction = await ZnStreamUserInteract.query()
        .preload('user')
        .where({ id: interactionId })
        .first()

      if (!interaction) { return response.notFound('Not Found') }

      if (interaction.streamId != streamId) {
        return response.badRequest('Wrong Stream')
      }

      interaction.raffleWinner = true
      await interaction.save()

      return response.ok(interaction)

    } catch (error) {
      console.log(error);
      return response.internalServerError({
        message: "Something went wrong!",
        error
      })
    }
  }

  /**
   * @statistics
   * @tag Admin Stream Post
   * @summary Get Stream statistic
   * @paramPath id - ID of Stream - @required
   * @responseBody 200 - {"likesCount":0,"messagesCount":0,"sharesCount":0,"views":["ZnTracking"],"stream":"ZnStream","variantTimeline":["ZnVideoTimeline"],"messages":["ZnChatMessage"]} - Get Stream statistic descriptively
   */
  async statistics({ params, response }: HttpContext) {
    const streamId = params.id

    const stream = await ZnStream.query()
      .where('id', streamId)
      .first()

    if (!stream) { return response.notFound("Stream Not Found") }

    const interactions = await this.trackingService.getInteractions({
      resourceId: stream.postId,
      actions: [
        TRACKING_ACTION.ADD_WISHLIST,
        TRACKING_ACTION.SHARE,
      ]
    })

    const likesCount = interactions[TRACKING_ACTION.ADD_WISHLIST].count
    const sharesCount = interactions[TRACKING_ACTION.SHARE].count

    const variantTimelines = await ZnVideoTimeline.query()
      .preload('variant', (variantQuery) => {
        variantQuery
          .preload('image')
          .preload('product')
      })
      .where({ postId: stream.postId })
      // .orderBy('start', 'asc')
      .distinct("variantId")

    const views = await ZnTracking
      .query()
      .where({
        resource: this.trackingService.getResourceByAction(TRACKING_ACTION.GET_STREAM_VIEW),
        resourceId: stream.id,
        action: TRACKING_ACTION.GET_STREAM_VIEW
      })
      .orderBy('createdAt', 'asc')

    const streamInteractions = await ZnStreamUserInteract.query()
      .where({ streamId })
      .preload('user', (userQuery) => {
        userQuery.preload('avatarMedia')
      })

    return response.ok({
      likesCount,
      sharesCount,
      views,
      stream,
      variantTimelines,
      streamInteractions,
    })
  }


}
