import { ACTION, RESOURCE } from "#constants/authorization";
import VendorEarningService from "#services/vendors/vendor_earning_service";
import { HttpContext } from "@adonisjs/core/http";

export default class AdminVendorEarningController {
  private vendorEarningService: VendorEarningService;

  constructor() {
    this.vendorEarningService = new VendorEarningService();
  }

  async index({ bouncer, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.VENDOR);

      const { vendorId } = request.params();
      const { page = 1, limit = 10 } = request.all();

      if (vendorId) {
        const vendors = await this.vendorEarningService.getAllEarningsByVendor(vendorId, page, limit);
        return response.ok(vendors);
      } else {
        const vendors = await this.vendorEarningService.getAllEarnings(page, limit);
        return response.ok(vendors);
      }
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async show({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.VENDOR);
      const { id } = params;
      if (!id) {
        return response.badRequest({ message: "Earning ID is required" });
      }

      const vendor = await this.vendorEarningService.getEarningById(id);
      return response.ok(vendor);
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async update({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.VENDOR);
      const id = params.id;
      if (!id) {
        return response.badRequest({ message: "Earning ID is required" });
      }
      const data = request.all();
      const updatedVendor = await this.vendorEarningService.update(id, data);
      return response.ok(updatedVendor);
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async destroy({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.VENDOR);
      const { id } = params;
      if (!id) {
        return response.badRequest({ message: "Vendor Earning ID is required" });
      }
      await this.vendorEarningService.delete(id);
      return response.ok({ message: "Vendor Earning deleted successfully" });
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }
}