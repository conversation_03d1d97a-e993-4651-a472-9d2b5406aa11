import { ACTION, RESOURCE } from "#constants/authorization";
import VendorPaymentService from "#services/vendors/vendor_payment_service";
import { HttpContext } from "@adonisjs/core/http";

export default class AdminVendorPaymentController {
  private vendorPaymentService: VendorPaymentService;

  constructor() {
    this.vendorPaymentService = new VendorPaymentService();
  }

  async index({ bouncer, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.VENDOR);

      const { vendorId } = request.params();
      const { page = 1, limit = 10 } = request.all();

      if (vendorId) {
        const payments = await this.vendorPaymentService.getAllPaymentsByVendor(vendorId, page, limit);
        return response.ok(payments);
      } else {
        const payments = await this.vendorPaymentService.getAllPayments(page, limit);
        return response.ok(payments);
      }
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async show({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.VENDOR);
      const { id } = params;
      if (!id) {
        return response.badRequest({ message: "Payment ID is required" });
      }

      const payment = await this.vendorPaymentService.getPaymentById(id);
      return response.ok(payment);
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async store({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.VENDOR);
      const vendorId = params.vendorId;
      if (!vendorId) {
        return response.badRequest({ message: "Vendor ID is required" });
      }
      const data = request.all();
      const createdPayment = await this.vendorPaymentService.create(vendorId, data);
      return response.ok(createdPayment);
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async update({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.VENDOR);
      const id = params.id;
      if (!id) {
        return response.badRequest({ message: "Earning ID is required" });
      }
      const data = request.all();
      const updatedVendor = await this.vendorPaymentService.update(id, data);
      return response.ok(updatedVendor);
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async destroy({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.VENDOR);
      const { id } = params;
      if (!id) {
        return response.badRequest({ message: "Payout ID is required" });
      }
      const payment = await this.vendorPaymentService.getPaymentById(id);
      if(payment) {
        await payment.softDelete()
        return response.ok({ message: "Payout deleted successfully" });
      }
      return response.notFound("payment not found!");

    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }
}
