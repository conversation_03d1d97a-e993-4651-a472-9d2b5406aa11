import { ACTION, RESOURCE } from "#constants/authorization";
import VendorPaymentMethodService from "#services/vendors/vendor_payment_method_service";
import { HttpContext } from "@adonisjs/core/http";

export default class AdminVendorPaymentMethodController {
  private vendorPaymentMethodService: VendorPaymentMethodService;

  constructor() {
    this.vendorPaymentMethodService = new VendorPaymentMethodService();
  }

  async index({ bouncer, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.VENDOR);

      const { vendorId } = request.params();
      if (!vendorId) {
        return response.badRequest('Vendor ID is required.');
      }

      const result = await this.vendorPaymentMethodService.getAllPaymentMethodsByVendor(vendorId);
      if (result.success)
        return response.ok(result.paymentMethods);
      else
        return response.badRequest(result.message);

    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async show({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.VENDOR);
      const { id } = params;
      if (!id) {
        return response.badRequest({ message: "Earning ID is required" });
      }

      const vendor = await this.vendorPaymentMethodService.getPaymentMethodById(id);
      return response.ok(vendor);
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async store({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.VENDOR);
      const vendorId = params.vendorId;
      if (!vendorId) {
        return response.badRequest({ message: "Vendor ID is required" });
      }
      const data = request.all();
      const createdPayment = await this.vendorPaymentMethodService.create(vendorId, data);
      return response.ok(createdPayment);
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async update({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.VENDOR);
      const id = params.id;
      if (!id) {
        return response.badRequest({ message: "Earning ID is required" });
      }
      const data = request.all();
      const updatedVendor = await this.vendorPaymentMethodService.update(id, data);
      return response.ok(updatedVendor);
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async destroy({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.VENDOR);
      const { id } = params;
      if (!id) {
        return response.badRequest({ message: "Vendor Earning ID is required" });
      }
      await this.vendorPaymentMethodService.delete(id);
      return response.ok({ message: "Vendor Earning deleted successfully" });
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }
}