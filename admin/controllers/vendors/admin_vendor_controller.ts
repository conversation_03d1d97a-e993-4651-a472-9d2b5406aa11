import { ACTION, RESOURCE } from "#constants/authorization";
import ProductInventoryChangeJob from "#jobs/product_inventory_change_job";
import ZnProduct from "#models/zn_product";
import ZnVendor from "#models/zn_vendor";
import ZnWarehouse from "#models/zn_warehouse";
import VendorService from "#services/vendors/vendor_service";
import { HttpContext } from "@adonisjs/core/http";
import queue from "@rlanz/bull-queue/services/main";

export default class AdminVendorController {
  private vendorService: VendorService;

  constructor() {
    this.vendorService = new VendorService();
  }

  async settings({ bouncer, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.VENDOR);
      const settings = await this.vendorService.getSettings();
      return response.ok(settings);
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async index({ bouncer, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.VENDOR);

      const { page = 1, limit = 10, search, filter } = request.all();
      const vendors = await this.vendorService.getAllVendors(page, limit, search, filter);

      return response.ok(vendors);
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async show({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.VENDOR);
      const { id } = params;
      if (!id) {
        return response.badRequest({ message: "Vendor ID is required" });
      }

      const vendor = await this.vendorService.getVendorById(id);

      return response.ok(vendor);

    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async stats({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.VENDOR);
      const { vendorId } = params;
      if (!vendorId) {
        return response.badRequest({ message: "Vendor ID is required" });
      }

      const stats = await this.vendorService.getVendorStats(vendorId);
      return response.ok(stats);
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async getBalance({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.VENDOR);
      const { vendorId } = params;
      if (!vendorId) {
        return response.badRequest({ message: "Vendor ID is required" });
      }

      const balance = await this.vendorService.getVendorBalance(vendorId);
      return response.ok(balance);
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async store({ bouncer, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.VENDOR);
      return response.ok('store');
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async update({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.VENDOR);
      const id = params.id;
      if (!id) {
        return response.badRequest({ message: "Vendor ID is required" });
      }
      const data = request.all();
      const updatedVendor = await this.vendorService.update(id, data);
      return response.ok(updatedVendor);
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async destroy({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.VENDOR);
      const { id } = params;
      if (!id) {
        return response.badRequest({ message: "Vendor ID is required" });
      }
      await this.vendorService.delete(id);
      return response.ok({ message: "Vendor deleted successfully" });
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async selectShopifyWarehouseLocation({ response }: HttpContext) {
    try {
      const result = await ZnWarehouse.query()
        .whereNotNull('shopifyLocationId')

      return response.ok(result)

    } catch (error) {
      console.error(error);
      return response.internalServerError({
        message: 'Something went wrong!',
        error,
      });
    }
  }

  async changeShopifyWarehouseLocation({ request, response }: HttpContext) {
    try {
      const { vendorId, warehouseId } = request.body()

      const vendor = await ZnVendor.find(vendorId)
      if (!vendor) { return response.notFound("Vendor Not Found") }

      const warehouse = await ZnWarehouse.find(warehouseId)
      if (!warehouse) { return response.notFound("Warehouse Not Found") }

      const products = await ZnProduct.query()
        .where({ vendorId })

      for (const product of products) {
        queue.dispatch(
          ProductInventoryChangeJob,
          {
            productId: product.id,
            shopifyLocationId: warehouse.shopifyLocationId,
          },
          { queueName: 'syncData' }
        )
        // await this.productService.changeInventory(product.id, shopifyLocationId)
      }

      vendor.warehouseId = warehouseId
      await vendor.save()

      return response.ok('Update Jobs Sent!')

    } catch (error) {
      console.error(error);
      return response.internalServerError({
        message: 'Something went wrong!',
        error,
      });
    }
  }
}