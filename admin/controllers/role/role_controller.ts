import ZnPermission from '#models/zn_permission'
import ZnRole from '#models/zn_role'
import type { HttpContext } from '@adonisjs/core/http'
import { ACTION, RESOURCE } from '../../../app/constants/authorization.js'
import { createRoleValidator } from '../../validators/role/role_validator.js'

export default class AdminRoleController {
  /**
   * @index
   * @tag Admin Role
   * @summary Read all roles
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @responseBody 200 - <ZnRole[]>.append("id":"").paginated() - Read all roles descriptively
   */
  /**
   * Display a list of resource
   */
  async index({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.ROLE)

    try {
      const { page = 1, limit = 10, search } = request.qs()

      const query = ZnRole.query()
        .preload('permissions')
        .withCount('permissions')
        .whereNull('deletedAt')

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder.where('name', 'LIKE', `%${search}%`)
        })
      }

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @create
   * @tag Admin Role
   * @summary Return info for creation
   * @responseBody 200 - {"info":{"permissions":["ZnPermission"]}} - Return info for creation descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display form to create a new record
   */
  async create({ bouncer, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.ROLE)

    try {
      const permissions = await ZnPermission.query()
        .preload('resource')
        .orderBy('orderBy')
        .whereNull('deletedAt')

      return response.ok({
        info: {
          permissions: permissions,
        },
      })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @store
   * @tag Admin Role
   * @summary Create a role
   * @requestBody <ZnRole>.append("permissionIds":[""])
   * @responseBody 201 - <ZnRole>.append("id":"","createdAt":"","updatedAt":"")
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The name field must be defined","rule":"required","field":"name"}]}
   * @responseBody 401 - Unauthorized access
   */
  /**
   * Handle form submission for the create action
   */
  async store({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.ROLE)

    const data = request.all()

    const payload = await createRoleValidator.validate(data)

    try {
      const created = await ZnRole.create({
        name: payload.name,
      })

      if (payload.permissionIds && payload.permissionIds.length > 0) {
        await created.related('permissions').sync(payload.permissionIds)
      }

      return response.created(created)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @show
   * @tag Admin Role
   * @summary Read role
   * @paramPath id - ID of Role - @type(string) @required
   * @responseBody 200 - <ZnRole>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read a role descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Role not found"} - Not Found
   */
  /**
   * Show individual record
   */
  async show({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.ROLE)

    const roleId = params.id

    const role = await ZnRole.query().preload('permissions').where('id', roleId).first()

    if (!role) {
      return response.notFound({ message: 'Role not found' })
    }

    return response.ok(role)
  }

  /**
   * @edit
   * @tag Admin Role
   * @summary Return info for updating
   * @responseBody 200 - {"data":"<ZnRole>","permissions":{"roles":["ZnPermission"]}} - Return info for updating descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Role not found"} - Not Found
   */
  /**
   * Edit individual record
   */
  async edit({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.ROLE)

    try {
      const roleId = params.id

      const role = await ZnRole.query().preload('permissions').where('id', roleId).first()

      if (!role) {
        return response.notFound({ message: 'Role not found' })
      }

      const permissions = await ZnPermission.query()
        // .orderBy('orderBy', 'asc')
        .preload('resource')
        // .orderBy('orderBy', 'asc')
        .whereNull('deletedAt')

      return response.ok({
        data: {
          ...role?.serialize(),
        },
        info: {
          permissions: permissions,
        },
      })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @update
   * @tag Admin Role
   * @summary Update role
   * @description Update role descriptively
   * @paramPath id - ID of Role - @type(string) @required
   * @requestBody <ZnRole>.append("permissionIds":[""])
   * @responseBody 200 - <ZnRole>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The name field must be defined","rule":"required","field":"name"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Role not found"} - Not Found
   */
  /**
   * Handle form submission for the edit action
   */
  async update({ bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.ROLE)

    const roleId = params.id

    const role = await ZnRole.query().where('id', roleId).first()

    if (!role) {
      return response.notFound({ message: 'Role not found' })
    }

    const data = request.all()

    const payload = await createRoleValidator.validate(data)

    try {
      role.name = payload.name || role.name

      if (Array.isArray(payload.permissionIds)) {
        await role.related('permissions').sync(payload.permissionIds)
      }

      const updated = await role.save()

      return response.ok(updated)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @destroy
   * @tag Admin Role
   * @summary Soft-delete a role
   * @description Soft-delete a role descriptively
   * @paramPath id - ID of Role - @type(string) @required
   * @responseBody 200 - {"message":"Role soft-deleted successfully"} - OK
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Role not found"} - Not Found
   */
  /**
   * Delete record
   */
  async destroy({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.ROLE)

    const roleId = params.id

    const role = await ZnRole.find(roleId)

    if (!role) {
      return response.notFound({ message: 'Role not found' })
    }

    await role.softDelete()

    return response.ok({ message: 'Role soft-deleted successfully' })
  }
}
