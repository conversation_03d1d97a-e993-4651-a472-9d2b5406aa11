import { EApprovalStatus } from "#constants/approval_status";
import { ACTION, RESOURCE } from "#constants/authorization";
import { NOTIFICATION_TYPE } from "#constants/notification";
import CommissionAdjustedNotification from "#mails/affiliation/commission_adjusted_notification";
import CommissionNotification from "#mails/affiliation/commission_notification";
import Notification from "#models/notification";
import ZnAffiliateCommission from "#models/zn_affiliate_commission";
import ZnUser from "#models/zn_user";
import { AffiliationCommissionService } from "#services/affiliation/affiliation_commission_service";
import { NotificationService } from "#services/notification_service";
import env from "#start/env";
import { HttpContext } from "@adonisjs/core/http";
import logger from "@adonisjs/core/services/logger";
import mail from "@adonisjs/mail/services/main";
import { AdminNotificationService } from "../../services/notification/admin_notification_service.js";
import { commissionAdjustmentValidator, commissionStatusValidator } from "../../validators/affiliation/commission_validator.js";

export default class AdminAffiliateCommissionController {
  private commissionService: AffiliationCommissionService
  private adminNotificationService: AdminNotificationService

  constructor() {
    this.commissionService = new AffiliationCommissionService()
    this.adminNotificationService = new AdminNotificationService()
  }

  async index({ bouncer, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.AFFILIATION)
      const { page, limit } = request.qs();

      const commissions = await ZnAffiliateCommission.query()
        .orderBy('createdAt', 'desc')
        .preload('affiliate', (table) => {
          table.preload('user').withScopes((scopes) => scopes.withAll())
        })
        .paginate(page, limit);
      return response.ok(commissions)

    } catch (error) {
      console.error(error);
      if (error.status == 500)
        return response.internalServerError(error);
      else
        return response.badRequest(error);
    }
  }

  async show({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.AFFILIATION)
      const commissionId = params.id;
      return await this.commissionService.getCommissionById(commissionId)
    } catch (error) {
      console.error(error);
      if (error.status == 500)
        return response.internalServerError(error);
      else
        return response.badRequest(error);
    }
  }

  async update({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.AFFILIATION)

      const commissionId = params.id
      const payload = await commissionAdjustmentValidator.validate(request.body())

      const commission = await ZnAffiliateCommission.findOrFail(commissionId)

      const lastAdjustedAmount = commission.finalAmount

      commission.adjustedAmount = payload.adjustedAmount
      commission.adjustedReason = payload.adjustedReason
      await commission.save()

      if (commission.status == EApprovalStatus.APPROVED) {
        await commission.load('affiliate', (query) => {
          query.preload('user')
        })
        await commission.load('order', (query) => {
          query.preload('user')
        })
        await commission.load('refCode')

        const affiliate = commission.affiliate
        affiliate.updateData(0, 0, 0, commission.finalAmount - lastAdjustedAmount)
        await affiliate.save()

        const orderTimeString = commission.createdAt.toFormat('EEE, MMM dd, yyyy, hh:mm a')
        const audienceName = `${commission.order.user.firstName} ${commission.order.user.lastName}`;
        const orderTotalString = `$${commission.order.totalPrice}`;
        const previousAmountString = (lastAdjustedAmount !== null && lastAdjustedAmount > 0) ? `$${lastAdjustedAmount.toFixed(2)}` : `$${commission.commissionAmount.toFixed(2)}`
        const newAmountString = `$${commission.finalAmount.toFixed(2)}`;

        if (env.get('DISABLE_AFFILIATE_NOTIFICATIONS') === 'true') {
          // const admins = await ZnAdmin.all()
          const admins = await this.adminNotificationService.getAdminsByPermissions([
            { action: ACTION.READ, resource: RESOURCE.AFFILIATION }
          ])
          admins.forEach(async (admin) => {
            await mail.send(new CommissionAdjustedNotification(
              commission.affiliate.user.firstName,
              commission.id,
              orderTimeString,
              audienceName,
              orderTotalString,
              previousAmountString,
              newAmountString,
              commission.adjustedReason,
              commission.refCode.code,
              admin.username))
              .then(() => {
                logger.info(`Commission adjusted email has been sent successfully to ${admin.username}`);
              })
              .catch((error) => {
                console.error('Error when sending email', error);
              })
          });
        } else {
          await mail.send(new CommissionAdjustedNotification(
            commission.affiliate.user.firstName,
            commission.id,
            orderTimeString,
            audienceName,
            orderTotalString,
            previousAmountString,
            newAmountString,
            commission.adjustedReason,
            commission.refCode.code,
            commission.affiliate.user.email))
            .then(() => {
              logger.info(`Commission adjusted email has been sent successfully to ${commission.affiliate.user.email}`);
            })
            .catch((error) => {
              console.error('Error when sending email', error);
            })

          await this.sendPushNotification(
            commission.affiliate.user,
            'commissions',
            'Commission Amount Adjusted',
            'Your commission amount has been updated. Tap to see details and the reason for adjustment.'
          )
        }
      }

      return response.ok(commission)

    } catch (error) {
      console.error(error)
      if (error.status == 500)
        return response.internalServerError(error)
      else
        return response.badRequest(error)
    }
  }

  async setStatus({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.AFFILIATION)

      const commissionId = params.id;
      const payload = await commissionStatusValidator.validate(request.body());

      const commission = await ZnAffiliateCommission.query()
        .where('id', commissionId)
        .preload('affiliate', (query) => {
          query
            .withScopes((scopes) => scopes.withAll())
            .preload('user')
        })
        .preload('order', (query) => {
          query.preload('user');
          query.preload('orderDetails', (query) => {
            query.preload('variant')
          })
        })
        .preload('refCode')
        .preload('commissionDetails', (query) => {
          query.preload('orderDetail');
        })
        .firstOrFail();

      const affiliate = commission.affiliate
      if (affiliate.deletedAt != null) {
        return response.abort('Affiliate was deleted')
      }

      let itemsSold = 0;
      let totalGmv = 0;
      for (const commissionDetail of commission.commissionDetails) {
        if (commissionDetail.commissionRate > 0) {
          itemsSold += commissionDetail.orderDetail.currentQuantity;
          totalGmv += commissionDetail.orderDetail.price * commissionDetail.orderDetail.currentQuantity;
        }
      }

      if (payload.status == EApprovalStatus.APPROVED && commission.status != EApprovalStatus.APPROVED)
        affiliate.updateData(1, itemsSold, totalGmv, commission.finalAmount)
      else if (payload.status != EApprovalStatus.APPROVED && commission.status == EApprovalStatus.APPROVED)
        affiliate.updateData(-1, -itemsSold, -totalGmv, -commission.finalAmount)

      await affiliate.save()

      commission.status = payload.status;
      commission.rejectReason = payload.rejectReason || null;
      await commission.save();

      const isApproved = commission.status == EApprovalStatus.APPROVED;
      const orderTimeString = commission.order.createdAt.toFormat('EEE, MMM dd, yyyy, hh:mm a');
      const audienceName = `${commission.order.user.firstName} ${commission.order.user.lastName}`;
      const orderTotalString = `$${commission.order.totalPrice}`;
      const commissionAmountString = `$${commission.finalAmount.toFixed(2)}`;

      if (env.get('DISABLE_AFFILIATE_NOTIFICATIONS') === 'true') {
        // const admins = await ZnAdmin.all()
        const admins = await this.adminNotificationService.getAdminsByPermissions([
          { action: ACTION.READ, resource: RESOURCE.AFFILIATION }
        ])
        admins.forEach(async (admin) => {
          await mail.send(new CommissionNotification(commission.affiliate.user.firstName,
            isApproved,
            commission.id,
            orderTimeString,
            audienceName,
            orderTotalString,
            commissionAmountString,
            commission.refCode.code,
            commission.rejectReason,
            admin.username))
            .then(() => {
              logger.info(`Commission email has been sent successfully to ${admin.username}`);
            })
            .catch((error) => {
              console.error('Error when sending email', error);
            });
        });
      } else {
        await mail.send(new CommissionNotification(commission.affiliate.user.firstName,
          isApproved,
          commission.id,
          orderTimeString,
          audienceName,
          orderTotalString,
          commissionAmountString,
          commission.refCode.code,
          commission.rejectReason,
          commission.affiliate.user.email))
          .then(() => {
            logger.info(`Commission email has been sent successfully to ${commission.affiliate.user.email}`);
          })
          .catch((error) => {
            console.error('Error when sending email', error);
          })

        await this.sendPushNotification(
          commission.affiliate.user,
          'commissions',
          isApproved ? 'New Commission Earned' : 'Commission Rejected',
          isApproved ?
            "You've earned a commission! Check your dashboard for details." :
            "A recent commission was not approved. Check your dashboard for details."
        );
      }

      return response.ok(commission);

    } catch (error) {
      console.error(error);
      if (error.status == 500)
        return response.internalServerError(error);
      else
        return response.badRequest(error);
    }
  }

  async syncCommission({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.AFFILIATION);

      const commissionId = params.id;
      if (!commissionId) throw new Error('Commission ID is required');

      const commission = await this.commissionService.syncCommission(commissionId);
      return response.ok({
        success: true,
        commission
      });

    } catch (error) {
      console.error(error);
      if (error.status == 500)
        return response.internalServerError(error);
      else
        return response.badRequest(error);
    }
  }

  async syncAllCommissions({ bouncer, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.AFFILIATION);

      const { affiliateId } = request.all();

      if (affiliateId) {
        const affiliate = await this.commissionService.syncCommissionsByAffiliate(affiliateId);
        return response.ok({
          success: true,
          affiliate
        });
      } else {
        await this.commissionService.syncAllCommissions();
        return response.ok({ success: true });
      }

    } catch (error) {
      console.error(error);
      if (error.status == 500)
        return response.internalServerError(error);
      else
        return response.badRequest(error);
    }
  }

  private async sendPushNotification(user: ZnUser, resourceId: string, title: string, description: string) {
    const notification = await Notification.create({
      type: NOTIFICATION_TYPE.AFFILIATE,
      userId: user.id,
      resourceId: resourceId,
      title: title,
      description: description,
    })

    if (notification) {
      const notificationService = new NotificationService()
      await notificationService.send([user], [notification])
    }
  }
}