import { ACTION, RESOURCE } from "#constants/authorization";
import AffiliationCommissionRateService from "#services/affiliation/affiliation_commission_rate_service";
import { HttpContext } from "@adonisjs/core/http";
import { createCommissionRateValidator, updateCommissionRateValidator } from "../../validators/affiliation/commission_rate_validator.js";

export default class AdminAffiliationCommissionRateController {
  private commissionRateService: AffiliationCommissionRateService;

  constructor() {
    this.commissionRateService = new AffiliationCommissionRateService();
  }

  async index({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.AFFILIATION)

      const commissionGroupId = params.id;
      if (!commissionGroupId) {
        throw new Error('Commission Group ID is required');
      }

      const commissionRates = await this.commissionRateService.findAllByCommissionGroupId(commissionGroupId);

      return response.ok(commissionRates);
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async show({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.AFFILIATION);

      const commissionRateId = params.id;
      if (!commissionRateId) {
        throw new Error('Commission Rate ID is required');
      }

      const commissionRate = await this.commissionRateService.findById(commissionRateId);
      return response.ok(commissionRate);
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async store({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.AFFILIATION);

      const commissionGroupId = params.id;
      if (!commissionGroupId) {
        throw new Error('Commission Group ID is required');
      }

      const data = await createCommissionRateValidator.validate(request.all());
      const created = await this.commissionRateService.create(commissionGroupId, data);

      return response.created(created);
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async update({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.AFFILIATION);

      const commissionRateId = params.id;
      if (!commissionRateId) {
        throw new Error('Commission Rate ID is required');
      }

      const data = await updateCommissionRateValidator.validate(request.all());
      const updated = await this.commissionRateService.update(commissionRateId, data);

      return response.ok(updated);
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }

  async destroy({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.AFFILIATION);

      const commissionRateId = params.id;
      if (!commissionRateId) {
        throw new Error('Commission Rate ID is required');
      }

      await this.commissionRateService.delete(commissionRateId);

      return response.noContent();
    } catch (error) {
      console.error(error);
      return response.badRequest(error);
    }
  }
}