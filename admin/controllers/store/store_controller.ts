import Store from '#models/zn_store'
import { HttpContext } from '@adonisjs/core/http'
import { DateTime } from 'luxon'
import {
  createStoreValidator,
  filterStoreValidator,
} from '../../validators/store/store_validator.js'
import { uploadMediasFromLinks, uploadReports } from '../../../services/media/index.js'
import ZnCountry from '#models/zn_country'
import ZnState from '#models/zn_state'
import ZnCity from '#models/zn_city'
import { GooglePlaceService } from "#services/google/google_place_service";
import ExcelJS from 'exceljs'
import { MEDIA_TYPE } from '#constants/media'

export default class AdminStoreController {
  /**
   * @list
   * @tag Admin Store
   * @summary Read all stores
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @paramQuery search - Search Term - @type(string)
   * @paramQuery ownerIds - IDs of Owners (Users) - @type(array)
   * @paramQuery countryIds - IDs of Country - @type(array)
   * @paramQuery stateIds - IDs of Country - @type(array)
   * @paramQuery cityIds - IDs of Country - @type(array)
   * @responseBody 200 - <ZnStore[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null).paginated() - Read all stores descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  async list({ request, response }: HttpContext) {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        ownerIds,
        countryIds,
        stateIds,
        cityIds,
        sortBy = 'default',
        verified,
      } = request.qs()

      const query = Store.query()
        .preload('logo')
        .preload('user')
        .preload('medias')
        .preload('thumbnail')
        .preload('nailSystems')
        .withCount('posts', (query) => {
          query.where('isDraft', 0).whereNull('deletedAt')
        })

      if (
        ownerIds &&
        ownerIds.length > 0 &&
        ownerIds.filter((item: any) => item !== '').length > 0
      ) {
        const ownerIdsArray = Array.isArray(ownerIds) ? ownerIds : [ownerIds]
        query.whereIn('userId', ownerIdsArray)
      }

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder
            .whereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(address) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(phoneNumber) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(website) LIKE LOWER(?)', [`%${search}%`])
        })
      }

      if (countryIds) {
        let ids = [countryIds]
        if (Array.isArray(countryIds)) {
          ids = countryIds
        }
        query.whereIn('countryId', ids)
      }

      if (stateIds) {
        let ids = [stateIds]
        if (Array.isArray(stateIds)) {
          ids = stateIds
        }
        query.whereIn('stateId', ids)
      }

      if (cityIds) {
        let ids = [cityIds]
        if (Array.isArray(cityIds)) {
          ids = cityIds
        }
        query.whereIn('cityId', ids)
      }

      if (verified !== undefined && verified !== '') {
        query.where({ verified })
      }

      if (sortBy === 'default') {
        query.orderBy('createdAt', 'desc')
      }

      const result = await query.paginate(page, limit)

      return response.ok(result.serialize())
    } catch (error) {
      return response.internalServerError(error)
    }
  }

  /**
   * @filter
   * @tag Admin Store
   */
  async filter({ request, response }: HttpContext) {
    const { page = 1, limit = 10 } = request.qs()

    const data = request.all()
    const payload = await filterStoreValidator.validate(data)

    try {
      const query = Store.query()
        .preload('logo')
        .preload('user')
        .preload('medias')
        .preload('thumbnail')
        .preload('nailSystems')
        .withCount('posts', (query) => {
          query.where('isDraft', 0).whereNull('deletedAt')
        })

      if (payload.searchAdvance) {
        query.where((queryBuilder) => {
          queryBuilder
            .whereRaw('LOWER(name) LIKE LOWER(?)', [`%${payload.searchAdvance}%`])
            .orWhereRaw('LOWER(address) LIKE LOWER(?)', [`%${payload.searchAdvance}%`])
            .orWhereRaw('LOWER(phoneNumber) LIKE LOWER(?)', [`%${payload.searchAdvance}%`])
            .orWhereRaw('LOWER(website) LIKE LOWER(?)', [`%${payload.searchAdvance}%`])
        })
      }

      // if (payload.filter) {
      //   if (
      //     payload.filter.ownerIds &&
      //     payload.filter.ownerIds.length > 0
      //   ) {
      //     // const ownerIdsArray = Array.isArray(ownerIds) ? ownerIds : [ownerIds]
      //     query.whereIn('userId', payload.filter.ownerIds)
      //   }
      // }

      if (payload.sort) {
        query.orderBy(payload.sort.field, payload.sort.direction)
      } else {
        query.orderBy('createdAt', 'desc')
      }

      const result = await query.paginate(page, limit)

      return response.ok(result.serialize())
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @filterByName
   * @tag Admin Store
   */
  async filterByName({ request, response }: HttpContext) {
    const { page = 1, search } = request.qs()

    try {
      const query = Store.query()

      if (search) {
        query.whereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
      }

      const result = await query.paginate(page, 10)

      return response.ok(result.serialize())
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @show
   * @tag Admin Store
   */
  async show({ params, response }: HttpContext) {
    try {
      const storeId = params.id

      const store = await Store.query()
        .preload('logo')
        .preload('user')
        .preload('thumbnail')
        .preload('medias')
        .preload('nailSystems')
        .withCount('posts', (query) => {
          query.where('isDraft', 0).whereNull('deletedAt')
        })
        .where('id', storeId)
        .first()

      return response.ok({
        ...store?.serialize(),
      })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @create
   * @tag Admin Store
   * @summary Return info for creation
   */
  /**
   * Display form to create a new record
   */
  async getDataToCreate({ response }: HttpContext) {
    try {
      // const users = await ZnUser.query()
      //   .select('id', 'firstName', 'lastName', 'phone', 'email')
      //   .whereNull('deletedAt')

      return response.ok({
        options: {
          // users: users,
        },
      })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @create
   * @tag Admin Store
   */
  async create({ request, response }: HttpContext) {
    const data = request.all()

    try {
      const payload = await createStoreValidator().validate(data)

      let workingHour = payload.workingHour
      if (typeof workingHour === 'object') {
        workingHour = JSON.stringify(workingHour) as any
      }

      let countryId, stateId, cityId
      if (payload.placeDetails) {
        const country = await ZnCountry.updateOrCreate(
          { name: payload.placeDetails.country },
          { name: payload.placeDetails.country }
        )
        countryId = country.id

        if (payload.placeDetails.state) {
          const state = await ZnState.updateOrCreate(
            { name: payload.placeDetails.state, countryId: country.id },
            { name: payload.placeDetails.state, countryId: country.id }
          )
          stateId = state.id
        }

        if (payload.placeDetails?.city && stateId) {
          const city = await ZnCity.updateOrCreate(
            { name: payload.placeDetails.city, stateId: stateId },
            { name: payload.placeDetails.city, stateId: stateId }
          )
          cityId = city.id
        }
      }

      const created = await Store.create({
        userId: payload.userId,
        name: payload.name,
        latitude: payload.latitude,
        longitude: payload.longitude,
        address: payload.address,
        phoneNumber: payload.phoneNumber,
        website: payload.website,
        workingHour: workingHour,
        thumbnailId: payload.thumbnailId ?? undefined,
        logoId: payload.logoId ?? undefined,
        zipCode: payload.zipCode,
        socials: payload.socials,
        timezone: payload.timezone,
        createdByAdminId: payload.createdByAdminId,
        placeId: payload.placeId,
        countryId,
        stateId,
        cityId,
      })

      if (payload.mediaIds || payload.mediaLinks) {
        let mediaIds: string[] = []

        if (payload.mediaIds && payload.mediaIds.length > 0) {
          mediaIds = mediaIds.concat(payload.mediaIds)
        }

        if (payload.mediaLinks && payload.mediaLinks.length > 0) {
          const mediasFromLinks = await uploadMediasFromLinks(payload.mediaLinks, MEDIA_TYPE.IMAGE)
          mediaIds = mediaIds.concat(mediasFromLinks.map((media) => media.id))
        }

        await created.related('medias').sync(mediaIds)
      }

      if (payload.nailSystemIds && payload.nailSystemIds.length > 0) {
        await created.related('nailSystems').sync(payload.nailSystemIds)
      }

      return response.created(created)
    } catch (error) {
      console.log('Error:', error) // This will print out more detailed error information
      if (error.messages) {
        return response.badRequest({
          message: 'Validation failed',
          errors: error.messages,
        })
      }
      return response.internalServerError({
        message: error.message || 'Something went wrong',
      })
    }
  }

  /**
   * @update
   * @tag Admin Store
   */
  async update({ request, response, params }: HttpContext) {
    const storeId = params.id

    const store = await Store.query().where('id', storeId).first()

    if (!store) {
      return response.notFound({ message: 'Store not found' })
    }

    try {
      const data = request.all()

      const payload = await createStoreValidator(store.id).validate(data)

      store.name = payload.name
      store.latitude = payload.latitude || store.latitude
      store.longitude = payload.longitude || store.longitude
      store.address = payload.address
      store.website = payload.website || store.website
      store.phoneNumber = payload.phoneNumber || store.phoneNumber
      store.workingHour = payload.workingHour || store.workingHour
      store.thumbnailId = payload.thumbnailId || store.thumbnailId
      store.logoId = payload.logoId || store.logoId
      store.zipCode = payload.zipCode || store.zipCode
      store.socials = payload.socials || store.socials
      store.updatedAt = DateTime.local()
      store.placeId = payload.placeId || store.placeId
      store.userId = payload.userId || store.userId
      store.timezone = payload.timezone || store.timezone

      if (payload.mediaIds || payload.mediaLinks) {
        let mediaIds: string[] = []

        if (payload.mediaIds && payload.mediaIds.length > 0) {
          mediaIds = mediaIds.concat(payload.mediaIds)
        }

        if (payload.mediaLinks && payload.mediaLinks.length > 0) {
          const mediasFromLinks = await uploadMediasFromLinks(payload.mediaLinks, MEDIA_TYPE.IMAGE)
          mediaIds = mediaIds.concat(mediasFromLinks.map((media) => media.id))
        }
        await store.related('medias').sync(mediaIds)
      }

      if (payload.nailSystemIds && payload.nailSystemIds.length > 0) {
        await store.related('nailSystems').sync(payload.nailSystemIds)
      }

      if (payload.placeDetails) {
        const country = await ZnCountry.updateOrCreate(
          { name: payload.placeDetails.country },
          { name: payload.placeDetails.country }
        )
        store.countryId = country.id

        if (payload.placeDetails.state) {
          const state = await ZnState.updateOrCreate(
            { name: payload.placeDetails.state, countryId: country.id },
            { name: payload.placeDetails.state, countryId: country.id }
          )
          store.stateId = state.id
        }

        if (payload.placeDetails?.city && store.stateId) {
          const city = await ZnCity.updateOrCreate(
            { name: payload.placeDetails.city, stateId: store.stateId },
            { name: payload.placeDetails.city, stateId: store.stateId }
          )
          store.cityId = city.id
        }
      }

      const updated = await store.save()

      return response.ok(updated)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @softDelete
   * @tag Admin Store
   */
  async softDelete({ params, response }: HttpContext) {
    const storeId = params.id

    const store = await Store.query().where('id', storeId).first()

    if (!store) {
      return response.notFound({ message: 'Store not found' })
    }

    try {
      await store.softDelete()

      return response.ok({ message: 'Store soft-deleted successfully' })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  async getDetailsByPlaceId({ params, response }: HttpContext) {
    try {
      const placeId = params.placeId

      const googlePlaceService = new GooglePlaceService()

      const result = await googlePlaceService.getPlaceDetails(placeId)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @export
   * @tag Admin Store
   * @summary Export all stores
   * @paramQuery search - Search Term - @type(string)
   * @paramQuery ownerIds - IDs of Owners (Users) - @type(array)
   * @paramQuery countryIds - IDs of Country - @type(array) @type(string)
   * @paramQuery stateIds - IDs of Country - @type(array)
   * @paramQuery cityIds - IDs of Country - @type(array)
   */
  async export({ request, response }: HttpContext) {
    try {
      const {
        search,
        ownerIds,
        countryIds,
        stateIds,
        cityIds,
        sortBy = 'default'
      } = request.qs()

      const query = Store.query()
        .preload('user')
        .preload('state')
        .preload('city')

      if (
        ownerIds &&
        ownerIds.length > 0 &&
        ownerIds.filter((item: any) => item !== '').length > 0
      ) {
        const ownerIdsArray = Array.isArray(ownerIds) ? ownerIds : [ownerIds]
        query.whereIn('userId', ownerIdsArray)
      }

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder
            .whereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(address) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(phoneNumber) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(website) LIKE LOWER(?)', [`%${search}%`])
        })
      }

      if (countryIds) {
        let ids = [countryIds]
        if (Array.isArray(countryIds)) {
          ids = countryIds
        }
        query.whereIn('countryId', ids)
      }

      if (stateIds) {
        let ids = [stateIds]
        if (Array.isArray(stateIds)) {
          ids = stateIds
        }
        query.whereIn('stateId', ids)
      }

      if (cityIds) {
        let ids = [cityIds]
        if (Array.isArray(cityIds)) {
          ids = cityIds
        }
        query.whereIn('cityId', ids)
      }


      if (sortBy === 'default') {
        query.orderBy('createdAt', 'desc')
      }

      const report = await query

      const workbook = new ExcelJS.Workbook()
      const worksheet = workbook.addWorksheet('Store Report')

      worksheet.addRow([
        "Name",
        "Phone Number",
        "Email",
        "State",
        "City",
        "Address",
      ])

      for (const record of report) {
        worksheet.addRow([
          record.name,
          record.phoneNumber,
          record.user?.email,
          record.state?.name,
          record.city?.name,
          record.address,
        ])
      }

      const buffer = (await workbook.xlsx.writeBuffer()) as unknown as Buffer

      const reports = await uploadReports([{ buffer: buffer }])

      return response.ok({
        filename: `Store Report.xlsx`,
        url: reports[0].url,
      })

    } catch (error) {
      return response.internalServerError(error)
    }
  }
}
