import ZnClaimStore, { EClaimStore } from '#models/zn_claim_store'
import { HttpContext } from '@adonisjs/core/http'
import {
  approveClaimStoreValidator,
  rejectClaimStoreValidator,
} from '../../validators/store/store_validator.js'
import ZnStore from '#models/zn_store'
import mail from '@adonisjs/mail/services/main'
import Notification from '#models/notification'
import queue from '@rlanz/bull-queue/services/main'
import SendNotificationJob from '#jobs/send_notification_job'
import { NOTIFICATION_TYPE } from '../../../app/constants/notification.js'
import RejectClaimStoreNotification from '#mails/claim-store/reject_claim_store_notification'
import env from '#start/env'
import { StoreService } from '#services/store_service'

export default class AdminClaimStoreController {
  private storeService: StoreService
  constructor() {
    this.storeService = new StoreService()
  }

  /**
   * @list
   * @tag Admin Claim Store
   * @summary Read all claim store request
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @responseBody 200 - <ZnClaimStore[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null).paginated() - Read all cliam store descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  async list({ request, response }: HttpContext) {
    try {
      const { page = 1, limit = 10, search, sortBy = 'default' } = request.qs()

      const query = ZnClaimStore.query()
        .whereHas('user', (userQuery) => {
          userQuery.whereNull('deletedAt')
        })
        .whereHas('store', (storeQuery) => {
          storeQuery.whereNull('deletedAt')
        })
        .preload('user')
        .preload('store', (storeQuery) => {
          storeQuery.preload('country').preload('state').preload('city')
        })
        .whereNull('deletedAt')
        .where('type', 'email')

      if (search) {
        query.whereHas('store', (queryBuilder) => {
          queryBuilder
            .whereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(address) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(phoneNumber) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(website) LIKE LOWER(?)', [`%${search}%`])
        })

        query.orWhereHas('user', (queryBuilder) => {
          queryBuilder
            .whereRaw('LOWER(email) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(firstName) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(lastName) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(phone) LIKE LOWER(?)', [`%${search}%`])
        })
      }

      if (sortBy === 'default') {
        query.orderBy('createdAt', 'desc')
      }

      const result = await query.paginate(page, limit)

      return response.ok(result.serialize())
    } catch (error) {
      console.log(error)
      return response.badRequest(error)
    }
  }

  /**
   * @approveClaimStore
   * @tag Admin Claim Store
   */
  async approveClaimStore({ request, response }: HttpContext) {
    try {
      const data = request.all()

      const payload = await approveClaimStoreValidator.validate(data)

      const claimStoreRequest = await ZnClaimStore.query()
        .where('id', payload.requestId)
        .preload('store')
        .preload('user')
        .first()

      await ZnStore.updateOrCreate(
        { id: claimStoreRequest?.storeId },
        { verified: true, userId: claimStoreRequest?.userId }
      )

      await ZnClaimStore.updateOrCreate(
        { id: claimStoreRequest?.id },
        { status: EClaimStore.APPROVE, note: payload.note }
      )

      if (claimStoreRequest?.user && claimStoreRequest?.store) {
        await this.storeService.sendApprovalNotification(
          claimStoreRequest?.user,
          claimStoreRequest?.store
        )
      }

      return response.ok(await ZnClaimStore.query().where('id', payload.requestId).first())
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @rejectClaimStore
   * @tag Admin Claim Store
   */
  async rejectClaimStore({ request, response }: HttpContext) {
    try {
      const data = request.all()

      const payload = await rejectClaimStoreValidator.validate(data)

      const claimStoreRequest = await ZnClaimStore.query()
        .where('id', payload.requestId)
        .preload('store')
        .preload('user')
        .first()

      await ZnClaimStore.updateOrCreate(
        { id: claimStoreRequest?.id },
        { status: EClaimStore.REJECT, note: payload.note }
      )

      const serverDomain = env.get('BASE_URL') || ''

      if (claimStoreRequest?.user)
        await mail.sendLater(
          new RejectClaimStoreNotification(
            claimStoreRequest?.user?.email,
            `${claimStoreRequest?.user.firstName} ${claimStoreRequest?.user.lastName}`,
            claimStoreRequest?.store?.name,
            claimStoreRequest?.store?.latitude,
            claimStoreRequest?.store?.longitude,
            claimStoreRequest?.store?.address,
            claimStoreRequest?.store?.phoneNumber,
            claimStoreRequest?.store?.website,
            claimStoreRequest?.store?.country?.name,
            claimStoreRequest?.store?.state?.name,
            claimStoreRequest?.store?.city?.name,
            payload.note,
            serverDomain
          )
        )

      const notificationDto = {
        title: 'Reject Claim Store Request',
        description: `Your request to claim ownership of store ${claimStoreRequest?.store?.name} has been rejected!`,
        type: NOTIFICATION_TYPE.CLAIM_STORE,
        resourceId: claimStoreRequest?.store?.id,
        actorId: claimStoreRequest?.user?.id,
        actorType: 'user',
      }

      const notification = await Notification.create({
        ...notificationDto,
        userId: claimStoreRequest?.user?.id,
      })

      if (claimStoreRequest?.user)
        await queue.dispatch(SendNotificationJob, {
          users: [claimStoreRequest?.user as any],
          notifications: [notification],
        })

      return response.ok(await ZnClaimStore.query().where('id', payload.requestId).first())
    } catch (error) {
      return response.badRequest(error)
    }
  }
}
