import { ACTION, RESOURCE } from '#constants/authorization'
import ZnCollection from '#models/zn_collection'
import ZnProductCategory from '#models/zn_product_category'
import ZnStoreSetting from '#models/zn_store_setting'
import { HttpContext } from '@adonisjs/core/http'
import db from '@adonisjs/lucid/services/db'
import { getMediaUrl } from '../../../services/commons.js'
import {
  createStoreSettingValidator,
  updateStoreSettingValidator,
} from '../../validators/store/store_setting_validator.js'

export default class AdminStoreSettingController {
  /**
   * @list
   * @tag Admin Store Setting Category
   */
  async listCategory({ request, response }: HttpContext) {
    try {
      const { page = 1, limit = 10, search } = request.qs()
      const query = db
        .from('zn_store_settings as ss')
        .whereNull('ss.deletedAt')
        .where('ss.sourceName', ZnProductCategory.table)
        .rightJoin('zn_product_categories as pc', 'pc.id', 'ss.sourceId')
        .leftJoin('zn_medias as media', 'media.id', 'pc.imageId')
        .select(
          'pc.name as name',
          'pc.imageId as imageId',
          'ss.orderBy as orderBy',
          'ss.sourceId as sourceId',
          'ss.sourceName as sourceName',
          'media.url as imageUrl',
          'media.fileKey as fileKey',
          'ss.id as id'
        )

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder.whereRaw('LOWER(pc.name) LIKE LOWER(?)', [`%${search}%`])
        })
      }

      const result = await query.orderBy('ss.orderBy', 'asc').paginate(page, limit)

      return response.ok({
        data: result.toJSON().data.map((item) => {
          let imageUrl = item.fileKey
          if (imageUrl) {
            imageUrl = getMediaUrl(imageUrl)
          }
          return {
            ...item,
            imageUrl,
          }
        }),
        meta: result.getMeta(),
      })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @list
   * @tag Admin Store Setting Collection
   */
  async listCollection({ request, response }: HttpContext) {
    try {
      const { page = 1, limit = 10, search } = request.qs()
      const query = db
        .from('zn_store_settings as ss')
        .where('ss.sourceName', ZnCollection.table)
        .whereNull('ss.deletedAt')
        .rightJoin('zn_collections as c', 'c.id', 'ss.sourceId')
        .select(
          'c.title as title',
          'c.imageUrl as imageUrl',
          'ss.orderBy',
          'ss.sourceId as sourceId',
          'ss.sourceName as sourceName',
          'ss.id as id'
        )

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder.whereRaw('LOWER(c.title) LIKE LOWER(?)', [`%${search}%`])
        })
      }

      const result = await query.orderBy('ss.orderBy', 'asc').paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @create
   * @tag Admin Store Setting Category
   */
  async createCategory({ request, response, bouncer }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.SHOP_CATEGORY)
    try {
      const payload = await request.validateUsing(
        createStoreSettingValidator(ZnProductCategory.table)
      )
      let orderBy = payload.orderBy
      if (!orderBy) {
        const maxOrderResult = await ZnStoreSetting.query()
          .max('orderBy as maxOrderBy') // Use the actual DB column name here
          .first()
        orderBy = (maxOrderResult?.$extras?.maxOrderBy || 1) + 1
      }

      const created = await ZnStoreSetting.create({
        sourceId: payload.sourceId,
        sourceName: ZnProductCategory.table,
        orderBy,
      })

      return response.created(created)
    } catch (error) {
      console.log('Error:', error) // This will print out more detailed error information
      if (error.messages) {
        return response.badRequest({
          message: 'Validation failed',
          errors: error.messages,
        })
      }
      return response.internalServerError({
        message: error.message || 'Something went wrong',
      })
    }
  }

  /**
   * @create
   * @tag Admin Store Setting Collection
   */
  async createCollection({ request, response, bouncer }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.SHOP_COLLECTION)
    try {
      const payload = await request.validateUsing(createStoreSettingValidator(ZnCollection.table))

      let orderBy = payload.orderBy
      if (!orderBy) {
        const maxOrderResult = await ZnStoreSetting.query()
          .max('orderBy as maxOrderBy') // Use the actual DB column name here
          .first()
        orderBy = (maxOrderResult?.$extras?.maxOrderBy || 1) + 1
      }
      const created = await ZnStoreSetting.create({
        sourceId: payload.sourceId,
        sourceName: ZnCollection.table,
        orderBy,
      })

      return response.created(created)
    } catch (error) {
      if (error.messages) {
        return response.badRequest({
          message: 'Validation failed',
          errors: error.messages,
        })
      }
      return response.internalServerError({
        message: error.message || 'Something went wrong',
      })
    }
  }

  /**
   * @softDelete
   * @tag Admin Store Setting
   */
  async softDelete({ params, response }: HttpContext) {
    const id = params.id

    const store = await ZnStoreSetting.find(id)

    if (!store) {
      return response.notFound({ message: 'Store setting not found' })
    }

    try {
      await store.softDelete()

      return response.ok({ message: 'Store setting soft-deleted successfully' })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @softDelete
   * @tag Admin Store Setting
   */
  async update({ params, response, request }: HttpContext) {
    const id = params.id

    const store = await ZnStoreSetting.find(id)

    if (!store) {
      return response.notFound({ message: 'Store setting not found' })
    }

    try {
      const payload = await request.validateUsing(updateStoreSettingValidator())
      await store.merge({ orderBy: payload.orderBy }).save()

      return response.ok({ message: 'Store setting soft-deleted successfully' })
    } catch (error) {
      return response.badRequest(error)
    }
  }
}
