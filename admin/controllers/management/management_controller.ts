import ZnAdmin from '#models/zn_admin'
import ZnRole from '#models/zn_role'
import AuthorizationService from '#services/authorization_service'
import {
  registerValidator,
  updateActiveValidator,
  updateAdminValidator,
  updatePasswordValidator,
  updateSuperAdminValidator,
} from '#validators/admin'
import { HttpContext } from '@adonisjs/core/http'
import hash from '@adonisjs/core/services/hash'
import { ACTION, RESOURCE } from '../../../app/constants/authorization.js'

export default class AdminManagementController {
  /**
   * @index
   * @tag Admin Management
   * @summary Read all admins
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @responseBody 200 - <ZnAdmin[]>.append("id":"").paginated() - Read all admins descriptively
   */
  /**
   * Display a list of resource
   */
  async index({
    // auth,
    bouncer,
    request,
    response,
  }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.ADMIN)

    const { page = 1, limit = 10, search } = request.qs()

    const query = ZnAdmin.query().preload('avatar').preload('roles')

    // @ts-ignore
    // if (!auth.getUserOrFail()?.isSuperAdmin) {
    //   query.where('isSuperAdmin', false)
    // }

    if (search) {
      query.where((queryBuilder) => {
        queryBuilder
          .where('username', 'LIKE', `%${search}%`)
          .orWhere('name', 'LIKE', `%${search}%`)
          .orWhere('phone', 'LIKE', `%${search}%`)
      })
    }

    const result = await query.paginate(page, limit)

    return response.ok(result)
  }

  async select({ request, response }: HttpContext) {
    const { page = 1, search, filter } = request.qs()
    try {
      const query = ZnAdmin.query()

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder
            .whereRaw('LOWER(username) LIKE LOWER(?)', [`%${search}%`])
            .orWhereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
        })
      }

      if (filter) {
        query.whereNotIn('id', filter)
      }

      const result = await query.paginate(page, 5)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @create
   * @tag Admin Management
   * @summary Return info for creation
   * @responseBody 200 - {"options":{"roles":["ZnRole"]}} - Return info for creation descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display form to create a new record
   */
  async create({ bouncer, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.ADMIN)

    try {
      const roles = await ZnRole.query().whereNull('deletedAt')

      return response.ok({
        info: {
          roles: roles,
        },
      })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @store
   * @tag Admin Management
   * @summary Create action
   * @requestBody <ZnAdmin>.append("password":"adminPassword","roleIds":[""])
   * @responseBody 201 - <ZnAdmin>.append("role":"admin","id":"","createdAt":"","updatedAt":"") - Create action descriptively
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The username field must be defined","rule":"required","field":"username"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Handle form submission for the create action
   */
  async store({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.ADMIN)

    const data = request.all()

    const payload = await registerValidator.validate(data)

    try {
      const hashedpassword = await hash.make(payload.password)

      const created = await ZnAdmin.create({
        username: payload.username,
        password: hashedpassword,
        name: payload.name ?? undefined,
        phone: payload.phone ?? undefined,
        avatarId: payload.avatarId ?? undefined,
        isSuperAdmin: false,
        isActive: true,
        countries: payload.countries?.join(',') || null,
      })

      if (payload.roleIds && payload.roleIds.length > 0) {
        await created.related('roles').sync(payload.roleIds)
      }

      return response.created(created)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @show
   * @tag Admin Management
   * @summary Read an admin
   * @paramPath id - ID of Admin - @type(string) @required
   * @responseBody 200 - <ZnAdmin>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read an admin descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Admin not found"} - Not Found
   */
  /**
   * Show individual record
   */
  async show({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.ADMIN)

    const adminId = params.id

    const admin = await ZnAdmin.query()
      // .where('isSuperAdmin', false)
      .preload('avatar')
      .preload('roles')
      .where('id', adminId)
      .first()

    if (!admin) {
      return response.notFound({ message: 'Admin not found' })
    }

    return response.ok(admin)
  }

  /**
   * @showPermissions
   * @tag Admin Management
   * @summary Read an admin permissions
   * @paramPath id - ID of Admin - @type(string) @required
   * @responseBody 200 - <ZnAdmin>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read an admin descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Admin not found"} - Not Found
   */
  /**
   * Show individual record
   */
  async showPermissions({ params, response }: HttpContext) {
    const adminId = params.id

    const admin = await ZnAdmin.query().preload('roles').where('id', adminId).first()

    if (!admin) {
      return response.notFound({ message: 'Admin not found' })
    }

    const permissions = await AuthorizationService.getPermissionsFromAdmin(admin)

    return response.ok(permissions)
  }

  /**
   * @edit
   * @tag Admin Management
   * @summary Return info for updating
   * @responseBody 200 - {"data":"<ZnAdmin>","options":{"roles":["ZnRole"]}} - Return info for updating descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Admin not found"} - Not Found
   */
  /**
   * Edit individual record
   */
  async edit({ auth, bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.ADMIN, { children: true })

    const adminId = params.id

    const admin = await ZnAdmin.query()
      // .where('isSuperAdmin', false)
      .preload('avatar')
      .preload('roles')
      .where('id', adminId)
      .first()

    if (!admin) {
      return response.notFound({ message: 'Admin not found' })
    }

    // @ts-ignore
    if (admin.id != auth.getUserOrFail()?.id) {
      await bouncer.authorize('deny', ACTION.UPDATE_SELF, RESOURCE.ADMIN)
    }

    try {
      const roles = await ZnRole.query().select('id', 'name').whereNull('deletedAt')

      return response.ok({
        data: {
          ...admin?.serialize(),
        },
        info: {
          roles: roles,
        },
      })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @update
   * @tag Admin Management
   * @summary Update admin info
   * @description Update admin info descriptively
   * @paramPath id - ID of Admin - @type(string) @required
   * @requestBody <ZnAdmin>.append("roleIds":[""])
   * @responseBody 200 - <ZnAdmin>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The name field must be defined","rule":"required","field":"name"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Admin not found"} - Not Found
   */
  /**
   * Handle form submission for the edit action
   */
  async update({ auth, bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.ADMIN, { children: true })

    const adminId = params.id

    const admin = await ZnAdmin.query().where('id', adminId).first()

    if (!admin) {
      return response.notFound({ message: 'Admin not found' })
    }

    // @ts-ignore
    if ((admin.username == process.env.ZURNO_ADMIN_USER && auth.getUserOrFail()?.username != process.env.ZURNO_ADMIN_USER) ||
      // @ts-ignore
      (admin.isSuperAdmin && !auth.getUserOrFail()?.isSuperAdmin)
    ) {
      return response.forbidden({ message: 'Access denied' })
    }

    // @ts-ignore
    if (admin.id != auth.getUserOrFail()?.id) {
      await bouncer.authorize('deny', ACTION.UPDATE_SELF, RESOURCE.ADMIN)
    }

    const data = request.all()

    const payload = await updateAdminValidator(adminId).validate(data)

    // const countries = payload.countries?.join(',') || null
    // if (admin.countries != countries) {
    //   await bouncer.authorize('allow', null, null)
    // }

    try {
      admin.username = payload.username || admin.username
      admin.name = payload.name || admin.name
      admin.phone = payload.phone || admin.phone
      admin.avatarId = payload.avatarId || admin.avatarId

      admin.countries = payload.countries?.join(',') || null

      if (payload.password) {
        const hashedpassword = await hash.make(payload.password)
        admin.password = hashedpassword
      }

      if (Array.isArray(payload.roleIds)) {
        await bouncer.authorize('deny', ACTION.UPDATE_SELF, RESOURCE.ADMIN)

        await admin.related('roles').sync(payload.roleIds)
      }

      const updated = await admin.save()

      return response.ok(updated)
    } catch (error) {
      console.log(error)

      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @updatePassword
   * @tag Admin Management
   * @summary Update admin password
   * @description Update admin password descriptively
   * @paramPath id - ID of Admin - @type(string) @required
   * @requestBody <ZnAdmin>.only(password)
   * @responseBody 200 - {"message":"Password updated successfully"} - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The password field must be defined","rule":"required","field":"password"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Admin not found"} - Not Found
}
   */
  public async updatePassword({ auth, bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.ADMIN, { children: true })

    const adminId = params.id

    const admin = await ZnAdmin.query().where('id', adminId).first()

    if (!admin) {
      return response.notFound({ message: 'Admin not found' })
    }

    // @ts-ignore
    if ((admin.username == process.env.ZURNO_ADMIN_USER && auth.getUserOrFail()?.username != process.env.ZURNO_ADMIN_USER) ||
      // @ts-ignore
      (admin.isSuperAdmin && !auth.getUserOrFail()?.isSuperAdmin)
    ) {
      return response.forbidden({ message: 'Access denied' })
    }

    // @ts-ignore
    if (admin.id != auth.getUserOrFail()?.id) {
      await bouncer.authorize('deny', ACTION.UPDATE_SELF, RESOURCE.ADMIN)
    }

    const data = request.all()

    const payload = await updatePasswordValidator.validate(data)

    try {
      const hashedpassword = await hash.make(payload.password)

      admin.password = hashedpassword

      await admin.save()

      return response.ok({ message: 'Password updated successfully' })
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @updateSuperAdmin
   * @tag Admin Management
   * @summary Update super admin
   * @description Update super admin descriptively
   * @paramPath id - ID of Admin - @type(string) @required
   * @requestBody <ZnAdmin>.only(isSuperAdmin)
   * @responseBody 200 - {"message":"SuperAdmin updated successfully"} - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The isSuperAdmin field must be defined","rule":"required","field":"isSuperAdmin"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Admin not found"} - Not Found
}
   */
  public async updateSuperAdmin({ auth, bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', null, null)

    const adminId = params.id

    const admin = await ZnAdmin.query().where('id', adminId).first()

    // @ts-ignore
    if (auth.getUserOrFail()?.id == adminId) {
      return response.forbidden({ message: 'Cannot change your own Super Admin status' })
    }

    if (!admin) {
      return response.notFound({ message: 'Admin not found' })
    }

    // @ts-ignore
    if (admin.username == process.env.ZURNO_ADMIN_USER || (admin.isSuperAdmin && !auth.getUserOrFail()?.isSuperAdmin)
    ) {
      return response.forbidden({ message: 'Access denied' })
    }

    const data = request.all()

    const payload = await updateSuperAdminValidator.validate(data)

    try {
      admin.isSuperAdmin =
        payload.isSuperAdmin !== undefined && payload.isSuperAdmin !== null
          ? payload.isSuperAdmin
          : (admin.isSuperAdmin as any)

      await admin.save()

      return response.ok({ message: 'Super Admin updated successfully' })
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @updateActivity
   * @tag Admin Management
   * @summary Update super admin activity
   * @description Update super admin activity descriptively
   * @paramPath id - ID of Admin - @type(string) @required
   * @requestBody <ZnAdmin>.only(isActive)
   * @responseBody 200 - {"message":"Activity updated successfully"} - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The isActive field must be defined","rule":"required","field":"isActive"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Admin not found"} - Not Found
}
   */

  public async updateActivity({ auth, bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.ADMIN)

    const adminId = params.id

    // @ts-ignore
    if (auth.getUserOrFail()?.id == adminId) {
      return response.badRequest({ message: 'Cannot change your own activity status' })
    }

    const admin = await ZnAdmin.query().where('id', adminId).first()

    if (!admin) {
      return response.notFound({ message: 'Admin not found' })
    }

    // @ts-ignore
    if (admin.username == process.env.ZURNO_ADMIN_USER || (admin.isSuperAdmin && !auth.getUserOrFail()?.isSuperAdmin)
    ) {
      return response.forbidden({ message: 'Access denied' })
    }

    const data = request.all()

    const payload = await updateActiveValidator.validate(data)

    try {
      admin.isActive =
        payload.isActive !== undefined && payload.isActive !== null
          ? payload.isActive
          : (admin.isActive as any)

      await admin.save()

      return response.ok({ message: 'Activity updated successfully' })
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @destroy
   * @tag Admin Management
   * @summary Soft-delete an admin
   * @description Soft-delete an admin descriptively
   * @paramPath id - ID of Admin - @type(string) @required
   * @responseBody 200 - {"message":"Admin soft-deleted successfully"} - OK
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Admin not found"} - Not Found
   */
  /**
   * Delete record
   */
  async destroy({ auth, bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.ADMIN)

    const adminId = params.id

    // @ts-ignore
    if (auth.getUserOrFail()?.id == adminId) {
      return response.badRequest({ message: 'Cannot delete yourself' })
    }

    const admin = await ZnAdmin.find(adminId)

    if (!admin) {
      return response.notFound({ message: 'Admin not found' })
    }

    // @ts-ignore
    if (admin.username == process.env.ZURNO_ADMIN_USER || (admin.isSuperAdmin && !auth.getUserOrFail()?.isSuperAdmin)
    ) {
      return response.forbidden({ message: 'Access denied' })
    }

    await admin.softDelete()

    return response.ok({ message: 'Admin soft-deleted successfully' })
  }
}
