import ZnProductVariant from '#models/zn_product_variant'
import { HttpContext } from '@adonisjs/core/http'

export default class AdminVariantController {
  /**
   * @index
   * @tag Admin Variant
   * @summary Read all variants
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @responseBody 200 - <ZnProductVariant[]>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null).paginated() - Read all produtcs descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display a list of resource
   */
  async index({ request, response }: HttpContext) {
    try {
      const { page = 1, limit = 10, search, filter } = request.qs()

      const query = ZnProductVariant.query().whereNull('deletedAt')

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder.whereRaw('LOWER(title) LIKE LOWER(?)', [`%${search}%`])
        })
      }

      if (filter) {
        query.where((queryBuilder) => {
          filter.map((fil: string) =>
            queryBuilder.orWhereRaw(`LOWER(${fil.split('=')[0]}) LIKE LOWER(?)`, [
              `%${fil.split('=')[1]}%`,
            ])
          )
        })
      }

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  // /**
  //  * @show
  //  * @tag Admin Variant
  //  * @summary Read a variant
  //  * @paramPath id - ID of Variant - @type(string) @required
  //  * @responseBody 200 - <ZnVariant>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read a variant descriptively
  //  * @responseBody 401 - Unauthorized access - Unauthorized
  //  * @responseBody 404 - {"message":"Variant not found"} - Not Found
  //  */
  // /**
  //  * Show individual record
  //  */
  // public async show({ bouncer, params, response }: HttpContext) {
  //   await bouncer.authorize('allow', ACTION.READ, RESOURCE.PRODUCT)

  //   try {
  //     const variantId = params.id

  //     const variant = await ZnVariant.query()
  //       .preload('images')
  //       .where('id', variantId)
  //       .first()

  //     if (!variant) { response.notFound({ message: 'Variant not found' }) }

  //     return response.ok(variant)

  //   } catch (error) {
  //     return response.badRequest(error)
  //   }
  // }

  // /**
  //  * @create
  //  * @tag Admin Variant
  //  * @summary Return data to create a variant
  //  */
  // /**
  //  * Display form to create a new record
  //  */
  // async create({ }: HttpContext) { }

  // /**
  //  * @store
  //  * @tag Admin Variant
  //  * @summary Create a variant
  //  * @requestBody <ZnVariant>
  //  * @responseBody 201 - <ZnVariant>.append("id":"","createdAt":"","updatedAt":"")
  //  * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The title field must be defined","rule":"required","field":"title"}]}
  //  * @responseBody 401 - Unauthorized access
  //  */
  // /**
  //  * Handle form submission for the create action
  //  */
  // public async store({ bouncer, request, response }: HttpContext) {
  //   await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.PRODUCT)

  //   const data = request.all()

  //   const payload = await createVariantValidator.validate(data)

  //   try {
  //     const created = await ZnVariant.create({
  //       // name: payload.name,
  //     })

  //     return response.created(created)

  //   } catch (error) {
  //     return response.internalServerError({
  //       message: 'Something went wrong',
  //     })
  //   }
  // }

  // /**
  //  * @edit
  //  * @tag Admin Variant
  //  * @summary Return data to update a variant
  //  */
  // /**
  //  * Edit individual record
  //  */
  // async edit({ bouncer, params, response }: HttpContext) {
  //   await bouncer.authorize('allow', ACTION.READ, RESOURCE.PRODUCT)

  //   try {
  //     const variantId = params.id

  //     const variant = await ZnVariant.query()
  //       .preload('images')
  //       .preload('collections')
  //       .preload('options')
  //       .preload('variants')
  //       .where('id', variantId)
  //       .first()

  //     return response.ok({
  //       data: {
  //         ...variant?.serialize(),
  //       },
  //       info: {
  //       },
  //     })

  //   } catch (error) {
  //     return response.badRequest(error)
  //   }
  // }

  // /**
  //  * @update
  //  * @tag Admin Variant
  //  * @summary Update a variant
  //  * @description Update a variant descriptively
  //  * @paramPath id - ID of Variant - @type(string) @required
  //  * @requestBody <ZnVariant>
  //  * @responseBody 200 - <ZnVariant>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - OK
  //  * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The title field must be defined","rule":"required","field":"title"}]} - Bad Request
  //  * @responseBody 401 - Unauthorized access - Unauthorized
  //  * @responseBody 404 - {"message":"Variant not found"} - Not Found
  //  */
  // /**
  //  * Handle form submission for the edit action
  //  */
  // public async update({ bouncer, params, request, response }: HttpContext) {
  //   await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.PRODUCT)

  //   const variantId = params.id

  //   const variant = await ZnVariant.query()
  //     .where('id', variantId)
  //     .first()

  //   if (!variant) { return response.notFound({ message: 'Variant not found' }) }

  //   const data = request.all()

  //   const payload = await createVariantValidator.validate(data)

  //   try {
  //     // nailSystem.name = payload.name

  //     const updated = await variant.save()

  //     return response.ok(updated)

  //   } catch (error) {
  //     return response.internalServerError({
  //       message: 'Something went wrong',
  //     })
  //   }
  // }
  // /**
  //  * @destroy
  //  * @tag Admin Variant
  //  * @summary Soft-delete a variant
  //  * @description Soft-delete a variant descriptively
  //  * @paramPath id - ID of Variant - @type(string) @required
  //  * @responseBody 200 - {"message":"Variant soft-deleted successfully"}
  //  * @responseBody 401 - Unauthorized access - Unauthorized
  //  * @responseBody 404 - {"message":"Variant not found"} - Not Found
  //  */
  // /**
  //  * Delete record
  //  */
  // public async destroy({ bouncer, params, response }: HttpContext) {
  //   await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.PRODUCT)

  //   try {
  //     const variantId = params.id

  //     const variant = await ZnVariant.query()
  //       .where('id', variantId)
  //       .first()

  //     if (!variant) { return response.notFound({ message: 'Variant not found' }) }

  //     await variant?.softDelete()

  //     return response.ok({ message: 'Variant soft-deleted successfully' })

  //   } catch (error) {
  //     return response.badRequest(error)
  //   }
  // }
}
