import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_orders'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.decimal('currentSubtotalPrice', 15, 6).defaultTo(0)
      table.decimal('currentTotalTax', 15, 6).defaultTo(0)
      table.decimal('currentTotalDiscounts', 15, 6).defaultTo(0)
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('currentSubtotalPrice')
      table.dropColumn('currentTotalTax')
      table.dropColumn('currentTotalDiscounts')
    })
  }
}