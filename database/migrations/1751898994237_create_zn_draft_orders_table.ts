import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_draft_orders'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary()
      table.integer('code').unsigned().nullable()
      table.uuid('userId').references('id').inTable('zn_users').notNullable()
      table.uuid('shippingAddressId').references('id').inTable('zn_addresses').nullable()
      table.uuid('billingAddressId').references('id').inTable('zn_addresses').nullable()

      table.string('status').notNullable().defaultTo('draft')
      table.string('currency').notNullable().defaultTo('USD')
      table.text('note').nullable()

      table.double('subTotalPrice').notNullable().defaultTo(0)
      table.double('totalDiscount').notNullable().defaultTo(0)
      table.double('totalShipping').notNullable().defaultTo(0)
      table.double('totalTax').notNullable().defaultTo(0)
      table.double('totalPrice').notNullable().defaultTo(0)
      table.string('paymentMethod').notNullable().defaultTo('cash')
      table.string('os').nullable()
      table.string('appVersion').nullable()
      table.string('shippingRateHandle').nullable()
      table.string('shopifyOrderId').nullable()
      table.json('cartSectionIds').defaultTo([])

      table.dateTime('deletedAt').nullable()
      table.dateTime('createdAt', { useTz: true }).notNullable()
      table.dateTime('updatedAt', { useTz: true }).notNullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
