import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_product_images'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('shopifyImageId').nullable().alter()
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      // revert to notNullable and unique?
      table
    })
  }
}