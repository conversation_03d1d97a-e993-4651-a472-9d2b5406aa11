import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_order_fulfillments_and_order_details'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropPrimary('id')
      table.dropColumn('id')
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.uuid('id')
    })
  }
}