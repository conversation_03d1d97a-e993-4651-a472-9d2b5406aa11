import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected vendorOrdersTableName = 'zn_vendor_orders'
  protected ordersTableName = 'zn_orders'
  protected vendorsTableName = 'zn_vendors'
  protected fulfillmentsTableName = 'zn_order_fulfillments'

  async up() {
    this.schema.createTable(this.vendorOrdersTableName, (table) => {
      table.uuid('id').primary()

      table.string('status').nullable()
      table.timestamp('cancelledAt', { useTz: true }).nullable()
      table.timestamp('closedAt', { useTz: true }).nullable()
      table.decimal('subtotalPrice', 15, 6).defaultTo(0)
      table.decimal('totalTax', 15, 6).defaultTo(0)
      table.decimal('totalDiscounts', 15, 6).defaultTo(0)
      table.decimal('totalShipping', 15, 6).defaultTo(0)
      table.decimal('totalPrice', 15, 6).defaultTo(0)
      table.decimal('currentTotalPrice', 15, 6).defaultTo(0)
      table.string('note').nullable()

      table.uuid('orderId').references('id').inTable(this.ordersTableName).onDelete('CASCADE')
      table.uuid('vendorId').references('id').inTable(this.vendorsTableName).onDelete('CASCADE')
      table.uuid('fulfillmentId').references('id').inTable(this.fulfillmentsTableName).onDelete('CASCADE')

      table.timestamp('createdAt', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updatedAt', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('deletedAt', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.vendorOrdersTableName)
  }
}