import { TransactionSource } from '#constants/transaction'
import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_transactions'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary()

      table.string('source', 255).notNullable().defaultTo(TransactionSource.PAYPAL)
      table.string('sourceId', 255).notNullable()
      table.string('status', 255).notNullable()

      table.string('amount', 255).notNullable()
      table.string('currency', 255).notNullable()

      table.string('paymentId', 255).notNullable()
      table.string('paymentStatus', 255).notNullable()

      table.string('sourceCreatedAt', 255).nullable()
      table.string('sourceUpdatedAt', 255).nullable()

      table.string('payerId', 255).notNullable()
      table.string('payerName', 255).nullable()
      table.string('payerEmail', 255).nullable()
      table.string('payerPhone', 255).nullable()

      table.uuid('orderId').index().comment('zurno order id')
      table.uuid('responseId').nullable()

      table.timestamp('createdAt', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updatedAt', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('deletedAt', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
