import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected affiliatesTableName = 'zn_affiliates'
  protected tiersTableName = 'zn_affiliate_tiers'
  protected commissionGroupsTableName = 'zn_tier_commission_groups'
  protected commissionRatesTableName = 'zn_affiliate_commission_rates'
  protected commissionsTableName = 'zn_affiliate_commissions'
  protected commissionDetailsTableName = 'zn_affiliate_commission_details'
  protected commissionPaymentsTableName = 'zn_affiliate_commission_payments'

  async up() {
    this.schema.alterTable(this.affiliatesTableName, (table) => {
      table.decimal('grossMerchandiseValue', 15, 6).alter()
      table.decimal('totalCommissions', 15, 6).alter()
      table.decimal('totalPayments', 15, 6).alter()
    })

    this.schema.alterTable(this.tiersTableName, (table) => {
      table.decimal('defaultCommission', 12, 6).alter()
      table.decimal('defaultDiscount', 12, 6).alter()
    })

    this.schema.alterTable(this.commissionGroupsTableName, (table) => {
      table.decimal('commissionRate', 12, 6).alter()
    })

    this.schema.alterTable(this.commissionRatesTableName, (table) => {
      table.decimal('revenueFrom', 15, 6).alter()
      table.decimal('commissionRate', 12, 6).alter()
    })

    this.schema.alterTable(this.commissionsTableName, (table) => {
      table.decimal('commissionAmount', 15, 6).alter()
      table.decimal('adjustedAmount', 15, 6).alter()
    })

    this.schema.alterTable(this.commissionDetailsTableName, (table) => {
      table.decimal('commissionAmount', 15, 6).alter()
      table.decimal('commissionRate', 12, 6).alter()
    })

    this.schema.alterTable(this.commissionPaymentsTableName, (table) => {
      table.decimal('amount', 15, 6).alter()
    })
  }

  async down() {
    this.schema.alterTable(this.commissionPaymentsTableName, (table) => {
      table.decimal('amount', 12, 2).alter()
    })

    this.schema.alterTable(this.commissionDetailsTableName, (table) => {
      table.decimal('commissionAmount', 12, 2).alter()
      table.float('commissionRate', 8, 2).alter()
    })

    this.schema.alterTable(this.commissionsTableName, (table) => {
      table.decimal('commissionAmount', 12, 2).alter()
      table.decimal('adjustedAmount', 12, 2).alter()
    })

    this.schema.alterTable(this.commissionRatesTableName, (table) => {
      table.float('revenueFrom', 8, 2).alter()
      table.float('commissionRate', 8, 2).alter()
    })

    this.schema.alterTable(this.commissionGroupsTableName, (table) => {
      table.decimal('commissionRate', 10, 2).alter()
    })

    this.schema.alterTable(this.tiersTableName, (table) => {
      table.decimal('defaultCommission', 10, 2).alter()
      table.decimal('defaultDiscount', 10, 2).alter()
    })

    this.schema.alterTable(this.affiliatesTableName, (table) => {
      table.decimal('grossMerchandiseValue', 12, 2).alter()
      table.decimal('totalCommissions', 12, 2).alter()
      table.decimal('totalPayments', 12, 2).alter()
    })
  }
}