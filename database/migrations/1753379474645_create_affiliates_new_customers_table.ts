import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected pivotTableName = 'zn_affiliates_new_customers'
  protected affiliatesTableName = 'zn_affiliates'
  protected usersTableName = 'zn_users'

  async up() {
    this.schema.createTable(this.pivotTableName, (table) => {
      table.uuid('affiliateId').references('id').inTable(this.affiliatesTableName).onDelete('CASCADE')
      table.uuid('userId').references('id').inTable(this.usersTableName).onDelete('CASCADE')
    })
  }

  async down() {
    this.schema.dropTable(this.pivotTableName)
  }
}