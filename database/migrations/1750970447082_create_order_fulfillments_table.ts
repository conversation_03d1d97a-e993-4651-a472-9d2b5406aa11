import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected fulfillmentsTableName = 'zn_order_fulfillments'
  protected ordersTableName = 'zn_orders'
  protected orderDetailsTableName = 'zn_order_details'
  protected fulfillmentsAndOrderDetailsPivotTableName = 'zn_order_fulfillments_and_order_details'

  async up() {
    this.schema.createTable(this.fulfillmentsTableName, (table) => {
      table.uuid('id').primary()

      table.string('status').nullable()
      table.string('trackingCompany').nullable()
      table.string('trackingNumber').nullable()
      table.string('trackingNumbers').nullable()
      table.string('trackingUrl').nullable()
      table.string('trackingUrls', 1024).nullable()
      table.string('shopifyFulfillmentId').nullable().comment('Shopify fulfillment ID if applicable')

      table.uuid('orderId').references('id').inTable(this.ordersTableName).onDelete('CASCADE')

      table.timestamp('createdAt', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updatedAt', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('deletedAt', { useTz: true }).nullable()
    })

    this.schema.createTable(this.fulfillmentsAndOrderDetailsPivotTableName, (table) => {
      table.uuid('id').primary()

      table.uuid('fulfillmentId').references('id').inTable(this.fulfillmentsTableName).onDelete('CASCADE')
      table.uuid('orderDetailId').references('id').inTable(this.orderDetailsTableName).onDelete('CASCADE')

      table.integer('quantity').notNullable().defaultTo(1).comment('Quantity of the order detail in this fulfillment');
    })

    this.schema.alterTable(this.orderDetailsTableName, (table) => {
      table.string('fulfillmentStatus').defaultTo('unfulfilled').comment('Status of the order detail fulfillment');
    })
  }

  async down() {
    this.schema.alterTable(this.orderDetailsTableName, (table) => {
      table.dropColumn('fulfillmentStatus')
    })
    this.schema.dropTable(this.fulfillmentsAndOrderDetailsPivotTableName)
    this.schema.dropTable(this.fulfillmentsTableName)
  }
}