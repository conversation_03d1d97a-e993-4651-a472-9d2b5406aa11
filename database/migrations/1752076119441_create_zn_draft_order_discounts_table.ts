import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_draft_order_discounts'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary()
      table.uuid('draftOrderId').references('id').inTable('zn_draft_orders').notNullable()
      table.string('discountCode').nullable()
      table.string('discountType').nullable()
      table.double('amount').defaultTo(0)
      table.double('percentage').defaultTo(0)
      table.string('title').nullable()
      table.text('description').nullable()

      table.dateTime('deletedAt').nullable()
      table.timestamp('createdAt')
      table.timestamp('updatedAt')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
