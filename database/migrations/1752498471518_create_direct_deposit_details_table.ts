import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected paymentMethodsTableName = 'zn_payment_methods'
  protected directDepositTableName = 'zn_direct_deposit_details'

  async up() {
    this.schema.alterTable(this.paymentMethodsTableName, (table) => {
      table.enum('paymentType', ['PAYPAL', 'DIRECT_DEPOSIT', 'ACH_TRANSFER', 'OTHER']).notNullable().alter()
    })

    this.schema.createTable(this.directDepositTableName, (table) => {
      table.uuid('id').primary()

      table.string('routingNumber').notNullable()
      table.string('accountName').notNullable()
      table.string('accountNumber').notNullable()
      table.enum('accountType', ['CHECKING', 'SAVINGS']).notNullable()
      table.string('bankName').nullable()
      table.string('accountHolderAddress').nullable()

      table.uuid('paymentMethodId').references('id').inTable(this.paymentMethodsTableName).onDelete('CASCADE')

      table.timestamp('createdAt', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updatedAt', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('deletedAt', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.directDepositTableName)

    this.schema.alterTable(this.paymentMethodsTableName, (table) => {
      table.enum('paymentType', ['PAYPAL', 'ACH_TRANSFER', 'OTHER']).notNullable().alter()
    })
  }
}