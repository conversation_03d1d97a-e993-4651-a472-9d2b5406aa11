import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableCommissionRatesName = 'zn_affiliate_commission_rates'
  protected tableCommissionGroupsName = 'zn_tier_commission_groups'

  async up() {
    this.schema.createTable(this.tableCommissionRatesName, (table) => {
      table.uuid('id').primary()

      table.float('revenueFrom').notNullable().defaultTo(0).comment('Minimum revenue to apply this commission rate')
      table.float('commissionRate').notNullable().defaultTo(0).comment('Commission rate percentage for the revenue range')

      table.uuid('commissionGroupId').references('id').inTable(this.tableCommissionGroupsName).onDelete('CASCADE')

      table.timestamp('createdAt', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updatedAt', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('deletedAt', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableCommissionRatesName)
  }
}