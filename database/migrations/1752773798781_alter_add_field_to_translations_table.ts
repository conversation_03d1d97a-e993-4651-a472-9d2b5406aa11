import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_transactions'
  protected tableNameDraftOrders = 'zn_draft_orders'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('accountNumber').nullable()
      table.string('accountType').nullable()
    })
    this.schema.alterTable(this.tableNameDraftOrders, (table) => {
      table.text('shippingRateHandle').nullable().alter()
      table.uuid('orderId').nullable()
      table.string('shopifyDraftOrderId').nullable()
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('accountNumber')
      table.dropColumn('accountType')
    })
    this.schema.alterTable(this.tableNameDraftOrders, (table) => {
      table.dropColumn('orderId')
      table.dropColumn('shopifyDraftOrderId')
    })
  }
}
