import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected vendorsTableName = 'zn_vendors'

  async up() {
    this.schema.alterTable(this.vendorsTableName, (table) => {
      table.dropUnique(['companyName'], 'zn_product_vendors_name_unique');
    })
  }

  async down() {
    this.schema.alterTable(this.vendorsTableName, (table) => {
      table.unique(['companyName'], 'zn_product_vendors_name_unique');
    })
  }
}