import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_affiliate_tiers'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.integer('requiredNewCustomers').defaultTo(0).notNullable().comment('Number of new customers required to qualify for this tier')
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('requiredNewCustomers')
    })
  }
}