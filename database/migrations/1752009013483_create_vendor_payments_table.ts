import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected vendorPaymentsTableName = 'zn_vendor_payments'
  protected vendorsTableName = 'zn_vendors'
  protected paymentMethodsTableName = 'zn_payment_methods'

  async up() {
    this.schema.createTable(this.vendorPaymentsTableName, (table) => {
      table.uuid('id').primary()

      table.decimal('amount', 15, 6).notNullable()

      table.uuid('vendorId').references('id').inTable(this.vendorsTableName).onDelete('CASCADE')
      table.uuid('paymentMethodId').references('id').inTable(this.paymentMethodsTableName).onDelete('CASCADE')

      table.timestamp('createdAt', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updatedAt', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('deletedAt', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.vendorPaymentsTableName)
  }
}