import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected vendorTableName = 'zn_vendors'
  protected warehouseTableName = 'zn_warehouses'

  async up() {
    this.schema.alterTable(this.vendorTableName, (table) => {
      table.uuid('warehouseId').references('id').inTable(this.warehouseTableName).onDelete("SET NULL")
    })

    this.schema.alterTable(this.warehouseTableName, (table) => {
      table.string('shopifyLocationId').after('fulfilWarehouseId')
      table.integer('fulfilWarehouseId').nullable().alter()
      table.string('code').nullable().alter()
    })
  }

  async down() {
    this.schema.alterTable(this.warehouseTableName, (table) => {
      table.dropColumn('shopifyLocationId')
    })

    this.schema.alterTable(this.vendorTableName, (table) => {
      table.dropForeign('warehouseId')
      table.dropColumn('warehouseId')
    })
  }
}