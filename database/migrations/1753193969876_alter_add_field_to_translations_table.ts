import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_transactions'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('refTransactionId').nullable()
      table.string('payerId').nullable().alter()
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('refTransactionId')
    })
  }
}
