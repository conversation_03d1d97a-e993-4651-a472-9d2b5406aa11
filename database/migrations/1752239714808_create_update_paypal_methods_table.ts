import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_paypal_details'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.string('legalName').nullable()
      table.string('countryCode').nullable()
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('countryCode')
      table.dropColumn('legalName')
    })
  }
}