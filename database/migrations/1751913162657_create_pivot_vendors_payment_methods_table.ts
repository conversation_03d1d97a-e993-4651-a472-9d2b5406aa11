import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected pivotTableName = 'zn_vendors_payment_methods'
  protected vendorsTableName = 'zn_vendors'
  protected paymentMethodsTableName = 'zn_payment_methods'

  async up() {
    this.schema.createTable(this.pivotTableName, (table) => {
      table.uuid('vendorId').references('id').inTable(this.vendorsTableName).onDelete('CASCADE')
      table.uuid('paymentMethodId').references('id').inTable(this.paymentMethodsTableName).onDelete('CASCADE')
      table.primary(['vendorId', 'paymentMethodId'])
    })
  }

  async down() {
    this.schema.dropTable(this.pivotTableName)
  }
}