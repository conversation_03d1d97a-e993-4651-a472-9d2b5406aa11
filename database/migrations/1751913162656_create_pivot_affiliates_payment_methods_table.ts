import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected pivotTableName = 'zn_affiliates_payment_methods'
  protected affiliatesTableName = 'zn_affiliates'
  protected paymentMethodsTableName = 'zn_payment_methods'

  async up() {
    this.schema.createTable(this.pivotTableName, (table) => {
      table.uuid('affiliateId').references('id').inTable(this.affiliatesTableName).onDelete('CASCADE')
      table.uuid('paymentMethodId').references('id').inTable(this.paymentMethodsTableName).onDelete('CASCADE')
      table.primary(['affiliateId', 'paymentMethodId'])
    })

    await this.defer(async (db) => {
      const paymentMethods = await db.from(this.paymentMethodsTableName);

      for (const paymentMethod of paymentMethods) {
        if (paymentMethod.affiliateId) {
          await db.table(this.pivotTableName).insert({
            affiliateId: paymentMethod.affiliateId,
            paymentMethodId: paymentMethod.id
          });
        }
      }
    })
  }

  async down() {
    this.schema.dropTable(this.pivotTableName)
  }
}