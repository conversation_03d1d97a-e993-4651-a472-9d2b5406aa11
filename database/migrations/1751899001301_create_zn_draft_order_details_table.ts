import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_draft_order_details'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('id').primary()
      table.uuid('draftOrderId').references('id').inTable('zn_draft_orders').notNullable()

      table.string('title').notNullable()
      table.uuid('variantId').references('id').inTable('zn_product_variants').notNullable()
      table.string('variantTitle').nullable()
      table.string('sku').notNullable()
      table.string('imageUrl').nullable()
      table.integer('quantity').notNullable().unsigned()
      table.double('price').defaultTo(0)
      table.double('tax').defaultTo(0)
      table.double('discount').defaultTo(0)
      table.integer('fastBundleDiscountId').nullable()

      table.dateTime('deletedAt').nullable()
      table.dateTime('createdAt', { useTz: true }).notNullable()
      table.dateTime('updatedAt', { useTz: true }).notNullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
