import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected vendorCommmissionsTableName = 'zn_vendor_commissions'
  protected vendorsTableName = 'zn_vendors'
  protected ordersTableName = 'zn_orders'

  async up() {
    this.schema.createTable('zn_vendor_commissions', (table) => {
      table.uuid('id').primary()
      table.float('commissionRate').defaultTo(0)
      table.float('fixedCommissionAmount').defaultTo(0)
      table.decimal('commissionAmount', 12, 2).nullable()
      table.string('status', 255).nullable()
      table.string('rejectionReason', 255).nullable()
      table.decimal('adjustedAmount', 12, 2).nullable()
      table.string('adjustedReason', 255).nullable()

      table.uuid('vendorId').references('id').inTable(this.vendorsTableName).onDelete('CASCADE')
      table.uuid('orderId').references('id').inTable(this.ordersTableName).onDelete('CASCADE')

      table.timestamp('createdAt', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('updatedAt', { useTz: true }).notNullable().defaultTo(this.now())
      table.timestamp('deletedAt', { useTz: true }).nullable()
    })
  }

  async down() {
    this.schema.dropTable(this.vendorCommmissionsTableName)
  }
}