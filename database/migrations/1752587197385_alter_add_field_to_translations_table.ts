import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_transactions'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.uuid('draftOrderId').nullable()
      table.uuid('orderId').nullable().alter()
    })

    this.schema.alterTable('zn_draft_order_details', (table) => {
      table.uuid('giftId').nullable()
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('draftOrderId')
    })
    this.schema.alterTable('zn_draft_order_details', (table) => {
      table.dropColumn('giftId')
    })
  }
}
