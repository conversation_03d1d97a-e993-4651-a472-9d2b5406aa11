import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_variants_option_values'
  protected productTableName = 'zn_products'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.uuid('variantId')
      table.uuid('optionValueId')
    })

    this.schema.alterTable(this.productTableName, (table) => {
      table.jsonb('pendingChanges')
      table.integer('pendingApproval')
    })
  }

  async down() {
    this.schema.alterTable(this.productTableName, (table) => {
      table.dropColumn('pendingChanges')
      table.dropColumn('pendingApproval')
    })

    this.schema.dropTable(this.tableName)
  }
}