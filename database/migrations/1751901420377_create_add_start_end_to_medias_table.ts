import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'zn_medias'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.float('start').nullable()
      table.float('end').nullable()
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropColumn('start')
      table.dropColumn('end')
    })
  }
}
