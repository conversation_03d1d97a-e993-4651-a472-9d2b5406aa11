import AppMail from '#mails/app_mail'
import ZnSalonConstructionServiceSignup from '#models/zn_salon_construction_service_signup'
import env from '#start/env'

export default class SalonConstructionRejectedNotification extends AppMail {
  subject = 'Update on Your Salon Construction Service Application'

  constructor(
    private signup: ZnSalonConstructionServiceSignup,
    private rejectionReason?: string
  ) {
    super()
  }

  /**
   * The "prepare" method is called automatically when
   * the email is sent or queued.
   */
  prepare() {
    const supportEmail = env.get('SUPPORT_EMAIL') || ''
    const supportPhone = env.get('SUPPORT_PHONE_NUMBER') || ''

    this.message
      .htmlView('mails/salon_construction_service/rejected_notification', {
        serverDomain: this.baseUrl,
        signup: this.signup,
        rejectionReason: this.rejectionReason,
        supportEmail,
        supportPhone,
      })
      .to(this.signup.emailAddress)
  }
}
