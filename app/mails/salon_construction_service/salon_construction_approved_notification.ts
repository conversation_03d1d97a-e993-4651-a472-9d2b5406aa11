import AppMail from '#mails/app_mail'
import ZnSalonConstructionServiceSignup from '#models/zn_salon_construction_service_signup'
import env from '#start/env'

export default class SalonConstructionApprovedNotification extends AppMail {
  subject = 'Great News! Your Salon Construction Service Application Has Been Approved 🎉'

  constructor(private signup: ZnSalonConstructionServiceSignup) {
    super()
  }

  /**
   * The "prepare" method is called automatically when
   * the email is sent or queued.
   */
  prepare() {
    const supportEmail = env.get('SUPPORT_EMAIL') || ''
    const supportPhone = env.get('SUPPORT_PHONE_NUMBER') || ''

    this.message
      .htmlView('mails/salon_construction_service/approved_notification', {
        serverDomain: this.baseUrl,
        signup: this.signup,
        supportEmail,
        supportPhone,
      })
      .to(this.signup.emailAddress)
  }
}
