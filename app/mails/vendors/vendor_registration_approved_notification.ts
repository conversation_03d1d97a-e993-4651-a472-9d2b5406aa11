import AppMail from '#mails/app_mail';
import ZnVendor from '#models/zn_vendor';
import env from '#start/env';

export default class VendorRegistrationApprovedNotification extends AppMail {
  subject = "Welcome to Zurno - Your Vendor Account is Now Active! 🎉"

  constructor(
    private vendor: ZnVendor
  ) {
    super()
  }

  prepare() {
    const dashboardUrl = env.get('VENDOR_CMS_URL') ?? 'https://vendor.zurno.com'
    const supportEmail = env.get('SUPPORT_EMAIL') ?? '<EMAIL>'
    const supportPhone = env.get('SUPPORT_PHONE_NUMBER') ?? '+****************'

    this.message
      .htmlView('mails/vendors/vendor_registration_approved.edge', {
        serverDomain: this.baseUrl,
        vendor: this.vendor,
        dashboardUrl,
        supportEmail,
        supportPhone,
      })
      .to(this.vendor.email)
  }
}
