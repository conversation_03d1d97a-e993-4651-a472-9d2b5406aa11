import AppMail from '#mails/app_mail';
import ZnVendorPayment from '#models/zn_vendor_payment';
import env from '#start/env';
import formatUSD from '../../../admin/utils/currency-formatter.js';

export default class VendorPaymentNotification extends AppMail {
  constructor(
    private payment: ZnVendorPayment,
  ) {
    super()
  }

  prepare() {
    const formatedAmount = formatUSD(this.payment.amount);
    const formatedPaymentDate = this.payment.createdAt.toFormat('EEE, MMM dd, yyyy, hh:mm a');
    const paymentMethod = this.payment.paymentMethod.paymentTypeName;
    const dashboardUrl = env.get('VENDOR_CMS_URL') ?? 'https://vendor.zurno.com'
    const supportEmail = env.get('SUPPORT_EMAIL') ?? '<EMAIL>';
    const supportPhone = env.get('SUPPORT_PHONE_NUMBER') ?? '+****************';
    const title = `Payout Processed - ${formatedAmount} Sent to Your Account`;

    this.message
      .subject(title)
      .htmlView('mails/vendors/vendor_payment.edge', {
        serverDomain: this.baseUrl,
        companyName: this.payment.vendor.companyName,
        amount: formatedAmount,
        processingDate: formatedPaymentDate,
        paymentMethod,
        dashboardUrl,
        supportEmail,
        supportPhone,
      })
      .to(this.payment.vendor.email)
  }
}
