import AppMail from '#mails/app_mail';
import env from '#start/env';

export default class VendorRegistrationRejectedNotification extends AppMail {
  subject = "Update on Your Vendor Application"

  constructor(
    private companyName: string,
    private userEmail: string,
    private rejectionReason: string,
  ) {
    super()
  }

  prepare() {
    const supportEmail = env.get('SUPPORT_EMAIL') ?? '<EMAIL>'
    const supportPhone = env.get('SUPPORT_PHONE_NUMBER') ?? '+****************'

    this.message
      .htmlView('mails/vendors/vendor_registration_rejected.edge', {
        serverDomain: this.baseUrl,
        companyName: this.companyName,
        rejectionReason: this.rejectionReason,
        supportEmail,
        supportPhone,
      })
      .to(this.userEmail)
  }
}
