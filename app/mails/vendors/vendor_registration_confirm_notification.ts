import AppMail from '#mails/app_mail';
import env from '#start/env';

export default class VendorRegistrationConfirmNotification extends AppMail {
  subject = "Welcome to Zurno - Your Application is Under Review"

  constructor(
    private companyName: string,
    private userEmail: string,
  ) {
    super()
  }

  prepare() {
    const supportEmail = env.get('SUPPORT_EMAIL') ?? '<EMAIL>'
    const supportPhone = env.get('SUPPORT_PHONE_NUMBER') ?? '+****************'

    this.message
      .htmlView('mails/vendors/vendor_registration_confirm.edge', {
        serverDomain: this.baseUrl,
        companyName: this.companyName,
        supportEmail,
        supportPhone,
      })
      .to(this.userEmail)
  }
}
