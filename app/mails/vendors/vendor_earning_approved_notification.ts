import AppMail from '#mails/app_mail';
import ZnVendorEarning from '#models/zn_vendor_earning';
import ZnVendorOrder from '#models/zn_vendor_order';
import env from '#start/env';
import formatUSD from '../../../admin/utils/currency-formatter.js';

export default class VendorEarningApprovedNotification extends AppMail {
  constructor(
    private earning: ZnVendorEarning,
    private vendorOrder: ZnVendorOrder,
  ) {
    super()
  }

  prepare() {
    const orderName = this.earning.order.name;
    const title = `Earnings Approved - Order ${orderName}`;
    const formatedOrderTime = this.vendorOrder.createdAt.toFormat('EEE, MMM dd, yyyy, hh:mm a');
    const formatedOrderTotal = formatUSD(this.vendorOrder.totalPrice);
    const formatedEarningAmount = formatUSD(this.earning.finalAmount);
    const formatedBalance = formatUSD(this.earning.vendor.earnings.map((e) => e.finalAmount).reduce((acc, total) => acc + total, 0));
    const defaultPaymentMethod = this.earning.vendor.paymentMethods.find((pm) => pm.isDefault)?.paymentTypeName;
    const dashboardUrl = env.get('VENDOR_CMS_URL') ?? 'https://vendor.zurno.com'
    const supportEmail = env.get('SUPPORT_EMAIL') ?? '<EMAIL>';
    const supportPhone = env.get('SUPPORT_PHONE_NUMBER') ?? '+****************';

    this.message
      .subject(title)
      .htmlView('mails/vendors/vendor_earning_approved.edge', {
        serverDomain: this.baseUrl,
        companyName: this.earning.vendor.companyName,
        orderName,
        orderDate: formatedOrderTime,
        orderTotal: formatedOrderTotal,
        earningAmount: formatedEarningAmount,
        orderDetails: this.vendorOrder.orderDetails,
        balance: formatedBalance,
        defaultPaymentMethod,
        dashboardUrl,
        supportEmail,
        supportPhone,
      })
      .to(this.earning.vendor.email)
  }
}
