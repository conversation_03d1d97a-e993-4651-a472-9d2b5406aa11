import { TransactionSource, TransactionStatus } from '#constants/transaction'
import ZnDraftOrder from '#models/zn_draft_order'
import ZnGift from '#models/zn_gift'
import ZnOrder from '#models/zn_order'
import ZnTransaction from '#models/zn_transaction'
import ZnTransactionResponse from '#models/zn_transaction_response'
import { CartService } from '#services/shopify/cart/cart_service'
import { ShopifyDraftOrderService } from '#services/shopify/order/draft_order_service'
import { ShopifyService } from '#services/shopify/shopify_service'
import { SyncOrderService } from '#services/sync/sync_order_service'
import db from '@adonisjs/lucid/services/db'
import { AuthorizeNetService } from '../../../services/authorize-net/authorize_net_service.js'

export class PaymentHandleService {
  public async paymentSuccess(draftOrderId: string) {
    const draftOrder = await ZnDraftOrder.query()
      .where('id', draftOrderId)
      .preload('user')
      .preload('shippingAddress')
      .preload('details')
      .preload('discounts')
      .first()
    if (!draftOrder) {
      throw new Error('Draft order not found')
    }
    const transaction = await db.transaction()
    try {
      // Complete draft Order
      await draftOrder.useTransaction(transaction).merge({ status: 'completed' }).save()

      // Create Shopify Order
      const shopifyService = new ShopifyService()
      const shopifyDraftOrder = await this.createShopifyDraftOrder(draftOrder)
      if (!shopifyDraftOrder) {
        throw new Error('Failed to create Shopify draft order')
      }
      const { data: shopifyOrder, error } = await shopifyService.completeDraftOrder(
        shopifyDraftOrder.id
      )
      if (!shopifyOrder) {
        console.error(error)
        throw new Error('Failed to create Shopify order')
      }
      // Update draft order with Shopify order id
      await draftOrder.useTransaction(transaction).merge({ shopifyOrderId: shopifyOrder.id }).save()

      // Sync Shopify Order
      const syncOrderService = new SyncOrderService()
      const order = await syncOrderService.syncOrderFromShopify(shopifyOrder)
      // Update draft order with order id
      await draftOrder.useTransaction(transaction).merge({ orderId: order.id }).save()

      await transaction.commit()
      return order
    } catch (error) {
      await transaction.rollback()
      throw error
    }
  }

  async createShopifyDraftOrder(draftOrder: ZnDraftOrder) {
    // get shipping address
    const shippingAddress = {
      address1: draftOrder.shippingAddress.address1,
      address2: draftOrder.shippingAddress.address2,
      city: draftOrder.shippingAddress.city,
      country: draftOrder.shippingAddress.country,
      province: draftOrder.shippingAddress.province,
      zip: draftOrder.shippingAddress.zip,
    }
    // discount code
    const discountCodes = draftOrder.discounts
      .filter((d) => d.discountCode)
      .map((d) => d.discountCode) as string[]

    // Line Item
    const cartService = new CartService()
    const draftOrderService = new ShopifyDraftOrderService()
    const originalItems = draftOrder.details.filter((d) => !d.giftId)
    const items = originalItems.map((detail) => ({
      variantId: detail.variantId,
      quantity: detail.quantity,
      fastBundleDiscountId: detail.fastBundleDiscountId || undefined,
    }))
    let lineItems = await cartService.getLineItems({ lineItems: items as ILineItem[] })

    // Handle Apply Gift
    const giftIds = draftOrder.details.filter((d) => d.giftId).map((d) => d.giftId) as string[]
    const gifts = await ZnGift.query().whereIn('id', giftIds).preload('variants')
    // Apply gift to line items
    for (const gift of gifts) {
      const result = await draftOrderService.applyGiftForLineItem({
        lineItems,
        gift,
      })
      if (result.success) {
        lineItems = result.lineItems
      }
    }

    // Create Shopify Draft Order
    const shopifyDraftOrder = await draftOrderService.create({
      user: draftOrder.user,
      shippingAddress,
      billingAddress: shippingAddress,
      lineItems,
      discountCodes,
      note: draftOrder.note,
      tags: ['Zurno App'],
    })

    return shopifyDraftOrder
  }

  async refundPayment(orderId: string) {
    const order = await ZnOrder.query()
      .where('id', orderId)
      .orWhere('shopifyId', orderId)
      .preload('transaction')
      .preload('billingAddress')
      .preload('user')
      .first()
    if (!order) {
      throw new Error(`Order ${orderId} not found`)
    }
    if (order.financialStatus !== 'paid') {
      throw new Error(`Order ${orderId} is not paid`)
    }
    if (!order.transaction) {
      throw new Error(`Transaction for order ${orderId} not found`)
    }

    const trx = await db.transaction()
    try {
      const authorizeNetService = new AuthorizeNetService()
      const paymentResult = (await authorizeNetService.refundTransaction({
        transactionId: order.transaction.sourceId,
        amount: order.transaction.amount,
        creditCardNumber: order.transaction.accountNumber || 'XXXX',
        expirationDate: 'XXXX',
      })) as any

      // Create Transaction Response
      await ZnTransactionResponse.create({
        response: JSON.stringify(paymentResult),
      })

      if (paymentResult.success) {
        // Save transaction response
        const transactionResponse = await ZnTransactionResponse.create({
          response: JSON.stringify(paymentResult),
        })

        await order.useTransaction(trx).merge({ status: 'refunded' }).save()

        // Create Refund Transaction
        await ZnTransaction.create(
          {
            orderId: order.id,
            status: TransactionStatus.REFUNDED,
            amount: order.transaction.amount,
            currency: 'USD',
            source: TransactionSource.AUTHORIZE_NET,
            sourceId: paymentResult.transactionId,
            paymentId: paymentResult.transactionId,
            refTransactionId: order.transaction.id, // Reference Transaction ID
            accountNumber: paymentResult.accountNumber,
            accountType: paymentResult.accountType,
            paymentStatus: TransactionStatus.CAPTURED,
            sourceCreatedAt: new Date().toISOString(),
            sourceUpdatedAt: new Date().toISOString(),
            payerName: [order.billingAddress?.firstName, order.billingAddress?.lastName]
              .filter(Boolean)
              .join(''),
            payerId: '',
            payerEmail: order.user.email || '',
            responseId: transactionResponse.id.toString(),
          },
          { client: trx }
        )
      }
      await trx.commit()
      return paymentResult
    } catch (error) {
      await trx.rollback()
      throw error
    }
  }
}
