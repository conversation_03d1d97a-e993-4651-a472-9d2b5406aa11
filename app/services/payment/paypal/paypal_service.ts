import { paymentConfig } from '#config/payment'
import {
  CheckoutPaymentIntent,
  Client,
  LogLevel,
  OrderApplicationContextShippingPreference,
  OrderApplicationContextUserAction,
  OrdersController,
} from '@paypal/paypal-server-sdk'

class PayPalService {
  private client: Client
  private ordersController: OrdersController

  constructor() {
    const clientId = paymentConfig.paypal.clientId
    const clientSecret = paymentConfig.paypal.clientSecret
    const environment = paymentConfig.paypal.environment

    // Init Client
    this.client = new Client({
      clientCredentialsAuthCredentials: {
        oAuthClientId: clientId,
        oAuthClientSecret: clientSecret,
      },
      timeout: 0,
      environment,
      logging: {
        logLevel: LogLevel.Info,
        logRequest: {
          logBody: true,
        },
        logResponse: {
          logHeaders: true,
        },
      },
    })

    this.ordersController = new OrdersController(this.client)
  }

  async createOrder(amount: string, currency: string = 'USD') {
    const returnUrl = paymentConfig.paypal.returnUrl
    const cancelUrl = paymentConfig.paypal.cancelUrl

    const request = {
      body: {
        intent: CheckoutPaymentIntent.Capture,
        purchaseUnits: [
          {
            amount: {
              currencyCode: currency,
              value: amount.toString(),
            },
          },
        ],
        applicationContext: {
          returnUrl,
          cancelUrl,
          brandName: paymentConfig.paypal.brandName,
          userAction: OrderApplicationContextUserAction.PayNow,
          shippingPreference: OrderApplicationContextShippingPreference.NoShipping,
        },
      },
    }

    try {
      const response = await this.ordersController.createOrder(request)
      return response.result
    } catch (error) {
      throw new Error(`PayPal order creation failed: ${error.message}`)
    }
  }

  async captureOrder(orderId: string): Promise<any> {
    const request = {
      id: orderId,
      body: {},
    }

    try {
      const response = await this.ordersController.captureOrder(request)
      return response.result
    } catch (error) {
      throw new Error(`PayPal order capture failed: ${error.message}`)
    }
  }

  async getOrderDetails(orderId: string) {
    try {
      const response = await this.ordersController.getOrder({ id: orderId })
      return response.result
    } catch (error) {
      throw new Error(`Failed to get order details: ${error.message}`)
    }
  }
}

export default PayPalService
