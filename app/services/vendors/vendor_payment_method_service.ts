import VendorService from "./vendor_service.js"
import ZnPaymentMethod from "#models/zn_payment_method";
import { EPaymentType } from "#constants/payment_type";
import ZnPaypalDetail from "#models/zn_paypal_detail";
import ZnAchTransferDetail from "#models/zn_ach_transfer_detail";
import ZnOtherPaymentDetail from "#models/zn_other_payment_detail";
import ZnVendor from "#models/zn_vendor";
import logger from "@adonisjs/core/services/logger";
import ZnUser from "#models/zn_user";
import ZnDirectDepositDetail from "#models/zn_direct_deposit_detail";

export default class VendorPaymentMethodService {
  private vendorService: VendorService;

  constructor() {
    this.vendorService = new VendorService();
  }

  async getAllPaymentMethodsByUser(user: ZnUser) {
    if (!user.vendorId) {
      return {
        success: false,
        message: "This user doesn't belong to any vendor",
        paymentMethods: [],
      };
    }

    return await this.getAllPaymentMethodsByVendor(user.vendorId);
  }

  async getAllPaymentMethodsByVendor(vendorId: string) {
    const vendor = await ZnVendor.query()
      .where('id', vendorId)
      .preload('paymentMethods', (query) => {
        query.preload('paypalDetail')
          .preload('directDepositDetail')
          .preload('achTransferDetail')
          .preload('otherPaymentDetail')
      })
      .firstOrFail();
    return {
      success: true,
      message: "Success",
      paymentMethods: [
        ...vendor.paymentMethods.filter((pm) => pm.paymentType === EPaymentType.PAYPAL),
        ...vendor.paymentMethods.filter((pm) => pm.paymentType === EPaymentType.DIRECT_DEPOSIT),
        ...vendor.paymentMethods.filter((pm) => pm.paymentType === EPaymentType.ACH_TRANSFER),
        ...vendor.paymentMethods.filter((pm) => pm.paymentType === EPaymentType.OTHER),
      ],
    }
  }

  async getPaymentMethodById(paymentMethodId: string) {
    const paymentMethod = await ZnPaymentMethod.findOrFail(paymentMethodId);

    if (paymentMethod.paymentType == EPaymentType.PAYPAL)
      await paymentMethod.load('paypalDetail');
    else if (paymentMethod.paymentType == EPaymentType.DIRECT_DEPOSIT)
      await paymentMethod.load('directDepositDetail');
    else if (paymentMethod.paymentType == EPaymentType.ACH_TRANSFER)
      await paymentMethod.load('achTransferDetail');
    else
      await paymentMethod.load('otherPaymentDetail');

    return paymentMethod;
  }

  async create(user: ZnUser, payload: any) {
    const vendor = await this.vendorService.getVendorByUserId(user.id);

    let paymentMethod = await ZnPaymentMethod.query()
      .whereHas('vendors', (query) => {
        query.where('id', vendor.id);
      })
      .where('paymentType', payload.paymentType)
      .first();

    if (!paymentMethod) {
      paymentMethod = await ZnPaymentMethod.create({
        paymentType: payload.paymentType,
        isDefault: false
      })

      await vendor.related('paymentMethods').attach([paymentMethod.id]);
      await vendor.load('paymentMethods', (query) => {
        query.where('paymentType', payload.paymentType);
      });
    }

    switch (payload.paymentType) {

      case EPaymentType.PAYPAL:
        if (!payload.paypalEmail) throw new Error('PayPal email is required')

        const existingPaypalMethod = await ZnPaypalDetail.findBy('paymentMethodId', vendor.paymentMethods[0].id)

        if (!existingPaypalMethod) {
          await ZnPaypalDetail.create({
            paymentMethodId: vendor.paymentMethods[0].id,
            paypalEmail: payload.paypalEmail,
            legalName: payload.legalName,
            countryCode: payload.countryCode,
          })
        } else {
          return {
            success: false,
            message: 'This payment method is already existed. Please use PUT method to update this payment method.'
          }
        }
        break

      case EPaymentType.DIRECT_DEPOSIT:
        if (!payload.accountName || !payload.routingNumber || !payload.accountNumber || !payload.accountType) {
          throw new Error('Direct deposit information is not fully provided')
        }

        const directDepositMethod = await ZnDirectDepositDetail.findBy('paymentMethodId', vendor.paymentMethods[0].id)

        if (!directDepositMethod) {
          await ZnDirectDepositDetail.create({
            paymentMethodId: vendor.paymentMethods[0].id,
            accountName: payload.accountName,
            routingNumber: payload.routingNumber,
            accountNumber: payload.accountNumber,
            accountType: payload.accountType,
            bankName: payload.bankName,
            accountHolderAddress: payload.accountHolderAddress,
          })
        } else {
          return {
            success: false,
            message: 'This payment method is already existed. Please use PUT method to update this payment method.'
          }
        }
        break

      case EPaymentType.ACH_TRANSFER:
        if (!payload.accountName || !payload.routingNumber || !payload.accountNumber || !payload.accountType) {
          throw new Error('All ACH Transfer information are required')
        }

        const existingACHMethod = await ZnAchTransferDetail.findBy('paymentMethodId', vendor.paymentMethods[0].id)

        if (!existingACHMethod) {
          await ZnAchTransferDetail.create({
            paymentMethodId: vendor.paymentMethods[0].id,
            accountName: payload.accountName,
            routingNumber: payload.routingNumber,
            accountNumber: payload.accountNumber,
            accountType: payload.accountType
          })
        } else {
          return {
            success: false,
            message: 'This payment method is already existed. Please use PUT method to update this payment method.'
          }
        }
        break

      default:
        if (!payload.note) {
          throw new Error('Additional information is required')
        }

        const existingOtherMethod = await ZnOtherPaymentDetail.findBy('paymentMethodId', vendor.paymentMethods[0].id)

        if (!existingOtherMethod) {
          await ZnOtherPaymentDetail.create({
            paymentMethodId: vendor.paymentMethods[0].id,
            note: payload.note
          })
        } else {
          return {
            success: false,
            message: 'This payment method is already existed. Please use PUT method to update this payment method.'
          }
        }
    }

    const isDefault = (payload.isDefault !== null && payload.isDefault !== undefined) ? payload.isDefault : false
    await this.setDefaultMethodOnCreate(vendor as ZnVendor, vendor.paymentMethods[0].id, isDefault)

    for (const paymentMethod of vendor.paymentMethods) {
      if (paymentMethod.paymentType == EPaymentType.PAYPAL)
        await paymentMethod.load('paypalDetail')
      else if (paymentMethod.paymentType == EPaymentType.DIRECT_DEPOSIT)
        await paymentMethod.load('directDepositDetail')
      else if (paymentMethod.paymentType == EPaymentType.ACH_TRANSFER)
        await paymentMethod.load('achTransferDetail')
      else
        await paymentMethod.load('otherPaymentDetail')
    }

    logger.info('Added payment method. Current payment methods: %s', JSON.stringify(vendor.paymentMethods, null, 2))
    return {
      success: true,
      paymentMethods: vendor.paymentMethods
    }
  }

  private async setDefaultMethodOnCreate(vendor: ZnVendor, currentPaymentMethodId: string, isDefault: boolean) {
    await vendor.load('paymentMethods');

    if (vendor.paymentMethods.length === 1) {
      vendor.paymentMethods[0].isDefault = true;
      await vendor.paymentMethods[0].save();
      return vendor.paymentMethods[0];
    }

    if (!isDefault) {
      return vendor.paymentMethods[0];
    }

    for (const method of vendor.paymentMethods) {
      method.isDefault = (method.id == currentPaymentMethodId)
      await method.save();
    }

    return vendor.paymentMethods;
  }

  async update(paymentMethodId: string, payload: any) {
    const paymentMethod = await ZnPaymentMethod.findOrFail(paymentMethodId);

    switch (paymentMethod.paymentType) {
      case EPaymentType.PAYPAL:
        if (!payload.paypalEmail) {
          throw new Error('PayPal email was not provided');
        }
        const paypalDetail = await ZnPaypalDetail.findByOrFail('paymentMethodId', paymentMethodId);
        paypalDetail.paypalEmail = payload.paypalEmail;
        paypalDetail.legalName = payload.legalName;
        paypalDetail.countryCode = payload.countryCode;
        await paypalDetail.save();
        break;

      case EPaymentType.DIRECT_DEPOSIT:
        if (!payload.accountName || !payload.routingNumber || !payload.accountNumber || !payload.accountType) {
          throw new Error('Direct deposit information is not fully provided');
        }
        const directDepositDetail = await ZnDirectDepositDetail.findByOrFail('paymentMethodId', paymentMethodId);
        directDepositDetail.accountName = payload.accountName;
        directDepositDetail.routingNumber = payload.routingNumber;
        directDepositDetail.accountNumber = payload.accountNumber;
        directDepositDetail.accountType = payload.accountType;
        directDepositDetail.bankName = payload.bankName ?? '';
        directDepositDetail.accountHolderAddress = payload.accountHolderAddress ?? '';
        await directDepositDetail.save();
        break;

      case EPaymentType.ACH_TRANSFER:
        if (!payload.accountName || !payload.routingNumber || !payload.accountNumber || !payload.accountType) {
          throw new Error('ACH Transfer information was not fully provided');
        }
        const achTransferDetail = await ZnAchTransferDetail.findByOrFail('paymentMethodId', paymentMethodId);
        achTransferDetail.accountName = payload.accountName;
        achTransferDetail.routingNumber = payload.routingNumber;
        achTransferDetail.accountNumber = payload.accountNumber;
        achTransferDetail.accountType = payload.accountType;
        await achTransferDetail.save();
        break;

      default:
        if (!payload.note) {
          throw new Error('Additional information was not provided');
        }
        const otherPaymentDetail = await ZnOtherPaymentDetail.findByOrFail('paymentMethodId', paymentMethodId);
        otherPaymentDetail.paymentMethodId = paymentMethod.id;
        otherPaymentDetail.note = payload.note;
        await otherPaymentDetail.save();
        break;
    }

    const newIsDefaultStatus = (payload.isDefault !== null && payload.isDefault !== undefined) ? payload.isDefault : false
    if (paymentMethod.isDefault !== newIsDefaultStatus) {
      if (newIsDefaultStatus)
        await this.setDefaultMethod(paymentMethodId);
      else
        await this.unsetDefaultMethod(paymentMethodId);
    }

    if (paymentMethod.paymentType == EPaymentType.PAYPAL)
      await paymentMethod.load('paypalDetail');
    else if (paymentMethod.paymentType == EPaymentType.DIRECT_DEPOSIT)
      await paymentMethod.load('directDepositDetail');
    else if (paymentMethod.paymentType == EPaymentType.ACH_TRANSFER)
      await paymentMethod.load('achTransferDetail');
    else
      await paymentMethod.load('otherPaymentDetail')
    logger.info('Updated payment method: %s', JSON.stringify(paymentMethod, null, 2));
    return paymentMethod;
  }

  async setDefaultMethod(paymentMethodId: string) {
    const targetPaymentMethod = await ZnPaymentMethod.query()
      .where('id', paymentMethodId)
      .preload('vendors')
      .firstOrFail();

    if (targetPaymentMethod.vendors.length === 0) {
      targetPaymentMethod.isDefault = true;
      await targetPaymentMethod.save();
      return targetPaymentMethod;
    }

    const allMethods = await ZnPaymentMethod.query()
      .whereHas('vendors', (query) => {
        query.where('id', targetPaymentMethod.vendors[0].id);
      })
      .preload('paypalDetail')
      .preload('directDepositDetail')
      .preload('achTransferDetail')
      .preload('otherPaymentDetail');

    for (const method of allMethods) {
      method.isDefault = (method.id == paymentMethodId);
      await method.save();
    }

    return allMethods;
  }

  async unsetDefaultMethod(paymentMethodId: string) {
    const targetPaymentMethod = await ZnPaymentMethod.query()
      .where('id', paymentMethodId)
      .preload('vendors')
      .firstOrFail();

    if (targetPaymentMethod.vendors.length === 0) {
      targetPaymentMethod.isDefault = true;
      await targetPaymentMethod.save();
      return targetPaymentMethod;
    }

    const anotherMethod = await ZnPaymentMethod.query()
      .whereHas('vendors', (query) => {
        query.where('id', targetPaymentMethod.vendors[0].id);
      })
      .whereNot('id', paymentMethodId)
      .preload('paypalDetail')
      .preload('directDepositDetail')
      .preload('achTransferDetail')
      .preload('otherPaymentDetail')
      .first();

    if (anotherMethod) {
      anotherMethod.isDefault = true;
      await anotherMethod.save();

      targetPaymentMethod.isDefault = false;
      await targetPaymentMethod.save();
    }

    return anotherMethod;
  }

  async delete(paymentMethodId: string) {
    const paymentMethod = await ZnPaymentMethod.query()
      .where('id', paymentMethodId)
      .preload('vendors')
      .firstOrFail();

    if (paymentMethod.isDefault && paymentMethod.vendors) {
      const anotherMethod = await ZnPaymentMethod
        .query()
        .whereNot('paymentType', paymentMethod.paymentType)
        .whereHas('vendors', (query) => {
          query.where('id', paymentMethod.vendors[0].id);
        })
        .first();
      if (anotherMethod) {
        anotherMethod.isDefault = true;
        await anotherMethod.save();
        logger.info(`Set payment method ${anotherMethod.paymentType} as default method.`);
      }
    }

    await paymentMethod.softDelete();

    const message = `Payment method ${paymentMethod.paymentType} has been deleted successfully.`
    logger.info(message)
    return message;
  }
}