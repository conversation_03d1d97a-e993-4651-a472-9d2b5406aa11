import { EApprovalStatus } from "#constants/approval_status";
import ZnOrder from "#models/zn_order";
import ZnOrderDetail from "#models/zn_order_detail";
import ZnVendor from "#models/zn_vendor";
import ZnVendorEarning from "#models/zn_vendor_earning";
import ZnVendorEarningDetail from "#models/zn_vendor_earning_detail";
import ZnVendorPayment from "#models/zn_vendor_payment";
import logger from "@adonisjs/core/services/logger";
import VendorNotificationService from "./vendor_notification_service.js";

export default class VendorPaymentService {
  private vendorNotificationService: VendorNotificationService;

  constructor() {
    this.vendorNotificationService = new VendorNotificationService();
  }

  async getAllPayments(
    page: number = 1,
    limit: number = 10,
  ): Promise<ZnVendorPayment[]> {
    const query = ZnVendorPayment.query()
      .preload('vendor')
      .preload('paymentMethod')
      .orderBy('createdAt', 'desc');

    return query.paginate(page, limit);
  }
  async getAllPaymentsByVendor(
    vendorId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<ZnVendorPayment[]> {
    const query = ZnVendorPayment.query()
      .where('vendorId', vendorId)
      .preload('vendor')
      .preload('paymentMethod', (query) => {
        query.withScopes((scopes) => scopes.withAll())
      })
      .orderBy('createdAt', 'desc');

    return query.paginate(page, limit);
  }

  async getPaymentById(id: string): Promise<ZnVendorPayment> {
    const payment = await ZnVendorPayment.query()
      .where('id', id)
      .preload('vendor')
      .preload('paymentMethod', (query) => {
        query.withScopes((scopes) => scopes.withAll())
      })
      .firstOrFail();
    return payment;
  }

  async create(vendorId: string, data: Partial<ZnVendorPayment>): Promise<ZnVendorPayment> {
    const payment = await ZnVendorPayment.create({
      amount: data.amount,
      note: data.note,
      vendorId,
      paymentMethodId: data.paymentMethodId,
    });

    await this.vendorNotificationService.sendPaymentNotification(payment);

    return payment;
  }

  async update(id: string, data: Partial<ZnVendorEarning>): Promise<ZnVendorEarning> {
    const commission = await ZnVendorEarning.query().where('id', id).firstOrFail();
    commission.merge(data);
    await commission.save();

    await commission.load('order');
    await commission.load('vendor');
    return commission;
  }

  async delete(id: string) {
    const commission = await ZnVendorEarning.find(id);
    if (!commission) return;
    await commission.softDelete();
  }

  async calculateEarning(shopifyOrder: any): Promise<void> {
    const order = await ZnOrder.query()
      .where('shopifyId', shopifyOrder.admin_graphql_api_id)
      .first();

    if (!order) {
      logger.debug("No order provided for commission calculation.");
      return;
    }

    const orderDetails = await ZnOrderDetail.query()
      .where('orderId', order.id)
      .preload('variant', (query) => {
        query.preload('product', (productQuery) => {
          productQuery.preload('vendor');
        });
      });

    const orderDetailsByVendor: Record<string, ZnOrderDetail[]> = {};
    for (const orderDetail of orderDetails) {
      const vendorId = orderDetail.variant?.product?.vendor?.id;
      if (vendorId) {
        if (!orderDetailsByVendor[vendorId]) {
          orderDetailsByVendor[vendorId] = [];
        }
        orderDetailsByVendor[vendorId].push(orderDetail);
      }
    }

    for (const vendorId in orderDetailsByVendor) {
      const details = orderDetailsByVendor[vendorId];
      await this.createVendorEarning(vendorId, order.id, details);
    }
  }

  private async createVendorEarning(vendorId: string, orderId: string, orderDetails: ZnOrderDetail[]): Promise<void> {
    const vendor = await ZnVendor.query().where('id', vendorId).firstOrFail();
    if (vendor.registrationStatus !== EApprovalStatus.APPROVED) return;

    const earningData = {
      status: EApprovalStatus.PENDING,
      vendorId,
      orderId,
    };

    const earningDetailsData = [];
    let totalEarnedAmount = 0;

    for (const orderDetail of orderDetails) {
      const itemSubTotal = orderDetail.price * orderDetail.quantity;
      const itemSubTotalAfterDiscounted = itemSubTotal;
      const earnedAmount = itemSubTotalAfterDiscounted * (1 - vendor.commissionRate) - vendor.fixedCommissionAmount;

      if (earnedAmount > 0) {
        earningDetailsData.push({
          commissionRate: vendor.commissionRate,
          fixedCommissionAmount: vendor.fixedCommissionAmount,
          earnedAmount,
          orderDetailId: orderDetail.id
        });

        totalEarnedAmount += earnedAmount;
      }
    }

    if (totalEarnedAmount > 0) {
      const earning = await ZnVendorEarning.create({
        ...earningData,
        earnedAmount: totalEarnedAmount,
      });

      for (const earningDetailData of earningDetailsData) {
        await ZnVendorEarningDetail.create({
          ...earningDetailData,
          earningId: earning.id,
        });
      }

      logger.info(`Vendor earning created for vendor ${vendor.companyName} with total amount: ${totalEarnedAmount}`);
    }
  }
}
