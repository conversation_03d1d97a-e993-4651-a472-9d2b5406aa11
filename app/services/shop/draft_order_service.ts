import ZnAddress from '#models/zn_address'
import ZnDraftOrder from '#models/zn_draft_order'
import ZnDraftOrderDetail from '#models/zn_draft_order_detail'
import ZnDraftOrderDiscount from '#models/zn_draft_order_discount'
import ZnProductVariant from '#models/zn_product_variant'
import ZnUser from '#models/zn_user'
import { CartService } from '#services/shopify/cart/cart_service'
import { IAddress, ICustomAttribute } from '#services/shopify/order/draft_order'
import { ShopifyDraftOrderService } from '#services/shopify/order/draft_order_service'
import { ShopifyService } from '#services/shopify/shopify_service'
import { getShopifyCustomerId } from '../../../services/commons.js'
import { GiftService } from './gift_service.js'

export class DraftOrderService {
  // Create Draft Order
  async createDraftOrder(user: ZnUser, payload: ICreateDraftOrderData) {
    const cartService = new CartService()
    let shippingAddressId = payload.shippingAddressId
    if (!shippingAddressId) {
      if (user.defaultAddressId) {
        shippingAddressId = user.defaultAddressId
      } else {
        const address = await ZnAddress.query().where('userId', user.id).first()
        shippingAddressId = address?.id
      }
    }

    if (!shippingAddressId) {
      throw new Error('Shipping address not found')
    }
    const lineItems = await cartService.getLineItems({ lineItems: payload.items })
    const cartSectionIds = (payload.items.map((i) => i.cartSectionId).filter(Boolean) ||
      []) as string[]

    // calculator draft order with Shopify
    const { calculatorDraftOrder, shippingRateHandle: shippingRateHandleDefault } =
      await this.calculatorShopifyOrder({
        user,
        lineItems,
        discountCodes: payload.discountCodes || [],
        addressId: shippingAddressId,
      })

    let shippingRateHandle = shippingRateHandleDefault
    let totalShipping = Number(calculatorDraftOrder.totalShippingPriceSet?.shopMoney?.amount || 0)
    if (calculatorDraftOrder.availableShippingRates?.length > 0) {
      const shipping = calculatorDraftOrder.availableShippingRates[0]
      shippingRateHandle = shipping.handle
      totalShipping = Number(shipping.price?.amount || 0)
    }

    const discount = calculatorDraftOrder.platformDiscounts.reduce((acc: any, discount: any) => {
      return acc + Number(discount.totalAmount.amount)
    }, 0)
    // Create Zurno draft order
    const draftOrder = await ZnDraftOrder.create({
      shippingAddressId,
      userId: user.id,
      billingAddressId: shippingAddressId,
      note: payload.note,
      os: payload.os,
      status: 'draft',
      appVersion: payload.appVersion,
      paymentMethod: payload.paymentMethod,
      totalDiscount: Number(discount || 0),
      totalPrice: Number(calculatorDraftOrder.totalPriceSet?.shopMoney?.amount || 0),
      totalShipping,
      totalTax: Number(calculatorDraftOrder.totalTaxSet?.shopMoney?.amount || 0),
      subTotalPrice: Number(calculatorDraftOrder.subtotalPriceSet?.shopMoney?.amount || 0),
      shippingRateHandle,
      // @ts-ignore
      cartSectionIds: JSON.stringify(cartSectionIds),
    })

    // Create Discount
    for (const platformDiscount of calculatorDraftOrder.platformDiscounts) {
      await this.createDiscount(draftOrder, platformDiscount)
    }
    // Create Order Detail
    for (const lineItem of calculatorDraftOrder.lineItems) {
      await this.createOrderDetail(draftOrder, lineItem)
    }

    return draftOrder
  }

  // Create Discount
  private async createDiscount(draftOrder: ZnDraftOrder, platformDiscount: any) {
    await ZnDraftOrderDiscount.create({
      draftOrderId: draftOrder.id,
      discountType: platformDiscount.automaticDiscount ? 'automatic' : 'code',
      discountCode: platformDiscount.code,
      amount: Number(platformDiscount.totalAmount.amount),
      description: platformDiscount.summary,
      title: platformDiscount.title,
    })
  }

  // Create Order Detail
  private async createOrderDetail(draftOrder: ZnDraftOrder, lineItem: any) {
    const variant = await ZnProductVariant.query()
      .where('shopifyVariantId', lineItem.variant.id)
      .first()
    if (!variant) {
      return
    }
    const fastBundleDiscountId = lineItem.customAttributes?.find(
      (attribute: ICustomAttribute) => attribute.key === 'fastBundleDiscountId'
    )?.value
    const quantity = lineItem.quantity
    const price = Number(lineItem.originalTotal?.amount || 0)
    const discount = Number(lineItem.totalDiscount?.amount || 0)
    const amount = price * quantity - discount

    await ZnDraftOrderDetail.create({
      draftOrderId: draftOrder.id,
      variantId: variant.id,
      quantity,
      price,
      title: lineItem.title,
      variantTitle: lineItem.variantTitle,
      sku: lineItem.sku,
      amount,
      imageUrl: lineItem.image?.url || null,
      discount,
      fastBundleDiscountId: fastBundleDiscountId ? Number(fastBundleDiscountId) : null,
      giftId:
        lineItem.customAttributes?.find((attribute: ICustomAttribute) => attribute.key === 'giftId')
          ?.value || null,
    })
  }

  // Get Draft Order By Id
  async getDraftOrderById(id: string) {
    return ZnDraftOrder.query()
      .where('id', id)
      .preload('details')
      .preload('discounts')
      .preload('shippingAddress')
      .first()
  }

  // Update Draft Order Note
  async updateDraftOrderNote(order: ZnDraftOrder, note: string) {
    await order.merge({ note }).save()

    await order.refresh()
    return order
  }

  // Update Draft Order Discount Codes
  async updateDraftOrderDiscountCodes(user: ZnUser, order: ZnDraftOrder, discountCodes: string[]) {
    const { calculatorDraftOrder } = await this.calculatorShopifyOrderByDraftOrder({
      user,
      order,
      discountCodes,
    })

    await ZnDraftOrderDiscount.query().where({ draftOrderId: order.id }).delete()
    for (const platformDiscount of calculatorDraftOrder.platformDiscounts) {
      await this.createDiscount(order, platformDiscount)
    }
    const totalDiscount = calculatorDraftOrder.platformDiscounts.reduce(
      (acc: any, discount: any) => {
        return acc + Number(discount.totalAmount.amount)
      },
      0
    )
    await order
      .merge({
        totalDiscount,
        totalPrice: Number(calculatorDraftOrder.totalPriceSet?.shopMoney?.amount || 0),
        totalShipping: Number(calculatorDraftOrder.totalShippingPriceSet?.shopMoney?.amount || 0),
        totalTax: Number(calculatorDraftOrder.totalTaxSet?.shopMoney?.amount || 0),
        subTotalPrice: Number(calculatorDraftOrder.subtotalPriceSet?.shopMoney?.amount || 0),
      })
      .save()

    await order.load('discounts')
    return order
  }

  // Update Draft Order Discount Codes
  async updateDraftOrderRemoveItems(user: ZnUser, order: ZnDraftOrder, removeItemId: string) {
    // Get Line Items
    const details = await ZnDraftOrderDetail.query()
      .where('draftOrderId', order.id)
      .whereNot('id', removeItemId)
      .preload('variant')

    const items = details.map((detail) => ({
      variantId: detail.variant.shopifyVariantId,
      quantity: detail.quantity,
      fastBundleDiscountId: detail.fastBundleDiscountId || undefined,
    }))
    const { calculatorDraftOrder, shippingRateHandle } =
      await this.calculatorShopifyOrderByDraftOrder({
        user,
        order,
        items,
      })

    // Remove Item
    await ZnDraftOrderDetail.query()
      .where('draftOrderId', order.id)
      .where('id', removeItemId)
      .delete()

    // Update Order
    await order
      .merge({
        totalPrice: Number(calculatorDraftOrder.totalPriceSet?.shopMoney?.amount || 0),
        totalDiscount: Number(calculatorDraftOrder.totalDiscountsSet?.shopMoney?.amount || 0),
        totalShipping: Number(calculatorDraftOrder.totalShippingPriceSet?.shopMoney?.amount || 0),
        totalTax: Number(calculatorDraftOrder.totalTaxSet?.shopMoney?.amount || 0),
        subTotalPrice: Number(calculatorDraftOrder.subtotalPriceSet?.shopMoney?.amount || 0),
        shippingRateHandle: order.shippingRateHandle || shippingRateHandle,
      })
      .save()

    await order.load('details')
    return order
  }

  // Update Draft Order Address
  async updateDraftOrderAddress(user: ZnUser, order: ZnDraftOrder, shippingAddressId: string) {
    const { calculatorDraftOrder } = await this.calculatorShopifyOrderByDraftOrder({
      user,
      order,
      shippingAddressId,
    })

    if (calculatorDraftOrder) {
      await order.merge({ shippingAddressId }).save()
    }

    await order.load('shippingAddress')
    return order
  }

  // Calculator Shopify Order By Draft Order
  async calculatorShopifyOrderByDraftOrder({
    user,
    order,
    shippingAddressId,
    discountCodes: defaultDiscountCodes,
    items: defaultItems,
  }: {
    user: ZnUser
    order: ZnDraftOrder
    shippingAddressId?: string
    discountCodes?: string[]
    items?: ILineItem[]
  }) {
    const cartService = new CartService()

    // Get Shipping Address
    const addressId = (shippingAddressId || order.shippingAddressId) as string

    // Get discount Codes
    let codes = defaultDiscountCodes || []
    if (!defaultDiscountCodes) {
      codes = order.discounts.map((discount) => discount.discountCode).filter(Boolean) as string[]
    }

    // Get Line Items
    let items = defaultItems
    if (!defaultItems) {
      await order.load('details', (query) => query.preload('variant'))
      items = order.details.map((detail) => ({
        variantId: detail.variant.id,
        quantity: detail.quantity,
        fastBundleDiscountId: detail.fastBundleDiscountId || undefined,
      }))
    }
    const lineItems = await cartService.getLineItems({ lineItems: items as ILineItem[] })
    // Calculator Shopify Order
    const result = await this.calculatorShopifyOrder({
      user,
      shippingRateHandle: order.shippingRateHandle,
      addressId,
      discountCodes: codes,
      lineItems,
    })

    if (!order.shippingRateHandle) {
      await order.merge({ shippingRateHandle: result.shippingRateHandle }).save()
    }
    return result
  }

  // Calculator Shopify Order
  async calculatorShopifyOrder({
    user,
    lineItems,
    discountCodes,
    addressId,
    shippingRateHandle: defaultShippingRateHandle,
  }: {
    user: ZnUser
    lineItems: ILineItem[]
    discountCodes: string[]
    addressId: string
    shippingRateHandle?: string
  }): Promise<{ calculatorDraftOrder: any; shippingRateHandle?: string }> {
    const address = await ZnAddress.find(addressId)
    if (!address) {
      throw new Error('Shipping address not found')
    }

    const shippingAddress: IAddress = {
      address1: address.address1,
      address2: address.address2,
      city: address.city,
      country: address.country,
      province: address.province,
      zip: address.zip,
    }
    let input = {
      customerId: user.shopifyCustomerId ? getShopifyCustomerId(user.shopifyCustomerId) : null,
      email: user.email,
      lineItems,
      discountCodes,
      shippingAddress,
    } as any
    const shopifyService = new ShopifyService()

    // Get Shipping Rate Handle (Delivery Option)
    let shippingRateHandle = defaultShippingRateHandle
    if (!shippingRateHandle) {
      const availableDeliveryOptions = await shopifyService.getAvailableDeliveryOptions({
        lineItems,
        shippingAddress,
      })
      shippingRateHandle = availableDeliveryOptions?.handle
    }
    if (shippingRateHandle) {
      input.shippingLine = {
        shippingRateHandle,
      }
    }

    const resultCalculator = await shopifyService.calculatorDraftOrder(input)
    if (resultCalculator.error) {
      throw new Error(resultCalculator.error[0].message)
    }
    let calculatorDraftOrder = resultCalculator.data

    const totalPrice = Number(calculatorDraftOrder.totalPriceSet?.shopMoney?.amount || 0)
    const giftService = new GiftService()
    const gift = await giftService.getGiftVariantsForCheckout({
      userId: user.id,
      amount: totalPrice,
      variantIds: lineItems.map((item) => item.variantId),
    })

    if (gift) {
      const draftOrderService = new ShopifyDraftOrderService()
      input = await draftOrderService.applyGift({
        input,
        gift,
      })
      const resultCalculator2 = await shopifyService.calculatorDraftOrder(input)
      if (resultCalculator2.error) {
        throw new Error(resultCalculator2.error[0].message)
      }
      calculatorDraftOrder = resultCalculator2.data
    }

    return { calculatorDraftOrder, shippingRateHandle }
  }
}
