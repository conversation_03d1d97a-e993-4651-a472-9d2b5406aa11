import { IAddress, ICustomAttribute } from './draft_order.js'

export interface IPriceSet {
  shopMoney: {
    amount: number
    currencyCode: string
  }
}

export interface IOrderCreate {
  customerId?: string | null
  email?: string | null
  note?: string | null
  lineItems: ILineItem[]
  tags?: string[]
  shippingAddress?: IAddress
  billingAddress: IAddress
  customAttributes: ICustomAttribute[]
  shippingLine?: {
    shippingRateHandle?: string
    title?: string
    price?: number
  }
  amount: number,
}
