import edge from 'edge.js'
import puppeteer from 'puppeteer'
import { AmazonS3StorageService } from '../../../services/aws/s3/aws-s3.service.js'
import ZnMedia from '#models/zn_media'
import { MEDIA_TYPE } from '../../constants/media.js'
import type ZnSalonConstructionServiceSignup from '#models/zn_salon_construction_service_signup'
import { DateTime } from 'luxon'
import fs from 'fs'
import path from 'path'

export class SalonConstructionServicePdfGenerationService {
  private s3Service: AmazonS3StorageService

  constructor() {
    this.s3Service = new AmazonS3StorageService()
  }

  /**
   * Load logo as base64
   */
  private async loadLogoAsBase64(): Promise<string> {
    try {
      const logoPath = path.join(process.cwd(), 'public', 'images', 'logo.png')
      const logoBuffer = fs.readFileSync(logoPath)
      return logoBuffer.toString('base64')
    } catch (error) {
      console.warn('Logo not found, using empty string:', error)
      return ''
    }
  }

  /**
   * Generate PDF from salon construction service signup data
   */
  async generatePdf(signup: ZnSalonConstructionServiceSignup): Promise<ZnMedia> {
    try {
      const logoBase64 = await this.loadLogoAsBase64()

      const templateData = {
        fullName: signup.fullName,
        businessName: signup.businessName,
        salonAddress: signup.salonAddress,
        phoneNumber: signup.phoneNumber,
        emailAddress: signup.emailAddress,
        serviceInterest: signup.serviceInterest,
        preferredStartDate: signup.preferredStartDate?.toFormat('MM/dd/yyyy') || '',
        budgetRange: signup.budgetRange,
        additionalNotes: signup.additionalNotes,
        signature: signup.signature,
        signatureDate:
          signup.signatureDate?.toFormat('MM/dd/yyyy') || DateTime.now().toFormat('MM/dd/yyyy'),
        consentConfirmed: signup.consentConfirmed,
        logoBase64,
      }

      const html = await edge.render('pdf/salon_construction_service_signup', {
        data: templateData,
      })

      const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      })

      const page = await browser.newPage()
      await page.setContent(html, { waitUntil: 'networkidle0' })

      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: {
          top: '15px',
          right: '15px',
          bottom: '15px',
          left: '15px',
        },
        preferCSSPageSize: true,
        scale: 0.8,
      })

      await browser.close()

      const fileName = `salon-construction-signup-${signup.id}-${Date.now()}.pdf`

      const uploadResult = await this.s3Service.uploadPdf({
        fileName,
        buffer: Buffer.from(pdfBuffer),
        mimeType: 'application/pdf',
      })

      if (!uploadResult.fileKey) {
        throw new Error('Failed to upload PDF to S3')
      }

      const media = await ZnMedia.create({
        fileKey: uploadResult.fileKey,
        url: uploadResult.fileUrl,
        type: MEDIA_TYPE.DOC,
        resourceId: signup.id,
        sourceFrom: 'salon_construction_service',
      })

      return media
    } catch (error) {
      console.error('Error generating PDF:', error)
      throw new Error('Failed to generate PDF')
    }
  }

  /**
   * Generate PDF preview (for testing)
   */
  async generatePdfPreview(data: any): Promise<Buffer> {
    try {
      // Load logo
      const logoBase64 = await this.loadLogoAsBase64()

      const html = await edge.render('pdf/salon_construction_service_signup', {
        data: {
          ...data,
          logoBase64,
        },
      })

      const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
      })

      const page = await browser.newPage()
      await page.setContent(html, { waitUntil: 'networkidle0' })

      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: {
          top: '15px',
          right: '15px',
          bottom: '15px',
          left: '15px',
        },
        preferCSSPageSize: true,
        scale: 0.8,
      })

      await browser.close()
      return Buffer.from(pdfBuffer)
    } catch (error) {
      console.error('Error generating PDF preview:', error)
      throw new Error('Failed to generate PDF preview')
    }
  }
}
