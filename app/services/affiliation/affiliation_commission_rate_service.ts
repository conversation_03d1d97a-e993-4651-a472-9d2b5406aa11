import ZnAffiliateCommissionRate from "#models/zn_affiliate_commission_rate";
import ZnAffiliateTierCommissionGroup from "#models/zn_affiliate_tier_commission_group";

export default class AffiliationCommissionRateService {
  async create(commissionGroupId: string, data: Partial<ZnAffiliateCommissionRate>): Promise<ZnAffiliateCommissionRate> {
    const commissionGroup = await ZnAffiliateTierCommissionGroup.findOrFail(commissionGroupId);

    return await ZnAffiliateCommissionRate.create({
      ...data,
      commissionGroupId: commissionGroup.id
    });
  }

  async findById(id: number): Promise<ZnAffiliateCommissionRate | null> {
    return await ZnAffiliateCommissionRate.findOrFail(id);
  }

  async findAllByCommissionGroupId(commissionGroupId: string): Promise<ZnAffiliateCommissionRate[]> {
    return await ZnAffiliateCommissionRate.query()
      .where('commissionGroupId', commissionGroupId)
      .orderBy('revenueFrom', 'asc');
  }

  async update(id: number, data: Partial<ZnAffiliateCommissionRate>): Promise<ZnAffiliateCommissionRate> {
    const commissionRate = await ZnAffiliateCommissionRate.findOrFail(id);
    commissionRate.merge(data);
    return await commissionRate.save();
  }

  async delete(id: number): Promise<void> {
    const commissionRate = await ZnAffiliateCommissionRate.findOrFail(id);
    await commissionRate.softDelete();
  }
}