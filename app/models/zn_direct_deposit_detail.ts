import { belongsTo, column } from '@adonisjs/lucid/orm'
import AppModel from './app_model.js';
import type { BelongsTo } from '@adonisjs/lucid/types/relations';
import ZnPaymentMethod from './zn_payment_method.js';

export default class ZnDirectDepositDetail extends AppModel {
  static table = 'zn_direct_deposit_details'

  @column({ columnName: 'routingNumber' })
  declare routingNumber: string

  @column({ columnName: 'accountName' })
  declare accountName: string

  @column({ columnName: 'accountNumber' })
  declare accountNumber: string

  @column({ columnName: 'accountType' })
  // @no-swagger
  declare accountType: 'CHECKING' | 'SAVINGS'

  @column({ columnName: 'bankName' })
  declare bankName?: string

  @column({ columnName: 'accountHolderAddress' })
  declare accountHolderAddress?: string

  @column({ columnName: 'paymentMethodId' })
  declare paymentMethodId: string

  @belongsTo(() => ZnPaymentMethod, {
    foreignKey: 'paymentMethodId'
  })
  declare paymentMethod: BelongsTo<typeof ZnPaymentMethod>
}