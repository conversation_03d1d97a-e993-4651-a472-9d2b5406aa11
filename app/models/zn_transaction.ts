import { column } from '@adonisjs/lucid/orm'
import AppModel from './app_model.js'

export default class ZnTransaction extends AppModel {
  @column({ columnName: 'source' })
  declare source: string

  @column({ columnName: 'sourceId' })
  declare sourceId: string

  @column({ columnName: 'accountNumber' })
  declare accountNumber: string | null

  @column({ columnName: 'accountType' })
  declare accountType: string | null

  @column({ columnName: 'paymentId' })
  declare paymentId: string

  @column({ columnName: 'paymentStatus' })
  declare paymentStatus: string

  @column({ columnName: 'status' })
  declare status: string

  @column({ columnName: 'amount' })
  declare amount: number

  @column({ columnName: 'currency' })
  declare currency: string

  @column({ columnName: 'orderId' })
  declare orderId: string | null

  @column({ columnName: 'draftOrderId' })
  declare draftOrderId: string | null

  @column({ columnName: 'sourceCreatedAt' })
  declare sourceCreatedAt: string | null

  @column({ columnName: 'sourceUpdatedAt' })
  declare sourceUpdatedAt: string | null

  @column({ columnName: 'payerId' })
  declare payerId: string

  @column({ columnName: 'payerName' })
  declare payerName: string

  @column({ columnName: 'payerEmail' })
  declare payerEmail: string | null

  @column({ columnName: 'payerPhone' })
  declare payerPhone: string | null

  @column({ columnName: 'responseId' })
  declare responseId: string | null

  @column({ columnName: 'refTransactionId' })
  declare refTransactionId: string | null
}
