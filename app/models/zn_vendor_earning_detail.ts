import { belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations';
import AppModel from './app_model.js';
import ZnVendorEarning from './zn_vendor_earning.js';
import ZnOrderDetail from './zn_order_detail.js';

export default class ZnVendorEarningDetail extends AppModel {
  @column({
    columnName: 'commissionRate',
    consume: (value: string) => parseFloat(value)
  })
  declare commissionRate: number

  @column({
    columnName: 'fixedCommissionAmount',
    consume: (value: string) => parseFloat(value)
  })
  declare fixedCommissionAmount: number

  @column({
    columnName: 'earnedAmount',
    consume: (value: string) => parseFloat(value)
  })
  declare earnedAmount: number

  @column({ columnName: 'earningId' })
  declare earningId: string

  @column({ columnName: 'orderDetailId' })
  declare orderDetailId: string

  @belongsTo(() => ZnVendorEarning, {
    foreignKey: 'earningId'
  })
  declare earning: BelongsTo<typeof ZnVendorEarning>

  @belongsTo(() => ZnOrderDetail, {
    foreignKey: 'orderDetailId'
  })
  declare orderDetail: BelongsTo<typeof ZnOrderDetail>
}