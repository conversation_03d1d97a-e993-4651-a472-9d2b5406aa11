import { column } from '@adonisjs/lucid/orm'
import AppModel from './app_model.js'

export default class ZnDraftOrderDiscount extends AppModel {
  @column({ columnName: 'draftOrderId' })
  declare draftOrderId: string

  @column({ columnName: 'discountCode' })
  declare discountCode?: string

  @column({ columnName: 'discountType' })
  declare discountType?: string

  @column({ columnName: 'amount' })
  declare amount: number

  @column({ columnName: 'percentage' })
  declare percentage: number

  @column({ columnName: 'title' })
  declare title?: string

  @column({ columnName: 'description' })
  declare description?: string
}
