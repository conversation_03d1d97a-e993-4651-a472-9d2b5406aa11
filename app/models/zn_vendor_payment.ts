import { belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations';
import AppModel from './app_model.js';
import ZnPaymentMethod from './zn_payment_method.js';
import ZnVendor from './zn_vendor.js';

export default class ZnVendorPayment extends AppModel {
  static table = 'zn_vendor_payments';

  @column({
    columnName: 'amount',
    consume: (value: string) => parseFloat(value)
  })
  declare amount: number

  @column({ columnName: 'vendorId' })
  declare vendorId: string

  @column({ columnName: 'note' })
  declare note: string

  @column({ columnName: 'paymentMethodId' })
  declare paymentMethodId: string

  @belongsTo(() => ZnVendor, {
    foreignKey: 'vendorId'
  })
  declare vendor: BelongsTo<typeof ZnVendor>

  @belongsTo(() => ZnPaymentMethod, {
    foreignKey: 'paymentMethodId'
  })
  declare paymentMethod: BelongsTo<typeof ZnPaymentMethod>
}
