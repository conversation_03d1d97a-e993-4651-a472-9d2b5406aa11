import { belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import AppModel from './app_model.js'
import ZnProductVariant from './zn_product_variant.js'

export default class ZnDraftOrderDetail extends AppModel {
  @column({ columnName: 'draftOrderId' })
  declare draftOrderId: string

  @column({ columnName: 'title' })
  declare title: string

  @column({ columnName: 'amount' })
  declare amount: number

  @column({ columnName: 'variantId' })
  declare variantId: string

  @column({ columnName: 'variantTitle' })
  declare variantTitle: string | null

  @column({ columnName: 'giftId' })
  declare giftId: string | null

  @column({ columnName: 'sku' })
  declare sku: string | null

  @column({ columnName: 'imageUrl' })
  declare imageUrl: string | null

  @column({ columnName: 'quantity' })
  declare quantity: number

  @column({ columnName: 'price' })
  declare price: number

  @column({ columnName: 'tax' })
  declare tax: number

  @column({ columnName: 'discount' })
  declare discount: number

  @column({ columnName: 'fastBundleDiscountId' })
  declare fastBundleDiscountId?: number | null

  @belongsTo(() => ZnProductVariant, {
    foreignKey: 'variantId',
  })
  declare variant: BelongsTo<typeof ZnProductVariant>
}
