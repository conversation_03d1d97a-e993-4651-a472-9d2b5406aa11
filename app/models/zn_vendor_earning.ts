import { belongsTo, column, computed, hasMany } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations';
import AppModel from './app_model.js';
import { EApprovalStatus } from '#constants/approval_status';
import ZnOrder from './zn_order.js';
import ZnVendor from './zn_vendor.js';
import ZnVendorEarningDetail from './zn_vendor_earning_detail.js';

export default class ZnVendorEarning extends AppModel {
  @column({
    columnName: 'earnedAmount',
    consume: (value: string) => parseFloat(value)
  })
  declare earnedAmount: number

  @column({ columnName: 'status' })
  declare status: string | EApprovalStatus.PENDING

  @column({ columnName: 'rejectionReason' })
  declare rejectionReason: string | null

  @column({
    columnName: 'adjustedAmount',
    consume: (value: string) => parseFloat(value)
  })
  declare adjustedAmount: number

  @column({ columnName: 'adjustedReason' })
  declare adjustedReason: string

  @column({ columnName: 'vendorId' })
  declare vendorId: string

  @column({ columnName: 'orderId' })
  declare orderId: string

  @computed()
  get finalAmount() {
    if (this.adjustedAmount !== null && this.adjustedAmount > 0)
      return this.adjustedAmount
    return this.earnedAmount
  }

  @belongsTo(() => ZnVendor, {
    foreignKey: 'vendorId'
  })
  declare vendor: BelongsTo<typeof ZnVendor>

  @belongsTo(() => ZnOrder, {
    foreignKey: 'orderId'
  })
  declare order: BelongsTo<typeof ZnOrder>

  @hasMany(() => ZnVendorEarningDetail, {
    foreignKey: 'earningId'
  })
  declare earningDetails: HasMany<typeof ZnVendorEarningDetail>
}