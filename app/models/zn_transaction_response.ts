import { BaseModel, column } from '@adonisjs/lucid/orm'
import { DateTime } from 'luxon'

export default class ZnTransactionResponse extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column({ columnName: 'response' })
  declare response: string

  @column.dateTime({ autoCreate: true, columnName: 'createdAt' })
  declare createdAt: DateTime

  // @column.dateTime({ autoCreate: true, autoUpdate: true, columnName: 'updatedAt' })
  @column.dateTime({ autoCreate: true, columnName: 'updatedAt' })
  declare updatedAt: DateTime
}
