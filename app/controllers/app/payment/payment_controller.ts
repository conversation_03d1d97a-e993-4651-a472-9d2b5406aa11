import { paymentConfig } from '#config/payment'
import ZnDraftOrder from '#models/zn_draft_order'
import { PaymentHandleService } from '#services/payment/payment_handle_service'
import env from '#start/env'
import { paymentValidator } from '#validators/app/payment/payment_validator'
import { HttpContext } from '@adonisjs/core/http'

export default class PaymentController {
  public async payment({ request, response }: HttpContext) {
    const { draftOrderId } = await request.validateUsing(paymentValidator)

    const draftOrder = await ZnDraftOrder.query()
      .where('id', draftOrderId)
      .preload('user')
      .preload('shippingAddress')
      .preload('details')
      .preload('discounts')
      .preload('order')
      .first()

    if (!draftOrder) {
      return response.badRequest({
        success: false,
        message: 'Draft order not found',
      })
    }

    if (draftOrder.status === 'completed') {
      return response.badRequest({
        success: false,
        message: 'Draft order already completed',
      })
    }

    if (draftOrder.status === 'cancelled') {
      return response.badRequest({
        success: false,
        message: 'Draft order already cancelled',
      })
    }

    if (paymentConfig.applyZurnoPayment) {
      // Apply Zurno Payment
      // Redirect to Zurno Payment Page
      const feBaseUrl = env.get('ADS_WEBSITE_DOMAIN')

      return response.ok({
        success: true,
        data: {
          url: `${feBaseUrl}/payment/${draftOrder.id}`,
          type: 'zurno',
        },
      })
    } else {
      // Create Shopify Draft Order
      // Redirect to Shopify Payment Page
      try {
        const paymentHandleService = new PaymentHandleService()
        const shopifyDraftOrder = await paymentHandleService.createShopifyDraftOrder(draftOrder)
        if (!shopifyDraftOrder) {
          return response.badRequest({
            success: false,
            message: 'Failed to create Shopify draft order',
          })
        }
        await draftOrder.merge({ shopifyDraftOrderId: shopifyDraftOrder.id }).save()

        return response.ok({
          success: true,
          data: {
            url: shopifyDraftOrder.invoiceUrl,
            type: 'shopify',
          },
        })
      } catch (error) {
        return response.badRequest({
          success: false,
          message: 'Failed to create Shopify draft order',
        })
      }
    }
  }
}
