import { TransactionSource } from '#constants/transaction'
import ZnTransaction from '#models/zn_transaction'
import ZnTransactionResponse from '#models/zn_transaction_response'
import PayPalService from '#services/payment/paypal/paypal_service'
import {
  paymentPaypalCaptureValidator,
  paymentPaypalCreateOrderValidator,
} from '#validators/app/payment/payment_paypal'
import type { HttpContext } from '@adonisjs/core/http'

export default class PayPalController {
  private paypalService = new PayPalService()

  public async createOrder({ request, response }: HttpContext) {
    try {
      const { amount, currency } = await request.validateUsing(paymentPaypalCreateOrderValidator)

      const order = await this.paypalService.createOrder(amount.toString(), currency)

      return response.ok({
        success: true,
        orderId: order.id,
        approvalUrl: order.links?.find((link) => link.rel === 'approve')?.href,
        order,
      })
    } catch (error) {
      return response.internalServerError({
        success: false,
        error: error.message,
      })
    }
  }

  public async captureOrder({ request, response }: HttpContext) {
    try {
      const { orderId } = await request.validateUsing(paymentPaypalCaptureValidator)

      if (!orderId) {
        return response.badRequest({ error: 'Order ID is required' })
      }

      const captureResult = await this.paypalService.captureOrder(orderId)

      try {
        const transactionResponse = await ZnTransactionResponse.create({
          response: captureResult,
        })

        await ZnTransaction.create({
          orderId,
          status: captureResult.status,
          amount: Number(captureResult.payments?.captures?.[0]?.amount?.value),
          currency: captureResult.payments?.captures?.[0]?.amount?.currency_code,
          source: TransactionSource.PAYPAL,
          sourceId: captureResult.id,
          paymentId: captureResult.payments?.captures?.[0]?.id,
          paymentStatus: captureResult.payments?.captures?.[0]?.status,
          sourceCreatedAt: captureResult.create_time,
          sourceUpdatedAt: captureResult.update_time,
          payerId: captureResult.payer?.payer_id,
          payerName: [captureResult.payer?.name?.given_name, captureResult.payer?.name?.surname]
            .filter(Boolean)
            .join(' '),
          payerEmail: captureResult.payer?.email_address,
          payerPhone: captureResult.payer?.phone?.phone_number?.national_number,
          responseId: transactionResponse.id.toString(),
        })
      } catch (error) {
        return response.internalServerError({
          success: false,
          error: error.message,
        })
      }

      return response.ok({
        success: true,
        captureResult,
      })
    } catch (error) {
      return response.internalServerError({
        success: false,
        error: error.message,
      })
    }
  }

  public async getOrder({ request, response }: HttpContext) {
    try {
      const { orderId } = request.params()

      const order = await this.paypalService.getOrderDetails(orderId)

      return response.ok({
        success: true,
        order,
      })
    } catch (error) {
      return response.internalServerError({
        success: false,
        error: error.message,
      })
    }
  }

  public async webhook({ request, response }: HttpContext) {
    try {
      const webhookData = request.body()

      // Handle different webhook events
      switch (webhookData.event_type) {
        case 'PAYMENT.CAPTURE.COMPLETED':
          // Handle successful payment
          console.log('Payment completed:', webhookData)
          break
        case 'PAYMENT.CAPTURE.DENIED':
          // Handle failed payment
          console.log('Payment denied:', webhookData)
          break
        default:
          console.log('Unhandled webhook event:', webhookData.event_type)
      }

      return response.ok({ received: true })
    } catch (error) {
      return response.internalServerError({
        success: false,
        error: error.message,
      })
    }
  }
}
