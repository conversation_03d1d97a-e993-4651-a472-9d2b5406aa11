import { TransactionSource, TransactionStatus } from '#constants/transaction'
import ZnCartSection from '#models/zn_cart_section'
import ZnDraftOrder from '#models/zn_draft_order'
import ZnTransaction from '#models/zn_transaction'
import ZnTransactionResponse from '#models/zn_transaction_response'
import { PaymentHandleService } from '#services/payment/payment_handle_service'
import { authorizeNetPaymentValidator } from '#validators/app/payment/payment_authorize_net'
import type { HttpContext } from '@adonisjs/core/http'
import { AuthorizeNetService } from '../../../../services/authorize-net/authorize_net_service.js'

export default class AuthorizeNetController {
  private authorizeNetService = new AuthorizeNetService()
  private paymentHandleService = new PaymentHandleService()

  public async processPayment({ request, response }: HttpContext) {
    const payload = await request.validateUsing(authorizeNetPaymentValidator)
    const draftOrder = await ZnDraftOrder.query()
      .where('id', payload.draftOrderId)
      .preload('user')
      .preload('billingAddress')
      .preload('shippingAddress')
      .preload('details')
      .first()

    if (!draftOrder) {
      return response.badRequest({
        success: false,
        error: 'Draft order not found',
      })
    }

    if (draftOrder.status === 'completed') {
      return response.badRequest({
        success: false,
        error: 'Draft order already completed',
      })
    }

    if (payload.billingAddressId) {
      await draftOrder.merge({ billingAddressId: payload.billingAddressId }).save()
    }

    const paymentResult = (await this.authorizeNetService.processPayment({
      amount: draftOrder.totalPrice,
      cardNumber: payload.cardNumber,
      expirationDate: payload.expirationDate,
      cardCode: payload.cardCode,
      firstName: draftOrder.billingAddress?.firstName || '',
      lastName: draftOrder.billingAddress?.lastName || '',
      address: draftOrder.billingAddress?.address1 || '',
      city: draftOrder.billingAddress?.city || '',
      state: draftOrder.billingAddress?.province || '',
      zip: draftOrder.billingAddress?.zip || '',
      country: draftOrder.billingAddress?.country || '',
      email: draftOrder.user.email || '',
      code: draftOrder.code,
      orderId: draftOrder.id,
      details: draftOrder.details,
      tax: draftOrder.totalTax,
      shipping: draftOrder.totalShipping,
      discount: draftOrder.totalDiscount,
    })) as any

    if (paymentResult.success) {
      // Save transaction response
      const transactionResponse = await ZnTransactionResponse.create({
        response: JSON.stringify(paymentResult),
      })

      // Save transaction record
      const transaction = await ZnTransaction.create({
        draftOrderId: draftOrder.id,
        status: TransactionStatus.COMPLETED,
        amount: draftOrder.totalPrice,
        currency: 'USD',
        source: TransactionSource.AUTHORIZE_NET,
        sourceId: paymentResult.transactionId,
        paymentId: paymentResult.transactionId,
        accountNumber: paymentResult.accountNumber,
        accountType: paymentResult.accountType,
        paymentStatus: TransactionStatus.CAPTURED,
        sourceCreatedAt: new Date().toISOString(),
        sourceUpdatedAt: new Date().toISOString(),
        payerId: '',
        payerName: [draftOrder.billingAddress?.firstName, draftOrder.billingAddress?.lastName]
          .filter(Boolean)
          .join(''),
        payerEmail: draftOrder.user.email || '',
        responseId: transactionResponse.id.toString(),
      })

      // Handle successful payment for draft order
      const order = await this.paymentHandleService.paymentSuccess(draftOrder.id)

      // Update orderId in transaction
      await transaction.merge({ orderId: order.id }).save()

      // Delete Cart
      await ZnCartSection.query().whereIn('id', draftOrder.cartSectionIds).delete()

      return response.ok({
        success: true,
        data: {
          transaction,
          order,
        },
        message: 'Payment processed successfully',
      })
    } else {
      return response.badRequest({
        success: false,
        error: 'Process Payment Failed: ' + paymentResult.error,
      })
    }
  }
}
