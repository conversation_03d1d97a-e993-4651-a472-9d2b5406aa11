import ZnDraftOrder from '#models/zn_draft_order'
import { DraftOrderService } from '#services/shop/draft_order_service'
import {
  CreateDraftOrderValidator,
  DeleteOrderDetailValidator,
  UpdateAddressDraftOrderValidator,
  UpdateDiscountDraftOrderValidator,
  UpdateNoteDraftOrderValidator,
} from '#validators/app/draft-order/draft_order_validator'
import { HttpContext } from '@adonisjs/core/http'

export default class DraftOrderController {
  async createDraftOrder({ request, response, user }: HttpContext) {
    if (!user) {
      return response.unauthorized()
    }
    const payload = await request.validateUsing(CreateDraftOrderValidator)

    try {
      const draftOrderService = new DraftOrderService()
      const result = await draftOrderService.createDraftOrder(user, payload)

      const draftOrder = await ZnDraftOrder.query()
        .where('id', result.id)
        .preload('details')
        .preload('discounts')
        .preload('shippingAddress')
        .first()

      return response.created(draftOrder)
    } catch (error) {
      return response.badRequest(error.message)
    }
  }

  async updateDiscountCodes({ params, request, response, user }: HttpContext) {
    if (!user) {
      return response.unauthorized()
    }

    const { discountCodes } = await request.validateUsing(UpdateDiscountDraftOrderValidator)

    try {
      const draftOrderService = new DraftOrderService()
      const order = await draftOrderService.getDraftOrderById(params.id)
      if (!order) {
        return response.notFound('Order not found')
      }

      await draftOrderService.updateDraftOrderDiscountCodes(user, order, discountCodes)

      return response.ok(order)
    } catch (error) {
      return response.badRequest(error.message)
    }
  }

  async show({ params, response, user }: HttpContext) {
    if (!user) {
      return response.unauthorized()
    }

    const order = await ZnDraftOrder.query()
      .where('id', params.id)
      .preload('details')
      .preload('discounts')
      .preload('shippingAddress')
      .preload('order')
      .preload('transaction')
      .first()
    if (!order) {
      return response.notFound('Order not found')
    }

    return response.ok(order)
  }

  async updateAddress({ params, request, response, user }: HttpContext) {
    if (!user) {
      return response.unauthorized()
    }

    const { shippingAddressId } = await request.validateUsing(UpdateAddressDraftOrderValidator)

    const draftOrderService = new DraftOrderService()
    const order = await draftOrderService.getDraftOrderById(params.id)
    if (!order) {
      return response.notFound('Order not found')
    }

    try {
      await draftOrderService.updateDraftOrderAddress(user, order, shippingAddressId)

      return response.ok(order)
    } catch (error) {
      return response.badRequest(error.message)
    }
  }

  async updateNote({ params, request, response, user }: HttpContext) {
    if (!user) {
      return response.unauthorized()
    }
    const { note } = await request.validateUsing(UpdateNoteDraftOrderValidator)

    const draftOrderService = new DraftOrderService()
    const order = await draftOrderService.getDraftOrderById(params.id)
    if (!order) {
      return response.notFound('Order not found')
    }

    await draftOrderService.updateDraftOrderNote(order, note)

    return response.ok(order)
  }

  async deleteOrderDetail({ params, request, response, user }: HttpContext) {
    if (!user) {
      return response.unauthorized()
    }
    const { removeItemId } = await request.validateUsing(DeleteOrderDetailValidator)

    const draftOrderService = new DraftOrderService()
    const order = await draftOrderService.getDraftOrderById(params.id)
    if (!order) {
      return response.notFound('Order not found')
    }

    try {
      await draftOrderService.updateDraftOrderRemoveItems(user, order, removeItemId)

      return response.ok(order)
    } catch (error) {
      return response.badRequest(error.message)
    }
  }
}
