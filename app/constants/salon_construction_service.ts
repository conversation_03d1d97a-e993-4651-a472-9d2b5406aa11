export enum ESalonConstructionServiceStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

export enum EServiceInterest {
  FULL_RENOVATION = 'full_renovation',
  PARTIAL_REMODEL = 'partial_remodel',
  NEW_CONSTRUCTION = 'new_construction',
  ELECTRICAL_PLUMBING = 'electrical_plumbing',
  INTERIOR_DESIGN = 'interior_design'
}

export enum EBudgetRange {
  RANGE_30_50K = '$30-50K',
  RANGE_50_100K = '$50-100K',
  RANGE_100_250K = '$100-250K',
  RANGE_250_500K = '$250-500K',
  RANGE_500K_PLUS = '$500K+'
}

export const SALON_CONSTRUCTION_SERVICE_TEXT = {
  SIGNUP_SUCCESS: 'Salon construction service signup submitted successfully',
  SIGNUP_ERROR: 'Failed to submit salon construction service signup',
  PDF_GENERATION_ERROR: 'Failed to generate PDF',
  APPROVAL_SUCCESS: 'Salon construction service signup approved successfully',
  REJECTION_SUCCESS: 'Salon construction service signup rejected successfully',
  NOT_FOUND: 'Salon construction service signup not found',
  UNAUTHORIZED: 'Unauthorized access',
  VALIDATION_ERROR: 'Validation failed'
}

export const SERVICE_INTEREST_LABELS = {
  [EServiceInterest.FULL_RENOVATION]: 'Full Renovation',
  [EServiceInterest.PARTIAL_REMODEL]: 'Partial Remodel (e.g. chairs, counters, flooring)',
  [EServiceInterest.NEW_CONSTRUCTION]: 'New Construction',
  [EServiceInterest.ELECTRICAL_PLUMBING]: 'Electrical / Plumbing Updates',
  [EServiceInterest.INTERIOR_DESIGN]: 'Interior Design Services'
}

export const BUDGET_RANGE_LABELS = {
  [EBudgetRange.RANGE_30_50K]: '$30-50K',
  [EBudgetRange.RANGE_50_100K]: '$50-100K',
  [EBudgetRange.RANGE_100_250K]: '$100-250K',
  [EBudgetRange.RANGE_250_500K]: '$250-500K',
  [EBudgetRange.RANGE_500K_PLUS]: '$500K+'
}
