import { OrderService } from '#services/shop/order_service'
import { Job } from '@rlanz/bull-queue'

interface SyncOrderJobPayload {
  orderId: string
}

export default class SyncOrderJob extends Job {
  private orderSerivce = new OrderService()

  // This is the path to the file that is used to create the job
  static get $$filepath() {
    return import.meta.url
  }

  /**
   * Base Entry point
   */
  async handle(payload: SyncOrderJobPayload) {
    await this.orderSerivce.syncOrder(payload.orderId)
  }

  /**
   * This is an optional method that gets called when the retries has exceeded and is marked failed.
   */
  async rescue(_: SyncOrderJobPayload) { }
}
