import { PaymentHandleService } from '#services/payment/payment_handle_service'
import { Job } from '@rlanz/bull-queue'

interface OrderRefundJobPayload {
  orderId: string
}

export default class OrderRefundJob extends Job {
  // This is the path to the file that is used to create the job
  static get $$filepath() {
    return import.meta.url
  }

  /**
   * Base Entry point
   */
  async handle(payload: OrderRefundJobPayload) {
    try {
      const paymentHandleService = new PaymentHandleService()
      await paymentHandleService.refundPayment(payload.orderId)
    } catch (error) {
      console.error(error)
    }
  }

  /**
   * This is an optional method that gets called when the retries has exceeded and is marked failed.
   */
  async rescue(_: OrderRefundJobPayload) {}
}
