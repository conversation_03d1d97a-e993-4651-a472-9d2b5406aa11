// start/jobs/ExtractMsdsLinksJob.ts
import { Job } from '@rlanz/bull-queue'
import axios from 'axios'
import { promises as fs } from 'fs'
import path from 'path'

interface Payload {
  pageUrl:     string
  outputPath:  string
}

export default class ExtractMsdsLinksJob extends Job {
  public static get $$filepath() {
    return import.meta.url
  }

  public async handle({ pageUrl, outputPath }: Payload) {
    this.logger.info(`→ Fetching ${pageUrl}`)
    const { data: html } = await axios.get<string>(pageUrl)

    const linkRegex = /<a\b[^>]*href=["']([^"']+)["'][^>]*>([\s\S]*?MSDS[\s\S]*?)<\/a>/gi
    const entries: Array<{ label: string; url: string }> = []
    let match: RegExpExecArray | null

    while ((match = linkRegex.exec(html))) {
      let href = match[1]
      if (!href.match(/^https?:\/\//)) {
        href = new URL(href, pageUrl).toString()
      }

      const rawLabel = match[2]
      const label = rawLabel
        .replace(/<[^>]+>/g, ' ')
        .replace(/\s+/g, ' ')
        .trim()

      entries.push({ label, url: href })
    }

    if (entries.length === 0) {
      this.logger.warn('No MSDS links found.')
      return
    }

    // Write output
    await fs.mkdir(path.dirname(outputPath), { recursive: true })
    const lines = entries.map(e => `${e.label} – ${e.url}`)
    await fs.writeFile(outputPath, lines.join('\n'), 'utf-8')
    this.logger.info(`✓ Saved ${entries.length} MSDS link(s) to ${outputPath}`)
  }


  async rescue() {}


}
