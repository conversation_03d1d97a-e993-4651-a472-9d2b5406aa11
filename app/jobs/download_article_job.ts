// start/jobs/DownloadArticlesJob.ts
import fs from 'fs'
import path from 'path'
import axios from 'axios'
import { Job } from '@rlanz/bull-queue'

interface Payload {
  handle: string       // article slug
  blogHandle: string   // e.g. "discover"
  outputDir: string    // e.g. "html"
}


export default class DownloadArticleJob extends Job {
  public static get $$filepath(): string {
    return import.meta.url
  }

  public async handle({ handle, blogHandle, outputDir }: Payload) {
    const url = `https://zurno.com/blogs/${blogHandle}/${handle}`
    this.logger.info(`→ Fetching ${url}`)

    const { data: html } = await axios.get<string>(url)

    const sectionRegex = new RegExp(
      '<div[^>]+class=[\'"][^\'"]*article-template__content[^\'"]*[\'"][^>]*>' +
      '([\\s\\S]*?)' +
      '<div[^>]+class=[\'"][^\'"]*article-template__social-sharing[^\'"]*[\'"]',
      'i'
    )
    const sectionMatch = sectionRegex.exec(html)
    if (!sectionMatch) {
      this.logger.warn(`• could not locate main content for "${handle}"`)
      return
    }
    const sectionHtml = sectionMatch[1]

    const paragraphRegex = /<p\b[^>]*>([\s\S]*?)<\/p>/gi
    const paragraphs: string[] = []
    let match: RegExpExecArray | null
    while ((match = paragraphRegex.exec(sectionHtml))) {
      let inner = match[1]
      inner = inner
        .replace(/<[^>]+>/g, ' ')
        .replace(/\s+/g, ' ')
        .trim()
      if (inner) {
        paragraphs.push(inner)
      }
    }

    const tableRe = /<table\b[^>]*>([\s\S]*?)<\/table>/gi
    let tMatch: RegExpExecArray | null

    const allRows: string[][] = []

    while ((tMatch = tableRe.exec(sectionHtml))) {
      const tableHtml = tMatch[1]

      const rowRe = /<tr\b[^>]*>([\s\S]*?)<\/tr>/gi
      let rMatch: RegExpExecArray | null

      while ((rMatch = rowRe.exec(tableHtml))) {
        const rowHtml = rMatch[1]
        const cells: string[] = []

        const cellRe = /<(td|th)\b[^>]*>([\s\S]*?)<\/\1>/gi
        let cMatch: RegExpExecArray | null

        while ((cMatch = cellRe.exec(rowHtml))) {
          const cellText = cMatch[2]
            .replace(/<[^>]+>/g, ' ')     // strip any nested tags
            .replace(/\s+/g, ' ')         // collapse whitespace
            .trim()
          if (cellText) {
            cells.push(cellText)
          }
        }

        if (cells.length) {
          allRows.push(cells)
        }
      }
    }

    const mergedTable = allRows
      .map(row => row.join('\t'))
      .join('\n')

    if (paragraphs.length === 0) {
      this.logger.warn(`• no <p> tags found for "${handle}"`)
      return
    }

    const out = [
      ...paragraphs,
      '',
      mergedTable
    ].join('\n\n')

    await fs.promises.writeFile(
      path.join(outputDir, `${handle}.txt`),
      out,
      'utf-8'
    )

    this.logger.info(`✓ Saved ${paragraphs.length} paragraphs and ${allRows.length} table rows to ${handle}.txt`)
  }

  async rescue() {}

}
