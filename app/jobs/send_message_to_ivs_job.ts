import {Job} from "@rlanz/bull-queue";
import {IvschatService} from "../services/aws/ivschat_service.js";
import {ChatTokenCapability} from "@aws-sdk/client-ivschat";

interface SendMessageToIvsJobPayload {
  roomArn: string,
  senderId: string,
  content: string
  attributes?: any
}

export default class SendMessageToIvsJob extends Job {
  private ivsChatService = new IvschatService()

  static get $$filepath() {
    return import.meta.url
  }

  async handle({roomArn, senderId, content, attributes}: SendMessageToIvsJobPayload) {
    const chatToken = await this.ivsChatService.createChatToken({
      roomArn: roomArn,
      userId: senderId,
      capabilities: [ChatTokenCapability.SEND_MESSAGE],
      attributes : attributes
    })

    await this.ivsChatService.sendChatMessage(roomArn,  chatToken,  content)
  }

  async rescue() {}

}
