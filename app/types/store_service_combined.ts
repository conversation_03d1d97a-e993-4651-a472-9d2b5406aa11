export interface PaginationMeta {
  total: number
  perPage: number
  currentPage: number
  lastPage: number
  firstPage: number
  firstPageUrl: string
  lastPageUrl: string
  nextPageUrl: string | null
  previousPageUrl: string | null
}

export interface StoreService {
  id: string
  name: string
  price: number
  duration: number
  storeId: string
  imageId?: string
  image?: {
    id: string
    url: string
    filename: string
  }
  createdAt: string
  updatedAt: string
}

export interface StorePackage {
  id: string
  name: string
  storeId: string
  imageId?: string
  image?: {
    id: string
    url: string
    filename: string
  }
  services: StoreService[]
  createdAt: string
  updatedAt: string
  isPackage: true
}

export interface StoreServiceCategory {
  id: string
  name: string
  storeId: string
  services: StoreService[]
  createdAt: string
  updatedAt: string
  isPackage: false
}

export type CombinedItem = StorePackage | StoreServiceCategory

export interface PaginatedResponse<T> {
  data: T[]
  meta: PaginationMeta
}

export interface StoreServiceCombinedResponse {
  data: CombinedItem[]
  meta: PaginationMeta
}

export interface StoreServiceCombinedQueryParams {
  storeId: string
  page?: number
  limit?: number
  search?: string
  categories?: string[]
  priceFrom?: number
  priceTo?: number
  durationFrom?: number
  durationTo?: number
}
