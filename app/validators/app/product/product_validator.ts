import vine from '@vinejs/vine'

export const restockNotificationValidator = vine.compile(
  vine.object({
    shopifyVariantId: vine.string(),
    active: vine.boolean(),
  })
)

export const productShowValidator = vine.compile(
  vine.object({
    shopifyProductId: vine.string(),
  })
)

export const createProductValidator = vine.compile(
  vine.object({
    id: vine.string().uuid().optional(),

    title: vine.string(),
    description: vine.string().optional(),
    status: vine.enum(['draft', 'active']).optional(),

    vendor: vine.object({
      companyName: vine.string()
    }).optional(),

    productType: vine.object({
      name: vine.string()
    }).optional(),

    tags: vine.array(
      vine.object({
        name: vine.string()
      })
    ).optional(),

    category: vine.object({
      shopifyId: vine.string()
    }).optional(),

    images: vine.array(
      vine.object({
        src: vine.string(),
        position: vine.number(),
      })
    ).optional(),

    options: vine.array(
      vine.object({
        name: vine.string(),
        position: vine.number().optional(),
        productOptionValues: vine.array(
          vine.object({
            value: vine.string()
          })
        ).optional()
      })
    ).optional(),

    variants: vine.array(
      vine.object({
        sku: vine.string().optional(),
        barcode: vine.string().optional(),
        inventoryQuantity: vine.number().optional(),
        inventoryPolicy: vine.enum(['continue', 'deny']).optional(),

        price: vine.number().optional(),
        compareAtPrice: vine.number().optional(),

        weight: vine.number().optional(),
        weightUnit: vine.string().optional(),

        optionValues: vine.array(
          vine.object({
            value: vine.string(),
            option: vine.object({
              name: vine.string(),
            })
          })
        ),

        image: vine.object({
          src: vine.string().url(),
        }).optional(),
      })
    ),
  })
)
