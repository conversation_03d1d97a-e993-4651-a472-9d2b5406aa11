import vine from '@vinejs/vine'

export const authorizeNetPaymentValidator = vine.compile(
  vine.object({
    draftOrderId: vine.string(),
    cardNumber: vine.string().creditCard(),
    expirationDate: vine.string().regex(/^(0[1-9]|1[0-2])\/([0-9]{2})$/),
    cardCode: vine.string().minLength(3).maxLength(4),
    billingAddressId: vine.string().optional(),
  })
)

export const authorizeNetRefundValidator = vine.compile(
  vine.object({
    draftOrderId: vine.string(),
    transactionId: vine.string(),
    amount: vine.number().positive(),
  })
)
