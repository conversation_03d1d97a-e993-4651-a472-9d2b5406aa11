import vine from '@vinejs/vine'
import {
  EServiceInterest,
  EBudgetRange,
  ESalonConstructionServiceStatus,
} from '../../constants/salon_construction_service.js'

/**
 * Validator for creating salon construction service signup
 */
export const createSalonConstructionServiceSignupValidator = vine.compile(
  vine.object({
    fullName: vine.string().trim().minLength(2).maxLength(100),
    businessName: vine.string().trim().minLength(2).maxLength(100),
    salonAddress: vine.string().trim().minLength(10).maxLength(500),
    phoneNumber: vine
      .string()
      .trim()
      .regex(/^[\+]?[1-9][\d]{0,15}$/),
    emailAddress: vine.string().trim().email().normalizeEmail(),
    serviceInterest: vine.array(vine.enum(Object.values(EServiceInterest))).minLength(1),
    preferredStartDate: vine.date({ formats: { utc: true } }).optional(),
    budgetRange: vine.enum(Object.values(EBudgetRange)),
    additionalNotes: vine.string().trim().maxLength(1000).optional(),
    consentConfirmed: vine.boolean(),
    signature: vine.string().trim().minLength(2).maxLength(100).optional(),
    signatureDate: vine.date({ formats: { utc: true } }).optional(),
  })
)

/**
 * Validator for updating salon construction service signup status (admin)
 */
export const updateSalonConstructionServiceStatusValidator = vine.compile(
  vine.object({
    status: vine.enum(Object.values(ESalonConstructionServiceStatus)),
    rejectionReason: vine.string().trim().maxLength(500).optional(),
  })
)

/**
 * Validator for admin filtering/searching
 */
export const salonConstructionServiceFilterValidator = vine.compile(
  vine.object({
    page: vine.number().min(1).optional(),
    limit: vine.number().min(1).max(100).optional(),
    status: vine.enum(Object.values(ESalonConstructionServiceStatus)).optional(),
    search: vine.string().trim().maxLength(100).optional(),
    startDate: vine.date().optional(),
    endDate: vine.date().optional(),
    budgetRange: vine.enum(Object.values(EBudgetRange)).optional(),
  })
)

/**
 * Validator for getting single signup by ID
 */
export const getSalonConstructionServiceSignupValidator = vine.compile(
  vine.object({
    id: vine.string().uuid(),
  })
)
